{% extends 'base.html' %}
{% load crispy_forms_tags %}

{% block title %}Register Patient - HospiTrack{% endblock %}

{% block content %}
<div class="max-w-4xl mx-auto space-y-6">
    <!-- Header -->
    <div class="bg-white shadow rounded-lg p-6">
        <div class="flex items-center justify-between">
            <div>
                <h1 class="text-3xl font-bold text-gray-900">Register New Patient</h1>
                <p class="text-gray-600 mt-2">Enter patient information to create a new record</p>
            </div>
            <a href="{% url 'patients:list' %}" class="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
                <i class="fas fa-arrow-left mr-2"></i>
                Back to Patients
            </a>
        </div>
    </div>

    <!-- Registration Form -->
    <div class="bg-white shadow rounded-lg p-6">
        <form method="post" class="space-y-6">
            {% csrf_token %}
            
            <!-- Personal Information -->
            <div>
                <h3 class="text-lg font-medium text-gray-900 mb-4">Personal Information</h3>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                        <label for="{{ form.patient_id.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-2">
                            Patient ID <span class="text-red-500">*</span>
                        </label>
                        <input type="text" name="patient_id" id="{{ form.patient_id.id_for_label }}" required class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500">
                        {% if form.patient_id.errors %}
                            <p class="mt-1 text-sm text-red-600">{{ form.patient_id.errors.0 }}</p>
                        {% endif %}
                    </div>
                    <div></div>
                    
                    <div>
                        <label for="{{ form.first_name.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-2">
                            First Name <span class="text-red-500">*</span>
                        </label>
                        <input type="text" name="first_name" id="{{ form.first_name.id_for_label }}" required class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500">
                        {% if form.first_name.errors %}
                            <p class="mt-1 text-sm text-red-600">{{ form.first_name.errors.0 }}</p>
                        {% endif %}
                    </div>

                    <div>
                        <label for="{{ form.last_name.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-2">
                            Last Name <span class="text-red-500">*</span>
                        </label>
                        <input type="text" name="last_name" id="{{ form.last_name.id_for_label }}" required class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500">
                        {% if form.last_name.errors %}
                            <p class="mt-1 text-sm text-red-600">{{ form.last_name.errors.0 }}</p>
                        {% endif %}
                    </div>
                    
                    <div>
                        <label for="{{ form.date_of_birth.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-2">
                            Date of Birth <span class="text-red-500">*</span>
                        </label>
                        <input type="date" name="date_of_birth" id="{{ form.date_of_birth.id_for_label }}" required class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500">
                        {% if form.date_of_birth.errors %}
                            <p class="mt-1 text-sm text-red-600">{{ form.date_of_birth.errors.0 }}</p>
                        {% endif %}
                    </div>

                    <div>
                        <label for="{{ form.gender.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-2">
                            Gender <span class="text-red-500">*</span>
                        </label>
                        <select name="gender" id="{{ form.gender.id_for_label }}" required class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500 bg-white">
                            <option value="">Select Gender</option>
                            <option value="M">Male</option>
                            <option value="F">Female</option>
                            <option value="O">Other</option>
                        </select>
                        {% if form.gender.errors %}
                            <p class="mt-1 text-sm text-red-600">{{ form.gender.errors.0 }}</p>
                        {% endif %}
                    </div>

                    <div>
                        <label for="{{ form.blood_group.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-2">
                            Blood Group <span class="text-red-500">*</span>
                        </label>
                        <select name="blood_group" id="{{ form.blood_group.id_for_label }}" required class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500 bg-white">
                            <option value="">Select Blood Group</option>
                            <option value="A+">A+</option>
                            <option value="A-">A-</option>
                            <option value="B+">B+</option>
                            <option value="B-">B-</option>
                            <option value="AB+">AB+</option>
                            <option value="AB-">AB-</option>
                            <option value="O+">O+</option>
                            <option value="O-">O-</option>
                        </select>
                        {% if form.blood_group.errors %}
                            <p class="mt-1 text-sm text-red-600">{{ form.blood_group.errors.0 }}</p>
                        {% endif %}
                    </div>
                </div>
            </div>

            <!-- Contact Information -->
            <div>
                <h3 class="text-lg font-medium text-gray-900 mb-4">Contact Information</h3>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                        <label for="{{ form.phone.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-2">
                            Phone Number <span class="text-red-500">*</span>
                        </label>
                        <input type="tel" name="phone" id="{{ form.phone.id_for_label }}" required class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500">
                        {% if form.phone.errors %}
                            <p class="mt-1 text-sm text-red-600">{{ form.phone.errors.0 }}</p>
                        {% endif %}
                    </div>

                    <div>
                        <label for="{{ form.email.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-2">
                            Email Address
                        </label>
                        <input type="email" name="email" id="{{ form.email.id_for_label }}" class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500">
                        {% if form.email.errors %}
                            <p class="mt-1 text-sm text-red-600">{{ form.email.errors.0 }}</p>
                        {% endif %}
                    </div>
                </div>
                
                <div class="mt-6">
                    <label for="{{ form.address.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-2">
                        Address <span class="text-red-500">*</span>
                    </label>
                    <textarea name="address" id="{{ form.address.id_for_label }}" rows="3" required class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500 resize-vertical"></textarea>
                    {% if form.address.errors %}
                        <p class="mt-1 text-sm text-red-600">{{ form.address.errors.0 }}</p>
                    {% endif %}
                </div>
            </div>

            <!-- Emergency Contact -->
            <div>
                <h3 class="text-lg font-medium text-gray-900 mb-4">Emergency Contact</h3>
                <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                    <div>
                        <label for="{{ form.emergency_contact_name.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-2">
                            Contact Name <span class="text-red-500">*</span>
                        </label>
                        <input type="text" name="emergency_contact_name" id="{{ form.emergency_contact_name.id_for_label }}" required class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500">
                        {% if form.emergency_contact_name.errors %}
                            <p class="mt-1 text-sm text-red-600">{{ form.emergency_contact_name.errors.0 }}</p>
                        {% endif %}
                    </div>

                    <div>
                        <label for="{{ form.emergency_contact_phone.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-2">
                            Contact Phone <span class="text-red-500">*</span>
                        </label>
                        <input type="tel" name="emergency_contact_phone" id="{{ form.emergency_contact_phone.id_for_label }}" required class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500">
                        {% if form.emergency_contact_phone.errors %}
                            <p class="mt-1 text-sm text-red-600">{{ form.emergency_contact_phone.errors.0 }}</p>
                        {% endif %}
                    </div>

                    <div>
                        <label for="{{ form.emergency_contact_relation.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-2">
                            Relationship <span class="text-red-500">*</span>
                        </label>
                        <input type="text" name="emergency_contact_relation" id="{{ form.emergency_contact_relation.id_for_label }}" required class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500">
                        {% if form.emergency_contact_relation.errors %}
                            <p class="mt-1 text-sm text-red-600">{{ form.emergency_contact_relation.errors.0 }}</p>
                        {% endif %}
                    </div>
                </div>
            </div>

            <!-- Medical Information -->
            <div>
                <h3 class="text-lg font-medium text-gray-900 mb-4">Medical Information</h3>
                <div class="space-y-6">
                    <div>
                        <label for="{{ form.medical_history.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-2">
                            Medical History
                        </label>
                        <textarea name="medical_history" id="{{ form.medical_history.id_for_label }}" rows="3" class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500 resize-vertical"></textarea>
                        {% if form.medical_history.errors %}
                            <p class="mt-1 text-sm text-red-600">{{ form.medical_history.errors.0 }}</p>
                        {% endif %}
                    </div>

                    <div>
                        <label for="{{ form.allergies.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-2">
                            Allergies
                        </label>
                        <textarea name="allergies" id="{{ form.allergies.id_for_label }}" rows="2" class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500 resize-vertical"></textarea>
                        {% if form.allergies.errors %}
                            <p class="mt-1 text-sm text-red-600">{{ form.allergies.errors.0 }}</p>
                        {% endif %}
                    </div>

                    <div>
                        <label for="{{ form.current_medications.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-2">
                            Current Medications
                        </label>
                        <textarea name="current_medications" id="{{ form.current_medications.id_for_label }}" rows="2" class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500 resize-vertical"></textarea>
                        {% if form.current_medications.errors %}
                            <p class="mt-1 text-sm text-red-600">{{ form.current_medications.errors.0 }}</p>
                        {% endif %}
                    </div>

                    <div>
                        <label for="{{ form.insurance_number.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-2">
                            Insurance Number
                        </label>
                        <input type="text" name="insurance_number" id="{{ form.insurance_number.id_for_label }}" class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500">
                        {% if form.insurance_number.errors %}
                            <p class="mt-1 text-sm text-red-600">{{ form.insurance_number.errors.0 }}</p>
                        {% endif %}
                    </div>
                </div>
            </div>

            <!-- Form Actions -->
            <div class="flex justify-end space-x-4 pt-6 border-t border-gray-200">
                <a href="{% url 'patients:list' %}" class="px-6 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
                    Cancel
                </a>
                <button type="submit" class="px-6 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700">
                    <i class="fas fa-save mr-2"></i>
                    Register Patient
                </button>
            </div>
        </form>
    </div>
</div>


{% endblock %}
