from django.urls import path
from . import views

urlpatterns = [
    path('', views.landing_page, name='landing'),
    path('login/', views.login_view, name='login'),
    path('logout/', views.logout_view, name='logout'),
    path('dashboard/', views.dashboard, name='dashboard'),
    path('admin-dashboard/', views.admin_dashboard, name='admin_dashboard'),
    path('doctors/', views.doctors_list, name='doctors_list'),
    path('doctors/create/', views.doctor_create, name='doctor_create'),
    path('doctors/<int:doctor_id>/', views.doctor_detail, name='doctor_detail'),
    path('doctors/<int:doctor_id>/edit/', views.doctor_edit, name='doctor_edit'),
    path('doctor-dashboard/', views.doctor_dashboard, name='doctor_dashboard'),
    path('nurses/', views.nurses_list, name='nurses_list'),
    path('nurses/create/', views.nurse_create, name='nurse_create'),
    path('nurses/<int:nurse_id>/', views.nurse_detail, name='nurse_detail'),
    path('nurses/<int:nurse_id>/edit/', views.nurse_edit, name='nurse_edit'),
    path('nurses/leave/', views.nurse_leave_management, name='nurse_leave_management'),
    path('nurses/leave/<int:nurse_id>/', views.nurse_leave_management, name='nurse_leave_edit'),
    path('specialty-team/', views.specialty_team_view, name='specialty_team'),
    path('nurse-dashboard/', views.nurse_dashboard, name='nurse_dashboard'),
]
