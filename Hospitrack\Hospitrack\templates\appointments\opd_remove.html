{% extends 'base.html' %}

{% block title %}Remove OPD Registration - HospiTrack{% endblock %}

{% block content %}
<div class="space-y-6">
    <!-- Header -->
    <div class="bg-white shadow rounded-lg p-6">
        <div class="flex justify-between items-center">
            <div>
                <h1 class="text-3xl font-bold text-gray-900">Remove OPD Registration</h1>
                <p class="text-gray-600 mt-2">{{ registration.opd_number }} - {{ registration.patient_name }}</p>
            </div>
            <div class="flex space-x-3">
                <a href="{% url 'appointments:opd_schedule' %}" class="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
                    <i class="fas fa-arrow-left mr-2"></i>
                    Back to OPD Management
                </a>
            </div>
        </div>
    </div>

    <!-- Confirmation Card -->
    <div class="bg-white shadow rounded-lg p-6">
        <div class="flex items-center mb-6">
            <div class="flex-shrink-0">
                <i class="fas fa-trash-alt text-4xl text-red-600"></i>
            </div>
            <div class="ml-4">
                <h2 class="text-xl font-bold text-gray-900">Permanently Remove Registration</h2>
                <p class="text-gray-600">Are you sure you want to completely remove this OPD registration?</p>
            </div>
        </div>

        <!-- Patient Information -->
        <div class="bg-gray-50 rounded-lg p-4 mb-6">
            <h3 class="text-lg font-medium text-gray-900 mb-3">Registration Details</h3>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                    <span class="text-sm font-medium text-gray-500">OPD Number:</span>
                    <p class="text-sm text-gray-900 font-mono">{{ registration.opd_number }}</p>
                </div>
                <div>
                    <span class="text-sm font-medium text-gray-500">Patient Name:</span>
                    <p class="text-sm text-gray-900">{{ registration.patient_name }}</p>
                </div>
                <div>
                    <span class="text-sm font-medium text-gray-500">Doctor:</span>
                    <p class="text-sm text-gray-900">Dr. {{ registration.doctor.user.get_full_name }}</p>
                </div>
                <div>
                    <span class="text-sm font-medium text-gray-500">Specialization:</span>
                    <p class="text-sm text-gray-900">{{ registration.doctor.specialization }}</p>
                </div>
                <div>
                    <span class="text-sm font-medium text-gray-500">Appointment Time:</span>
                    <p class="text-sm text-gray-900">{{ registration.get_preferred_time_display }}</p>
                </div>
                <div>
                    <span class="text-sm font-medium text-gray-500">Registration Date:</span>
                    <p class="text-sm text-gray-900">{{ registration.registration_date|date:"F d, Y" }}</p>
                </div>
            </div>
        </div>

        <!-- Critical Warning Message -->
        <div class="bg-red-50 border border-red-200 rounded-lg p-4 mb-6">
            <div class="flex">
                <div class="flex-shrink-0">
                    <i class="fas fa-exclamation-triangle text-red-500"></i>
                </div>
                <div class="ml-3">
                    <h3 class="text-sm font-medium text-red-800">⚠️ CRITICAL WARNING</h3>
                    <div class="mt-2 text-sm text-red-700">
                        <ul class="list-disc list-inside space-y-1">
                            <li><strong>This will PERMANENTLY DELETE the OPD registration</strong></li>
                            <li>The registration will be completely removed from the system</li>
                            <li>This action CANNOT be undone</li>
                            <li>All appointment history for this registration will be lost</li>
                            <li>The patient record itself will NOT be deleted (only this OPD registration)</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>

        <!-- Important Clarification -->
        <div class="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-6">
            <div class="flex">
                <div class="flex-shrink-0">
                    <i class="fas fa-info-circle text-blue-500"></i>
                </div>
                <div class="ml-3">
                    <h3 class="text-sm font-medium text-blue-800">📋 What Gets Removed vs What Stays</h3>
                    <div class="mt-2 text-sm text-blue-700">
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <div>
                                <h4 class="font-medium text-red-700">❌ Will Be Removed:</h4>
                                <ul class="list-disc list-inside space-y-1 mt-1">
                                    <li>This specific OPD registration</li>
                                    <li>Today's appointment slot</li>
                                    <li>Queue position</li>
                                    <li>Registration history</li>
                                </ul>
                            </div>
                            <div>
                                <h4 class="font-medium text-green-700">✅ Will Stay Safe:</h4>
                                <ul class="list-disc list-inside space-y-1 mt-1">
                                    <li>Patient's personal information</li>
                                    <li>Doctor's profile and data</li>
                                    <li>Other patient registrations</li>
                                    <li>Hospital system data</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Alternative Actions -->
        <div class="bg-yellow-50 border border-yellow-200 rounded-lg p-4 mb-6">
            <div class="flex">
                <div class="flex-shrink-0">
                    <i class="fas fa-lightbulb text-yellow-500"></i>
                </div>
                <div class="ml-3">
                    <h3 class="text-sm font-medium text-yellow-800">💡 Consider These Alternatives</h3>
                    <div class="mt-2 text-sm text-yellow-700">
                        <p>Instead of permanently removing, you might want to:</p>
                        <ul class="list-disc list-inside space-y-1 mt-1">
                            <li><a href="{% url 'appointments:opd_cancel' registration.id %}" class="underline hover:text-yellow-900 font-medium">Cancel the registration</a> (keeps record but marks as cancelled)</li>
                            <li><a href="{% url 'appointments:opd_edit' registration.id %}" class="underline hover:text-yellow-900 font-medium">Edit the registration details</a> (modify instead of remove)</li>
                            <li>Reschedule for another day</li>
                            <li>Change the appointment status</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>

        <!-- Action Buttons -->
        <div class="flex justify-end space-x-3">
            <a href="{% url 'appointments:opd_schedule' %}" class="px-6 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
                <i class="fas fa-times mr-2"></i>
                No, Keep Registration
            </a>
            <a href="{% url 'appointments:opd_cancel' registration.id %}" class="px-6 py-2 border border-yellow-300 text-sm font-medium rounded-md text-yellow-700 bg-yellow-50 hover:bg-yellow-100">
                <i class="fas fa-ban mr-2"></i>
                Cancel Instead (Safer)
            </a>
            <form method="post" class="inline">
                {% csrf_token %}
                <button type="submit" class="px-6 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-red-600 hover:bg-red-700" onclick="return confirm('Are you absolutely sure? This action cannot be undone!')">
                    <i class="fas fa-trash-alt mr-2"></i>
                    Yes, Permanently Remove
                </button>
            </form>
        </div>
    </div>
</div>
{% endblock %}
