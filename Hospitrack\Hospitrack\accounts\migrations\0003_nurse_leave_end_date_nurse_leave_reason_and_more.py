# Generated by Django 5.2.4 on 2025-07-27 04:41

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('accounts', '0002_medicalspecialty_alter_doctor_available_from_and_more'),
    ]

    operations = [
        migrations.AddField(
            model_name='nurse',
            name='leave_end_date',
            field=models.DateField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='nurse',
            name='leave_reason',
            field=models.TextField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='nurse',
            name='leave_start_date',
            field=models.DateField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='nurse',
            name='leave_status',
            field=models.CharField(choices=[('available', 'Available'), ('on_leave', 'On Leave'), ('sick_leave', 'Sick Leave'), ('vacation', 'Vacation'), ('emergency_leave', 'Emergency Leave')], default='available', max_length=20),
        ),
    ]
