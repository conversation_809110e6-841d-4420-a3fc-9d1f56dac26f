from django.core.management.base import BaseCommand
from django.contrib.auth import get_user_model
from accounts.models import Doctor

User = get_user_model()

class Command(BaseCommand):
    help = 'Check all doctors in the system'

    def handle(self, *args, **options):
        self.stdout.write('Checking doctors in the system...')
        
        # Check all users with doctor type
        doctor_users = User.objects.filter(user_type='doctor')
        self.stdout.write(f'Users with doctor type: {doctor_users.count()}')
        
        for user in doctor_users:
            self.stdout.write(f'- {user.username}: {user.get_full_name()}')
        
        # Check all doctor profiles
        doctor_profiles = Doctor.objects.all()
        self.stdout.write(f'\nDoctor profiles: {doctor_profiles.count()}')
        
        for doctor in doctor_profiles:
            self.stdout.write(f'- Dr. {doctor.user.get_full_name()}: {doctor.specialization}')
        
        # Check for users without doctor profiles
        users_without_profiles = doctor_users.exclude(id__in=doctor_profiles.values_list('user_id', flat=True))
        if users_without_profiles.exists():
            self.stdout.write(f'\nUsers without doctor profiles: {users_without_profiles.count()}')
            for user in users_without_profiles:
                self.stdout.write(f'- {user.username}: {user.get_full_name()}')
        
        self.stdout.write(f'\n📊 Summary:')
        self.stdout.write(f'Total doctor users: {doctor_users.count()}')
        self.stdout.write(f'Total doctor profiles: {doctor_profiles.count()}')
        self.stdout.write(f'Users without profiles: {users_without_profiles.count()}')
