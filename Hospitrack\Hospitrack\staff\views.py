from django.shortcuts import render, redirect, get_object_or_404
from django.contrib.auth.decorators import login_required
from django.contrib import messages
from .models import StaffMember, Department, Attendance

@login_required
def staff_list(request):
    """List all staff members (nurses only)"""
    staff_members = StaffMember.objects.filter(is_active=True, designation='nurse').order_by('employee_id')
    return render(request, 'staff/list.html', {'staff_members': staff_members})

@login_required
def staff_create(request):
    """Create new staff member"""
    return render(request, 'staff/create.html')

@login_required
def staff_detail(request, staff_id):
    """View staff member details"""
    staff_member = get_object_or_404(StaffMember, id=staff_id)
    return render(request, 'staff/detail.html', {'staff_member': staff_member})

@login_required
def staff_edit(request, staff_id):
    """Edit staff member (nurse)"""
    staff_member = get_object_or_404(StaffMember, id=staff_id)

    if request.method == 'POST':
        try:
            # Update user information
            user = staff_member.user
            user.first_name = request.POST.get('first_name')
            user.last_name = request.POST.get('last_name')
            user.email = request.POST.get('email')
            user.save()

            # Update staff member information
            staff_member.employee_id = request.POST.get('employee_id')
            staff_member.department_id = request.POST.get('department')
            staff_member.employment_type = request.POST.get('employment_type')
            staff_member.hire_date = request.POST.get('hire_date')
            staff_member.salary = request.POST.get('salary') or None
            staff_member.experience_years = request.POST.get('experience_years') or None
            staff_member.emergency_contact_name = request.POST.get('emergency_contact_name')
            staff_member.emergency_contact_phone = request.POST.get('emergency_contact_phone')
            staff_member.emergency_contact_relation = request.POST.get('emergency_contact_relation')
            staff_member.qualifications = request.POST.get('qualifications')
            staff_member.is_active = 'is_active' in request.POST
            staff_member.save()

            # Update nurse information
            if hasattr(user, 'nurse'):
                nurse = user.nurse
                nurse.shift = request.POST.get('shift')
                nurse.license_number = request.POST.get('license_number')
                nurse.save()

            messages.success(request, f'{user.get_full_name()} has been updated successfully.')
            return redirect('staff:detail', staff_id=staff_member.id)

        except Exception as e:
            messages.error(request, f'Error updating nurse: {str(e)}')

    # Get departments for the form
    departments = Department.objects.all()

    context = {
        'staff_member': staff_member,
        'departments': departments,
    }

    return render(request, 'staff/edit.html', context)

@login_required
def staff_schedule(request, staff_id):
    """View nurse work schedule"""
    staff_member = get_object_or_404(StaffMember, id=staff_id)

    # Generate a sample weekly schedule based on nurse's shift
    from datetime import datetime, timedelta
    import calendar

    today = datetime.now().date()
    # Get the start of current week (Monday)
    start_of_week = today - timedelta(days=today.weekday())

    # Generate 4 weeks of schedule
    weeks = []
    for week_num in range(4):
        week_start = start_of_week + timedelta(weeks=week_num)
        week_days = []

        for day_num in range(7):  # Monday to Sunday
            current_date = week_start + timedelta(days=day_num)
            day_name = calendar.day_name[current_date.weekday()]

            # Generate schedule based on nurse's shift
            if hasattr(staff_member.user, 'nurse'):
                shift = staff_member.user.nurse.shift
                if shift == 'morning':
                    shift_time = "6:00 AM - 2:00 PM"
                elif shift == 'evening':
                    shift_time = "2:00 PM - 10:00 PM"
                elif shift == 'night':
                    shift_time = "10:00 PM - 6:00 AM"
                else:
                    shift_time = "Not Assigned"
            else:
                shift_time = "Not Assigned"

            # Simulate some days off (weekends or random days)
            is_working = True
            if current_date.weekday() >= 5:  # Weekend
                if week_num % 2 == 0:  # Every other weekend off
                    is_working = False
                    shift_time = "Day Off"

            week_days.append({
                'date': current_date,
                'day_name': day_name,
                'shift_time': shift_time,
                'is_working': is_working,
                'is_today': current_date == today
            })

        weeks.append({
            'week_start': week_start,
            'week_end': week_start + timedelta(days=6),
            'days': week_days
        })

    context = {
        'staff_member': staff_member,
        'weeks': weeks,
        'current_date': today,
    }

    return render(request, 'staff/schedule.html', context)

@login_required
def department_list(request):
    """List all departments"""
    departments = Department.objects.filter(is_active=True)
    return render(request, 'staff/department_list.html', {'departments': departments})

@login_required
def attendance_view(request):
    """View attendance records"""
    attendance_records = Attendance.objects.all().order_by('-date')[:50]
    return render(request, 'staff/attendance.html', {'attendance_records': attendance_records})
