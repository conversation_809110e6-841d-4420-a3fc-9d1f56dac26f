from django.shortcuts import render, redirect, get_object_or_404
from django.contrib.auth.decorators import login_required
from django.contrib import messages
from django.http import JsonResponse
from django.db.models import Q
from .models import Patient, PatientUpdate, PatientDischarge
from .forms import <PERSON>ientForm, PatientUpdateForm, PatientDischargeForm

@login_required
def patient_list(request):
    """List patients based on user role and specialty"""
    if request.user.user_type == 'doctor':
        # Doctors can only see patients in their specialty
        try:
            from accounts.models import Doctor

            doctor = Doctor.objects.get(user=request.user)

            # Get patients in the same specialty as the doctor
            patients = Patient.objects.filter(
                primary_specialty=doctor.specialty,
                is_active=True
            ).order_by('-created_at')

        except Doctor.DoesNotExist:
            patients = Patient.objects.none()

    elif request.user.user_type == 'nurse':
        # Nurses can only see patients in their specialty
        try:
            from accounts.models import Nurse

            nurse = Nurse.objects.get(user=request.user)

            # Get patients in the same specialty as the nurse
            patients = Patient.objects.filter(
                primary_specialty=nurse.specialty,
                is_active=True
            ).order_by('-created_at')

        except Nurse.DoesNotExist:
            patients = Patient.objects.none()

    elif request.user.user_type == 'admin':
        # Admins can see all patients
        patients = Patient.objects.filter(is_active=True).order_by('-created_at')

    else:
        # Other user types see no patients
        patients = Patient.objects.none()

    return render(request, 'patients/list.html', {'patients': patients})

@login_required
def patient_register(request):
    """Register a new patient - Admin and Nurse only"""
    # Check if user has permission to register patients
    if request.user.user_type not in ['admin', 'nurse']:
        messages.error(request, 'Only administrators and nurses can register new patients.')
        return redirect('patients:list')

    if request.method == 'POST':
        form = PatientForm(request.POST)
        if form.is_valid():
            patient = form.save(commit=False)
            patient.created_by = request.user
            
            # Auto-assign specialty based on the user creating the patient
            if request.user.user_type == 'nurse':
                try:
                    from accounts.models import Nurse
                    nurse = Nurse.objects.get(user=request.user)
                    if not patient.primary_specialty:
                        patient.primary_specialty = nurse.specialty
                except Nurse.DoesNotExist:
                    pass
            elif request.user.user_type == 'doctor':
                try:
                    from accounts.models import Doctor
                    doctor = Doctor.objects.get(user=request.user)
                    if not patient.primary_specialty:
                        patient.primary_specialty = doctor.specialty
                except Doctor.DoesNotExist:
                    pass
            
            patient.save()
            messages.success(request, f'Patient {patient.full_name} registered successfully and assigned to {patient.primary_specialty.name if patient.primary_specialty else "General"} specialty!')
            return redirect('patients:detail', patient_id=patient.id)
    else:
        form = PatientForm()
    return render(request, 'patients/register.html', {'form': form})

@login_required
def patient_detail(request, patient_id):
    """View patient details with specialty-based access control"""
    patient = get_object_or_404(Patient, id=patient_id)

    # Check if user has access to this patient based on specialty
    if not patient.can_user_access(request.user):
        messages.error(request, 'You can only view patients in your specialty area.')
        return redirect('patients:list')

    updates = patient.updates.all()[:10]

    # Check if patient has been discharged
    discharge_info = None
    try:
        discharge_info = PatientDischarge.objects.get(patient=patient)
    except PatientDischarge.DoesNotExist:
        pass

    return render(request, 'patients/detail.html', {
        'patient': patient,
        'updates': updates,
        'discharge_info': discharge_info,
    })

@login_required
def patient_edit(request, patient_id):
    """Edit patient information"""
    patient = get_object_or_404(Patient, id=patient_id)

    # Check if doctor has access to this patient
    if request.user.user_type == 'doctor':
        try:
            from accounts.models import Doctor
            from appointments.models import Appointment

            doctor = Doctor.objects.get(user=request.user)

            # Check if this doctor has any appointments with this patient
            has_appointment = Appointment.objects.filter(
                doctor=doctor,
                patient=patient
            ).exists()

            if not has_appointment:
                messages.error(request, 'You can only edit patients assigned to you.')
                return redirect('patients:list')

        except Doctor.DoesNotExist:
            messages.error(request, 'Doctor profile not found.')
            return redirect('patients:list')

    if request.method == 'POST':
        form = PatientForm(request.POST, instance=patient)
        if form.is_valid():
            form.save()
            messages.success(request, f'Patient {patient.full_name} updated successfully!')
            return redirect('patients:detail', patient_id=patient.id)
    else:
        form = PatientForm(instance=patient)
    return render(request, 'patients/edit.html', {'form': form, 'patient': patient})

@login_required
def patient_update(request, patient_id):
    """Add patient update"""
    patient = get_object_or_404(Patient, id=patient_id)

    # Check if doctor has access to this patient
    if request.user.user_type == 'doctor':
        try:
            from accounts.models import Doctor
            from appointments.models import Appointment

            doctor = Doctor.objects.get(user=request.user)

            # Check if this doctor has any appointments with this patient
            has_appointment = Appointment.objects.filter(
                doctor=doctor,
                patient=patient
            ).exists()

            if not has_appointment:
                messages.error(request, 'You can only update patients assigned to you.')
                return redirect('patients:list')

        except Doctor.DoesNotExist:
            messages.error(request, 'Doctor profile not found.')
            return redirect('patients:list')

    if request.method == 'POST':
        form = PatientUpdateForm(request.POST)
        if form.is_valid():
            update = form.save(commit=False)
            update.patient = patient
            update.updated_by = request.user
            update.save()
            messages.success(request, 'Patient update added successfully!')
            return redirect('patients:detail', patient_id=patient.id)
    else:
        form = PatientUpdateForm()
    return render(request, 'patients/update.html', {'form': form, 'patient': patient})

@login_required
def patient_discharge(request, patient_id):
    """Discharge a patient - Admin only"""
    # Check if user is admin
    if request.user.user_type != 'admin':
        messages.error(request, 'Only administrators can discharge patients.')
        return redirect('patients:detail', patient_id=patient_id)

    patient = get_object_or_404(Patient, id=patient_id)

    # Check if patient is already discharged
    existing_discharge = PatientDischarge.objects.filter(patient=patient).first()
    if existing_discharge:
        messages.warning(request, f'Patient {patient.full_name} has already been discharged on {existing_discharge.discharge_date.strftime("%B %d, %Y at %I:%M %p")}.')
        return redirect('patients:detail', patient_id=patient.id)

    if request.method == 'POST':
        try:
            # Create discharge record manually to handle the form data properly
            from datetime import datetime

            discharge_date_str = request.POST.get('discharge_date')
            discharge_date = datetime.fromisoformat(discharge_date_str.replace('T', ' '))

            follow_up_date = None
            if request.POST.get('follow_up_date'):
                follow_up_date = datetime.strptime(request.POST.get('follow_up_date'), '%Y-%m-%d').date()

            # Validate required fields
            if not request.POST.get('final_diagnosis'):
                messages.error(request, 'Final diagnosis is required.')
                return render(request, 'patients/discharge.html', {'patient': patient})

            if not request.POST.get('treatment_given'):
                messages.error(request, 'Treatment given is required.')
                return render(request, 'patients/discharge.html', {'patient': patient})

            if not request.POST.get('discharge_summary'):
                messages.error(request, 'Discharge summary is required.')
                return render(request, 'patients/discharge.html', {'patient': patient})

            # Double-check if patient is already discharged (race condition protection)
            if PatientDischarge.objects.filter(patient=patient).exists():
                messages.warning(request, f'Patient {patient.full_name} has already been discharged.')
                return redirect('patients:detail', patient_id=patient.id)

            discharge = PatientDischarge.objects.create(
                patient=patient,
                discharge_date=discharge_date,
                final_diagnosis=request.POST.get('final_diagnosis'),
                treatment_given=request.POST.get('treatment_given'),
                discharge_summary=request.POST.get('discharge_summary'),
                medications_prescribed=request.POST.get('medications_prescribed', ''),
                follow_up_instructions=request.POST.get('follow_up_instructions', ''),
                follow_up_date=follow_up_date,
                discharged_by=request.user
            )

            # Mark patient as inactive (discharged)
            patient.is_active = False
            patient.save()

            messages.success(request, f'Patient {patient.full_name} has been discharged successfully on {discharge_date.strftime("%B %d, %Y at %I:%M %p")}!')
            return redirect('patients:list')

        except Exception as e:
            messages.error(request, f'Error discharging patient: {str(e)}')

    return render(request, 'patients/discharge.html', {'patient': patient})

@login_required
def patient_updates_list(request):
    """List all patient updates"""
    updates = PatientUpdate.objects.all().order_by('-created_at')
    return render(request, 'patients/updates_list.html', {'updates': updates})

@login_required
def patient_admission(request):
    """Patient admission - Admin only"""
    # Check if user is admin
    if request.user.user_type != 'admin':
        messages.error(request, 'Only administrators can admit patients.')
        return redirect('patients:list')

    from .models import PatientAdmission
    from accounts.models import Doctor
    from datetime import datetime

    if request.method == 'POST':
        # Get form data
        patient_id = request.POST.get('patient')
        doctor_id = request.POST.get('doctor')
        admission_type = request.POST.get('admission_type')
        reason_for_admission = request.POST.get('reason_for_admission')
        diagnosis = request.POST.get('diagnosis')
        symptoms = request.POST.get('symptoms', '')
        medical_history = request.POST.get('medical_history', '')
        allergies = request.POST.get('allergies', '')
        current_medications = request.POST.get('current_medications', '')
        expected_discharge_date = request.POST.get('expected_discharge_date')

        try:
            patient = Patient.objects.get(id=patient_id)
            doctor = Doctor.objects.get(user_id=doctor_id)

            # Create admission
            admission = PatientAdmission.objects.create(
                patient=patient,
                doctor=doctor,
                admission_date=datetime.now(),
                admission_type=admission_type,
                reason_for_admission=reason_for_admission,
                diagnosis=diagnosis,
                symptoms=symptoms,
                medical_history=medical_history,
                allergies=allergies,
                current_medications=current_medications,
                expected_discharge_date=expected_discharge_date if expected_discharge_date else None,
                admitted_by=request.user
            )

            messages.success(request, f'Patient {patient.full_name} admitted successfully with ID {admission.admission_id}!')
            return redirect('patients:admission_management')

        except (Patient.DoesNotExist, Doctor.DoesNotExist) as e:
            messages.error(request, 'Invalid patient or doctor selected.')

    # Get all active patients and doctors for the form
    patients = Patient.objects.filter(is_active=True).order_by('first_name')
    doctors = Doctor.objects.all().order_by('user__first_name')

    return render(request, 'patients/admission.html', {
        'patients': patients,
        'doctors': doctors
    })

@login_required
def admission_list(request):
    """List all patient admissions"""
    from .models import PatientAdmission

    if request.user.user_type == 'doctor':
        # Doctors only see their admitted patients
        try:
            from accounts.models import Doctor
            doctor = Doctor.objects.get(user=request.user)
            admissions = PatientAdmission.objects.filter(doctor=doctor, status='admitted').order_by('-admission_date')
        except Doctor.DoesNotExist:
            admissions = PatientAdmission.objects.none()
    else:
        # Admins and nurses see all admissions
        admissions = PatientAdmission.objects.all().order_by('-admission_date')

    return render(request, 'patients/admission_list.html', {'admissions': admissions})

@login_required
def admission_management(request):
    """Hospital admission management - Admin only"""
    # Check if user is admin
    if request.user.user_type != 'admin':
        messages.error(request, 'Only administrators can access admission management.')
        return redirect('dashboard')

    from .models import PatientAdmission
    from datetime import date, datetime

    # Get filter parameters
    status_filter = request.GET.get('status', '')
    type_filter = request.GET.get('type', '')
    search_query = request.GET.get('search', '')

    # Base queryset
    admissions = PatientAdmission.objects.all()

    # Apply filters
    if status_filter:
        admissions = admissions.filter(status=status_filter)

    if type_filter:
        admissions = admissions.filter(admission_type=type_filter)

    if search_query:
        admissions = admissions.filter(
            Q(patient__first_name__icontains=search_query) |
            Q(patient__last_name__icontains=search_query) |
            Q(admission_id__icontains=search_query)
        )

    admissions = admissions.order_by('-admission_date')

    # Calculate statistics
    today = date.today()
    total_admitted = PatientAdmission.objects.filter(status='admitted').count()
    under_treatment = PatientAdmission.objects.filter(status='admitted').count()
    emergency_count = PatientAdmission.objects.filter(
        admission_type='emergency',
        status='admitted'
    ).count()
    discharged_today = PatientAdmission.objects.filter(
        actual_discharge_date__date=today
    ).count()

    context = {
        'admissions': admissions,
        'total_admitted': total_admitted,
        'under_treatment': under_treatment,
        'emergency_count': emergency_count,
        'discharged_today': discharged_today,
        'status_filter': status_filter,
        'type_filter': type_filter,
        'search_query': search_query,
    }

    return render(request, 'patients/admission_management.html', context)

@login_required
def admission_detail(request, admission_id):
    """View admission details"""
    from .models import PatientAdmission

    admission = get_object_or_404(PatientAdmission, id=admission_id)

    # Check access permissions
    if request.user.user_type == 'doctor':
        try:
            from accounts.models import Doctor
            doctor = Doctor.objects.get(user=request.user)
            if admission.doctor != doctor:
                messages.error(request, 'You can only view admissions for your patients.')
                return redirect('patients:admission_list')
        except Doctor.DoesNotExist:
            messages.error(request, 'Doctor profile not found.')
            return redirect('patients:admission_list')

    return render(request, 'patients/admission_detail.html', {'admission': admission})

@login_required
def admission_edit(request, admission_id):
    """Edit admission details - Admin only"""
    if request.user.user_type != 'admin':
        messages.error(request, 'Only administrators can edit admissions.')
        return redirect('patients:admission_management')

    from .models import PatientAdmission
    from accounts.models import Doctor

    admission = get_object_or_404(PatientAdmission, id=admission_id)

    if request.method == 'POST':
        # Update admission details
        admission.reason_for_admission = request.POST.get('reason_for_admission', admission.reason_for_admission)
        admission.diagnosis = request.POST.get('diagnosis', admission.diagnosis)
        admission.symptoms = request.POST.get('symptoms', admission.symptoms)
        admission.medical_history = request.POST.get('medical_history', admission.medical_history)
        admission.allergies = request.POST.get('allergies', admission.allergies)
        admission.current_medications = request.POST.get('current_medications', admission.current_medications)

        expected_discharge = request.POST.get('expected_discharge_date')
        if expected_discharge:
            admission.expected_discharge_date = expected_discharge

        admission.save()
        messages.success(request, f'Admission {admission.admission_id} updated successfully!')
        return redirect('patients:admission_detail', admission_id=admission.id)

    doctors = Doctor.objects.all().order_by('user__first_name')
    return render(request, 'patients/admission_edit.html', {
        'admission': admission,
        'doctors': doctors
    })

@login_required
def admission_discharge(request, admission_id):
    """Discharge patient from hospital - Admin only"""
    if request.user.user_type != 'admin':
        messages.error(request, 'Only administrators can discharge patients.')
        return redirect('patients:admission_management')

    from .models import PatientAdmission
    from datetime import datetime

    admission = get_object_or_404(PatientAdmission, id=admission_id)

    if admission.status != 'admitted':
        messages.warning(request, 'Patient has already been discharged.')
        return redirect('patients:admission_detail', admission_id=admission.id)

    if request.method == 'POST':
        # Update admission status to discharged
        admission.status = 'discharged'
        admission.actual_discharge_date = datetime.now()
        admission.save()

        messages.success(request, f'Patient {admission.patient.full_name} has been discharged successfully!')
        return redirect('patients:admission_management')

    return render(request, 'patients/admission_discharge.html', {'admission': admission})

@login_required
def patient_admission_direct(request, patient_id):
    """Direct patient admission from patient detail page - Admin only"""
    # Check if user is admin
    if request.user.user_type != 'admin':
        messages.error(request, 'Only administrators can admit patients.')
        return redirect('patients:detail', patient_id=patient_id)

    patient = get_object_or_404(Patient, id=patient_id)

    # Check if patient is already admitted
    from .models import PatientAdmission
    existing_admission = PatientAdmission.objects.filter(
        patient=patient,
        status='admitted'
    ).first()

    if existing_admission:
        messages.warning(request, f'Patient {patient.full_name} is already admitted with ID {existing_admission.admission_id}.')
        return redirect('patients:admission_detail', admission_id=existing_admission.id)

    from accounts.models import Doctor
    from datetime import datetime

    if request.method == 'POST':
        # Get form data
        doctor_id = request.POST.get('doctor')
        admission_type = request.POST.get('admission_type')
        reason_for_admission = request.POST.get('reason_for_admission')
        diagnosis = request.POST.get('diagnosis')
        symptoms = request.POST.get('symptoms', '')
        medical_history = request.POST.get('medical_history', patient.medical_history or '')
        allergies = request.POST.get('allergies', patient.allergies or '')
        current_medications = request.POST.get('current_medications', patient.current_medications or '')
        expected_discharge_date = request.POST.get('expected_discharge_date')

        try:
            doctor = Doctor.objects.get(user_id=doctor_id)

            # Create admission
            admission = PatientAdmission.objects.create(
                patient=patient,
                doctor=doctor,
                admission_date=datetime.now(),
                admission_type=admission_type,
                reason_for_admission=reason_for_admission,
                diagnosis=diagnosis,
                symptoms=symptoms,
                medical_history=medical_history,
                allergies=allergies,
                current_medications=current_medications,
                expected_discharge_date=expected_discharge_date if expected_discharge_date else None,
                admitted_by=request.user
            )

            messages.success(request, f'Patient {patient.full_name} admitted successfully with ID {admission.admission_id}!')
            return redirect('patients:admission_detail', admission_id=admission.id)

        except Doctor.DoesNotExist:
            messages.error(request, 'Invalid doctor selected.')

    # Get all doctors for the form
    doctors = Doctor.objects.all().order_by('user__first_name')

    return render(request, 'patients/admission_direct.html', {
        'patient': patient,
        'doctors': doctors
    })
