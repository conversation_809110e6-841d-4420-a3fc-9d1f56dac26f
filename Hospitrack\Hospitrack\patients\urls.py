from django.urls import path
from . import views

app_name = 'patients'

urlpatterns = [
    path('', views.patient_list, name='list'),
    path('register/', views.patient_register, name='register'),
    path('<int:patient_id>/', views.patient_detail, name='detail'),
    path('<int:patient_id>/edit/', views.patient_edit, name='edit'),
    path('<int:patient_id>/update/', views.patient_update, name='update'),
    path('<int:patient_id>/discharge/', views.patient_discharge, name='discharge'),
    path('updates/', views.patient_updates_list, name='updates_list'),
    path('admission/', views.patient_admission, name='admission'),
    path('admissions/', views.admission_list, name='admission_list'),
    path('admission-management/', views.admission_management, name='admission_management'),
    path('admission/<int:admission_id>/', views.admission_detail, name='admission_detail'),
    path('admission/<int:admission_id>/edit/', views.admission_edit, name='admission_edit'),
    path('admission/<int:admission_id>/discharge/', views.admission_discharge, name='admission_discharge'),
    path('<int:patient_id>/admit/', views.patient_admission_direct, name='admission_direct'),
]
