# Generated by Django 5.2.4 on 2025-07-23 00:30

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('accounts', '0001_initial'),
        ('appointments', '0001_initial'),
        ('patients', '0001_initial'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='PatientOPDSchedule',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('schedule_id', models.CharField(max_length=20, unique=True)),
                ('schedule_date', models.DateField()),
                ('time_slot', models.CharField(choices=[('09:00', '9:00 AM'), ('09:30', '9:30 AM'), ('10:00', '10:00 AM'), ('10:30', '10:30 AM'), ('11:00', '11:00 AM'), ('11:30', '11:30 AM'), ('14:00', '2:00 PM'), ('14:30', '2:30 PM'), ('15:00', '3:00 PM'), ('15:30', '3:30 PM'), ('16:00', '4:00 PM'), ('16:30', '4:30 PM'), ('18:00', '6:00 PM'), ('18:30', '6:30 PM'), ('19:00', '7:00 PM'), ('19:30', '7:30 PM')], max_length=5)),
                ('reason', models.TextField()),
                ('special_instructions', models.TextField(blank=True, null=True)),
                ('is_recurring', models.BooleanField(default=False)),
                ('recurring_weeks', models.IntegerField(default=1, help_text='Number of weeks to repeat')),
                ('status', models.CharField(choices=[('scheduled', 'Scheduled'), ('confirmed', 'Confirmed'), ('in_progress', 'In Progress'), ('completed', 'Completed'), ('cancelled', 'Cancelled'), ('no_show', 'No Show')], default='scheduled', max_length=15)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('created_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to=settings.AUTH_USER_MODEL)),
                ('doctor', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='patient_opd_schedules', to='accounts.doctor')),
                ('patient', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='opd_schedules', to='patients.patient')),
            ],
            options={
                'ordering': ['schedule_date', 'time_slot'],
                'unique_together': {('doctor', 'schedule_date', 'time_slot')},
            },
        ),
    ]
