from django.core.management.base import BaseCommand
from staff.models import Department

class Command(BaseCommand):
    help = 'Create sample departments for OPD schedule'

    def handle(self, *args, **options):
        self.stdout.write('Creating departments...')
        
        departments = [
            {
                'name': 'General Medicine',
                'description': 'General medical consultation and treatment'
            },
            {
                'name': 'Emergency',
                'description': 'Emergency medical services and trauma care'
            },
            {
                'name': 'Surgery',
                'description': 'Surgical procedures and operations'
            },
            {
                'name': 'Pediatrics',
                'description': 'Medical care for children and infants'
            },
            {
                'name': 'Cardiology',
                'description': 'Heart and cardiovascular system care'
            },
            {
                'name': 'Orthopedics',
                'description': 'Bone, joint, and musculoskeletal care'
            },
            {
                'name': 'Gynecology',
                'description': 'Women\'s health and reproductive care'
            },
            {
                'name': 'Neurology',
                'description': 'Brain and nervous system care'
            }
        ]
        
        created_count = 0
        for dept_data in departments:
            dept, created = Department.objects.get_or_create(
                name=dept_data['name'],
                defaults={'description': dept_data['description']}
            )
            if created:
                created_count += 1
                self.stdout.write(f'✓ Created department: {dept.name}')
            else:
                self.stdout.write(f'- Department already exists: {dept.name}')
        
        self.stdout.write(f'\nCreated {created_count} new departments.')
        self.stdout.write(f'Total departments: {Department.objects.count()}')
