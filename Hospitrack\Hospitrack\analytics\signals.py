"""
Django signals for automatic analytics data collection
"""
from django.db.models.signals import post_save, post_delete
from django.dispatch import receiver
from django.utils import timezone
from datetime import date

from patients.models import PatientAdmission
from appointments.models import Appointment
from beds.models import Bed
from .services import AnalyticsDataCollector

@receiver(post_save, sender=PatientAdmission)
def update_patient_flow_on_admission(sender, instance, created, **kwargs):
    """Update patient flow metrics when a new admission is created"""
    if created:
        collector = AnalyticsDataCollector()
        collector.collect_patient_flow_metrics(instance.admission_date.date())

@receiver(post_save, sender=Appointment)
def update_metrics_on_appointment_change(sender, instance, **kwargs):
    """Update relevant metrics when appointment status changes"""
    collector = AnalyticsDataCollector()
    collector.collect_staff_efficiency_metrics(instance.appointment_date)
    collector.collect_financial_metrics(instance.appointment_date)

@receiver(post_save, sender=Bed)
def update_bed_metrics_on_status_change(sender, instance, **kwargs):
    """Update bed utilization metrics when bed status changes"""
    collector = AnalyticsDataCollector()
    collector.collect_bed_utilization_metrics(date.today())
