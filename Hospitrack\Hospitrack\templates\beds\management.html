{% extends 'base.html' %}

{% block title %}Bed Management - HospiTrack{% endblock %}

{% block content %}
<div class="space-y-6">
    <!-- Header -->
    <div class="bg-white shadow rounded-lg p-6">
        <div class="flex justify-between items-center">
            <div>
                <h1 class="text-3xl font-bold text-gray-900">Bed Management</h1>
                <p class="text-gray-600 mt-2">Monitor and manage hospital bed availability</p>
            </div>
            <div class="flex space-x-3">
                <a href="{% url 'admin_dashboard' %}" class="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
                    <i class="fas fa-arrow-left mr-2"></i>
                    Back to Dashboard
                </a>
                {% if user.user_type == 'admin' %}
                <a href="{% url 'beds:ward_create' %}" class="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
                    <i class="fas fa-plus mr-2"></i>
                    Add Ward
                </a>
                <a href="{% url 'beds:assign' %}" class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700">
                    <i class="fas fa-bed mr-2"></i>
                    Assign Bed
                </a>
                {% endif %}
            </div>
        </div>
    </div>

    <!-- Bed Statistics -->
    <div class="grid grid-cols-1 md:grid-cols-4 gap-6">
        <div class="bg-white overflow-hidden shadow rounded-lg">
            <div class="p-5">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <i class="fas fa-bed text-3xl text-blue-500"></i>
                    </div>
                    <div class="ml-5 w-0 flex-1">
                        <dl>
                            <dt class="text-sm font-medium text-gray-500 truncate">Total Beds</dt>
                            <dd class="text-3xl font-bold text-gray-900">{{ total_beds }}</dd>
                        </dl>
                    </div>
                </div>
            </div>
        </div>

        <div class="bg-white overflow-hidden shadow rounded-lg">
            <div class="p-5">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <i class="fas fa-check-circle text-3xl text-green-500"></i>
                    </div>
                    <div class="ml-5 w-0 flex-1">
                        <dl>
                            <dt class="text-sm font-medium text-gray-500 truncate">Available</dt>
                            <dd class="text-3xl font-bold text-green-600">{{ available_beds }}</dd>
                        </dl>
                    </div>
                </div>
            </div>
        </div>

        <div class="bg-white overflow-hidden shadow rounded-lg">
            <div class="p-5">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <i class="fas fa-user-injured text-3xl text-red-500"></i>
                    </div>
                    <div class="ml-5 w-0 flex-1">
                        <dl>
                            <dt class="text-sm font-medium text-gray-500 truncate">Occupied</dt>
                            <dd class="text-3xl font-bold text-red-600">{{ occupied_beds }}</dd>
                        </dl>
                    </div>
                </div>
            </div>
        </div>

        <div class="bg-white overflow-hidden shadow rounded-lg">
            <div class="p-5">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <i class="fas fa-tools text-3xl text-yellow-500"></i>
                    </div>
                    <div class="ml-5 w-0 flex-1">
                        <dl>
                            <dt class="text-sm font-medium text-gray-500 truncate">Maintenance</dt>
                            <dd class="text-3xl font-bold text-yellow-600">{{ maintenance_beds }}</dd>
                        </dl>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Wards Overview -->
    {% for ward in wards %}
    <div class="bg-white shadow rounded-lg p-6">
        <div class="flex justify-between items-center mb-4">
            <div>
                <h2 class="text-xl font-bold text-gray-900">{{ ward.name }}</h2>
                <p class="text-gray-600">{{ ward.get_ward_type_display }} - Floor {{ ward.floor }}</p>
            </div>
            <div class="text-sm text-gray-500">
                {{ ward.available_beds }}/{{ ward.capacity }} available
            </div>
        </div>

        <!-- Bed Grid -->
        <div class="grid grid-cols-2 sm:grid-cols-4 md:grid-cols-6 lg:grid-cols-8 xl:grid-cols-10 gap-3">
            {% for bed in ward.beds.all %}
            <div class="relative">
                <div class="bed-card p-3 rounded-lg border-2 cursor-pointer transition-all duration-200 
                    {% if bed.status == 'available' %}border-green-300 bg-green-50 hover:bg-green-100
                    {% elif bed.status == 'occupied' %}border-red-300 bg-red-50
                    {% elif bed.status == 'maintenance' %}border-yellow-300 bg-yellow-50
                    {% elif bed.status == 'emergency' %}border-blue-300 bg-blue-50
                    {% endif %}"
                    onclick="{% if user.user_type == 'nurse' or user.user_type == 'admin' %}openBedModal({{ bed.id }}, '{{ bed.bed_number }}', '{{ bed.status }}', '{{ ward.name }}'){% endif %}">
                    
                    <div class="text-center">
                        <div class="text-lg font-bold text-gray-900">{{ bed.bed_number }}</div>
                        <div class="text-xs 
                            {% if bed.status == 'available' %}text-green-700
                            {% elif bed.status == 'occupied' %}text-red-700
                            {% elif bed.status == 'maintenance' %}text-yellow-700
                            {% elif bed.status == 'emergency' %}text-blue-700
                            {% endif %}">
                            {{ bed.get_status_display }}
                        </div>
                        
                        <!-- Status Icon -->
                        <div class="mt-1">
                            {% if bed.status == 'available' %}
                                <i class="fas fa-check-circle text-green-500"></i>
                            {% elif bed.status == 'occupied' %}
                                <i class="fas fa-user text-red-500"></i>
                            {% elif bed.status == 'maintenance' %}
                                <i class="fas fa-tools text-yellow-500"></i>
                            {% elif bed.status == 'emergency' %}
                                <i class="fas fa-exclamation-triangle text-blue-500"></i>
                            {% endif %}
                        </div>
                    </div>
                </div>
            </div>
            {% endfor %}
        </div>

        <!-- Ward Statistics -->
        <div class="mt-4 grid grid-cols-4 gap-4 text-center text-sm">
            <div class="bg-green-50 p-2 rounded">
                <div class="font-semibold text-green-700">{{ ward.beds.all|length|add:"-"|add:ward.occupied_beds|add:"-"|add:ward.beds.all|length|add:"-"|add:ward.occupied_beds }}</div>
                <div class="text-green-600">Available</div>
            </div>
            <div class="bg-red-50 p-2 rounded">
                <div class="font-semibold text-red-700">{{ ward.occupied_beds }}</div>
                <div class="text-red-600">Occupied</div>
            </div>
            <div class="bg-yellow-50 p-2 rounded">
                <div class="font-semibold text-yellow-700">{{ ward.maintenance_beds }}</div>
                <div class="text-yellow-600">Maintenance</div>
            </div>
            <div class="bg-blue-50 p-2 rounded">
                <div class="font-semibold text-blue-700">{{ ward.emergency_beds }}</div>
                <div class="text-blue-600">Emergency</div>
            </div>
        </div>
    </div>
    {% endfor %}

    <!-- Legend -->
    <div class="bg-white shadow rounded-lg p-6">
        <h3 class="text-lg font-medium text-gray-900 mb-4">Bed Status Legend</h3>
        <div class="grid grid-cols-2 md:grid-cols-4 gap-4">
            <div class="flex items-center space-x-2">
                <div class="w-4 h-4 bg-green-100 border-2 border-green-300 rounded"></div>
                <span class="text-sm text-gray-700">Available</span>
            </div>
            <div class="flex items-center space-x-2">
                <div class="w-4 h-4 bg-red-100 border-2 border-red-300 rounded"></div>
                <span class="text-sm text-gray-700">Occupied</span>
            </div>
            <div class="flex items-center space-x-2">
                <div class="w-4 h-4 bg-yellow-100 border-2 border-yellow-300 rounded"></div>
                <span class="text-sm text-gray-700">Under Maintenance</span>
            </div>
            <div class="flex items-center space-x-2">
                <div class="w-4 h-4 bg-blue-100 border-2 border-blue-300 rounded"></div>
                <span class="text-sm text-gray-700">Emergency Reserved</span>
            </div>
        </div>
    </div>
</div>

<!-- Bed Status Update Modal -->
{% if user.user_type == 'nurse' or user.user_type == 'admin' %}
<div id="bedModal" class="hidden fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
    <div class="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
        <div class="mt-3">
            <h3 class="text-lg font-medium text-gray-900 mb-4">Update Bed Status</h3>
            <form id="bedStatusForm" method="post">
                {% csrf_token %}
                <input type="hidden" id="bedId" name="bed_id">
                
                <div class="mb-4">
                    <label class="block text-sm font-medium text-gray-700 mb-2">Bed</label>
                    <div id="bedInfo" class="text-sm text-gray-900 bg-gray-50 p-2 rounded"></div>
                </div>
                
                <div class="mb-4">
                    <label class="block text-sm font-medium text-gray-700 mb-2">New Status</label>
                    <select id="newStatus" name="status" class="w-full border border-gray-300 rounded-md px-3 py-2">
                        <option value="available">Available</option>
                        <option value="occupied">Occupied</option>
                        <option value="maintenance">Under Maintenance</option>
                        <option value="emergency">Emergency Reserved</option>
                    </select>
                </div>
                
                <div class="mb-4">
                    <label class="block text-sm font-medium text-gray-700 mb-2">Reason</label>
                    <textarea name="reason" class="w-full border border-gray-300 rounded-md px-3 py-2" rows="3" placeholder="Reason for status change..."></textarea>
                </div>
                
                <div class="flex justify-end space-x-3">
                    <button type="button" onclick="closeBedModal()" class="px-4 py-2 bg-gray-300 text-gray-700 rounded-md hover:bg-gray-400">Cancel</button>
                    <button type="submit" class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700">Update Status</button>
                </div>
            </form>
        </div>
    </div>
</div>
{% endif %}

<script>
function openBedModal(bedId, bedNumber, currentStatus, wardName) {
    document.getElementById('bedModal').classList.remove('hidden');
    document.getElementById('bedId').value = bedId;
    document.getElementById('bedInfo').textContent = `${wardName} - Bed ${bedNumber}`;
    document.getElementById('newStatus').value = currentStatus;
    
    // Update form action
    document.getElementById('bedStatusForm').action = `/beds/${bedId}/update-status/`;
}

function closeBedModal() {
    document.getElementById('bedModal').classList.add('hidden');
}

// Close modal when clicking outside
document.getElementById('bedModal').addEventListener('click', function(e) {
    if (e.target === this) {
        closeBedModal();
    }
});
</script>
{% endblock %}
