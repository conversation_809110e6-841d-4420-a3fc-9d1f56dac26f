{% extends 'base.html' %}

{% block title %}Edit Dr. {{ doctor.user.get_full_name }} - Doctor{% endblock %}

{% block content %}
<div class="space-y-6">
    <!-- Header -->
    <div class="flex items-center justify-between">
        <div>
            <h1 class="text-3xl font-bold text-gray-900">Edit Doctor</h1>
            <p class="text-gray-600 mt-2">Update Dr. {{ doctor.user.get_full_name }}'s profile</p>
        </div>
        <div class="flex space-x-3">
            <a href="{% url 'doctor_detail' doctor.user.id %}" class="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
                <i class="fas fa-arrow-left mr-2"></i>
                Back to Details
            </a>
        </div>
    </div>

    <!-- Edit Form -->
    <div class="bg-white shadow rounded-lg p-6">
        <form method="post" class="space-y-6">
            {% csrf_token %}
            
            <!-- Personal Information -->
            <div>
                <h2 class="text-lg font-medium text-gray-900 mb-4">Personal Information</h2>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">
                            First Name <span class="text-red-500">*</span>
                        </label>
                        <input type="text" name="first_name" value="{{ doctor.user.first_name }}" required class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500">
                    </div>
                    
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">
                            Last Name <span class="text-red-500">*</span>
                        </label>
                        <input type="text" name="last_name" value="{{ doctor.user.last_name }}" required class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500">
                    </div>
                    
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">
                            Email <span class="text-red-500">*</span>
                        </label>
                        <input type="email" name="email" value="{{ doctor.user.email }}" required class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500">
                    </div>
                    
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">
                            Phone Number
                        </label>
                        <input type="tel" name="phone" value="{{ doctor.user.phone }}" class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500">
                    </div>
                </div>
            </div>

            <!-- Professional Information -->
            <div>
                <h2 class="text-lg font-medium text-gray-900 mb-4">Professional Information</h2>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">
                            Specialization <span class="text-red-500">*</span>
                        </label>
                        <select name="specialization" required class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500 bg-white">
                            <option value="">Select Specialization</option>
                            {% for spec in specializations %}
                            <option value="{{ spec }}" {% if spec == doctor.specialization %}selected{% endif %}>{{ spec }}</option>
                            {% endfor %}
                        </select>
                    </div>
                    
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">
                            Department <span class="text-red-500">*</span>
                        </label>
                        <select name="department" required class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500 bg-white">
                            <option value="">Select Department</option>
                            {% for dept in departments %}
                            <option value="{{ dept }}" {% if dept == doctor.department %}selected{% endif %}>{{ dept }}</option>
                            {% endfor %}
                        </select>
                    </div>
                    
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">
                            License Number <span class="text-red-500">*</span>
                        </label>
                        <input type="text" name="license_number" value="{{ doctor.license_number }}" required class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500">
                    </div>
                    
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">
                            Experience (Years)
                        </label>
                        <input type="number" name="experience_years" value="{{ doctor.experience_years }}" min="0" max="50" class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500">
                    </div>
                    
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">
                            Consultation Fee
                        </label>
                        <input type="number" name="consultation_fee" value="{{ doctor.consultation_fee }}" min="0" step="0.01" class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500">
                    </div>
                </div>
            </div>

            <!-- Qualifications -->
            <div>
                <h2 class="text-lg font-medium text-gray-900 mb-4">Qualifications</h2>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">
                        Education & Certifications
                    </label>
                    <textarea name="qualifications" rows="4" class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500 resize-vertical">{{ doctor.qualifications }}</textarea>
                </div>
            </div>

            <!-- Form Actions -->
            <div class="flex justify-end space-x-3 pt-6 border-t border-gray-200">
                <a href="{% url 'doctor_detail' doctor.user.id %}" class="px-6 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
                    Cancel
                </a>
                <button type="submit" class="px-6 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700">
                    <i class="fas fa-save mr-2"></i>
                    Update Doctor
                </button>
            </div>
        </form>
    </div>

    <!-- Account Information -->
    <div class="bg-blue-50 border border-blue-200 rounded-lg p-4">
        <div class="flex">
            <div class="flex-shrink-0">
                <i class="fas fa-info-circle text-blue-400"></i>
            </div>
            <div class="ml-3">
                <h3 class="text-sm font-medium text-blue-800">Account Information</h3>
                <div class="mt-2 text-sm text-blue-700">
                    <ul class="list-disc list-inside space-y-1">
                        <li><strong>Username:</strong> {{ doctor.user.username }} (cannot be changed)</li>
                        <li><strong>Account Created:</strong> {{ doctor.user.date_joined|date:"F d, Y" }}</li>
                        <li><strong>Last Login:</strong> {{ doctor.user.last_login|date:"F d, Y H:i"|default:"Never" }}</li>
                        <li>To change password, contact system administrator</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
