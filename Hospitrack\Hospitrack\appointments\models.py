from django.db import models
from django.db.models import Max
from django.contrib.auth import get_user_model
from patients.models import Patient

User = get_user_model()

class Appointment(models.Model):
    STATUS_CHOICES = [
        ('scheduled', 'Scheduled'),
        ('confirmed', 'Confirmed'),
        ('in_progress', 'In Progress'),
        ('completed', 'Completed'),
        ('cancelled', 'Cancelled'),
        ('no_show', 'No Show'),
    ]

    APPOINTMENT_TYPE_CHOICES = [
        ('consultation', 'Consultation'),
        ('follow_up', 'Follow-up'),
        ('emergency', 'Emergency'),
        ('routine_checkup', 'Routine Checkup'),
        ('surgery', 'Surgery'),
    ]

    appointment_id = models.CharField(max_length=20, unique=True)
    patient = models.ForeignKey(Patient, on_delete=models.CASCADE, related_name='appointments')
    doctor = models.ForeignKey('accounts.Doctor', on_delete=models.CASCADE, related_name='appointments')
    appointment_date = models.DateField()
    appointment_time = models.TimeField()
    appointment_type = models.CharField(max_length=20, choices=APPOINTMENT_TYPE_CHOICES, default='consultation')
    status = models.CharField(max_length=15, choices=STATUS_CHOICES, default='scheduled')
    reason = models.TextField()
    notes = models.TextField(blank=True, null=True)
    diagnosis = models.TextField(blank=True, null=True)
    prescription = models.TextField(blank=True, null=True)
    fee = models.DecimalField(max_digits=10, decimal_places=2, default=0.00)
    is_paid = models.BooleanField(default=False)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    created_by = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, blank=True)

    class Meta:
        ordering = ['appointment_date', 'appointment_time']
        unique_together = ['doctor', 'appointment_date', 'appointment_time']

    def __str__(self):
        return f"{self.appointment_id} - {self.patient.full_name} with Dr. {self.doctor.user.last_name}"

    @property
    def is_today(self):
        from datetime import date
        return self.appointment_date == date.today()

class OPDSchedule(models.Model):
    WEEKDAY_CHOICES = [
        (0, 'Monday'),
        (1, 'Tuesday'),
        (2, 'Wednesday'),
        (3, 'Thursday'),
        (4, 'Friday'),
        (5, 'Saturday'),
        (6, 'Sunday'),
    ]

    doctor = models.ForeignKey('accounts.Doctor', on_delete=models.CASCADE, related_name='opd_schedules')
    weekday = models.IntegerField(choices=WEEKDAY_CHOICES)
    start_time = models.TimeField()
    end_time = models.TimeField()
    max_patients = models.IntegerField(default=20)
    is_active = models.BooleanField(default=True)
    created_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        unique_together = ['doctor', 'weekday', 'start_time']

    def __str__(self):
        return f"Dr. {self.doctor.user.last_name} - {self.get_weekday_display()} {self.start_time}-{self.end_time}"

class PatientOPDSchedule(models.Model):
    """OPD Schedule for patients registered by admin"""
    WEEKDAY_CHOICES = [
        (0, 'Monday'),
        (1, 'Tuesday'),
        (2, 'Wednesday'),
        (3, 'Thursday'),
        (4, 'Friday'),
        (5, 'Saturday'),
        (6, 'Sunday'),
    ]

    TIME_SLOT_CHOICES = [
        ('09:00', '9:00 AM'),
        ('09:30', '9:30 AM'),
        ('10:00', '10:00 AM'),
        ('10:30', '10:30 AM'),
        ('11:00', '11:00 AM'),
        ('11:30', '11:30 AM'),
        ('14:00', '2:00 PM'),
        ('14:30', '2:30 PM'),
        ('15:00', '3:00 PM'),
        ('15:30', '3:30 PM'),
        ('16:00', '4:00 PM'),
        ('16:30', '4:30 PM'),
        ('18:00', '6:00 PM'),
        ('18:30', '6:30 PM'),
        ('19:00', '7:00 PM'),
        ('19:30', '7:30 PM'),
    ]

    schedule_id = models.CharField(max_length=20, unique=True)
    patient = models.ForeignKey(Patient, on_delete=models.CASCADE, related_name='opd_schedules')
    doctor = models.ForeignKey('accounts.Doctor', on_delete=models.CASCADE, related_name='patient_opd_schedules')
    schedule_date = models.DateField()
    time_slot = models.CharField(max_length=5, choices=TIME_SLOT_CHOICES)
    reason = models.TextField()
    special_instructions = models.TextField(blank=True, null=True)
    is_recurring = models.BooleanField(default=False)
    recurring_weeks = models.IntegerField(default=1, help_text="Number of weeks to repeat")
    status = models.CharField(max_length=15, choices=Appointment.STATUS_CHOICES, default='scheduled')
    created_by = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, blank=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        unique_together = ['doctor', 'schedule_date', 'time_slot']
        ordering = ['schedule_date', 'time_slot']

    def __str__(self):
        return f"OPD {self.schedule_id} - {self.patient.full_name} with Dr. {self.doctor.user.get_full_name()}"

class OPDRegistration(models.Model):
    """OPD Registration for walk-in patients and outpatients"""
    GENDER_CHOICES = [
        ('M', 'Male'),
        ('F', 'Female'),
        ('O', 'Other'),
    ]

    VISIT_TYPE_CHOICES = [
        ('new', 'New Patient'),
        ('follow_up', 'Follow-up'),
        ('consultation', 'Consultation'),
        ('emergency', 'Emergency'),
    ]

    STATUS_CHOICES = [
        ('registered', 'Registered'),
        ('waiting', 'Waiting'),
        ('consulting', 'Consulting'),
        ('completed', 'Completed'),
        ('cancelled', 'Cancelled'),
    ]

    # Registration Details
    opd_number = models.CharField(max_length=20, unique=True)
    registration_date = models.DateField(auto_now_add=True)
    registration_time = models.TimeField(auto_now_add=True)

    # Patient Details (for walk-in patients)
    patient_name = models.CharField(max_length=100)
    age = models.IntegerField()
    gender = models.CharField(max_length=1, choices=GENDER_CHOICES)
    phone = models.CharField(max_length=15)
    address = models.TextField()

    # Medical Details
    doctor = models.ForeignKey('accounts.Doctor', on_delete=models.CASCADE, related_name='opd_registrations')
    visit_type = models.CharField(max_length=15, choices=VISIT_TYPE_CHOICES, default='new')
    chief_complaint = models.TextField(help_text="Main reason for visit")
    symptoms = models.TextField(blank=True, null=True)

    # Appointment Details
    preferred_time = models.CharField(max_length=5, choices=PatientOPDSchedule.TIME_SLOT_CHOICES)
    consultation_fee = models.DecimalField(max_digits=8, decimal_places=2)
    is_emergency = models.BooleanField(default=False)

    # Status and Processing
    status = models.CharField(max_length=15, choices=STATUS_CHOICES, default='registered')
    queue_number = models.IntegerField(null=True, blank=True)
    consultation_start_time = models.TimeField(null=True, blank=True)
    consultation_end_time = models.TimeField(null=True, blank=True)

    # Payment
    is_paid = models.BooleanField(default=False)
    payment_method = models.CharField(max_length=20, choices=[
        ('cash', 'Cash'),
        ('card', 'Card'),
        ('upi', 'UPI'),
        ('insurance', 'Insurance'),
    ], default='cash')

    # Notes
    receptionist_notes = models.TextField(blank=True, null=True)
    doctor_notes = models.TextField(blank=True, null=True)

    # System fields
    created_by = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, blank=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        ordering = ['registration_date', 'queue_number', 'registration_time']
        # Remove the unique constraint that was causing issues
        # unique_together = ['doctor', 'registration_date', 'queue_number']

    def save(self, *args, **kwargs):
        if not self.opd_number:
            from datetime import date
            today = date.today()
            # Use a more robust method to generate unique OPD numbers
            existing_count = OPDRegistration.objects.filter(registration_date=today).count()
            for i in range(existing_count + 1, existing_count + 100):  # Try up to 100 numbers
                opd_number = f"OPD{today.strftime('%Y%m%d')}{i:03d}"
                if not OPDRegistration.objects.filter(opd_number=opd_number).exists():
                    self.opd_number = opd_number
                    break

        if not self.queue_number:
            # Set registration_date if not set
            if not self.registration_date:
                from datetime import date
                self.registration_date = date.today()

            # Use a more robust method to assign queue numbers
            from django.db import transaction
            with transaction.atomic():
                # Get the highest queue number for this doctor today
                max_queue = OPDRegistration.objects.filter(
                    doctor=self.doctor,
                    registration_date=self.registration_date
                ).exclude(pk=self.pk).aggregate(max_queue=Max('queue_number'))['max_queue']

                self.queue_number = (max_queue or 0) + 1

        if not self.consultation_fee and self.doctor:
            self.consultation_fee = self.doctor.consultation_fee

        super().save(*args, **kwargs)

    def get_preferred_time_display(self):
        """Return formatted time display"""
        time_map = {
            '09:00': '9:00 AM',
            '09:30': '9:30 AM',
            '10:00': '10:00 AM',
            '10:30': '10:30 AM',
            '11:00': '11:00 AM',
            '11:30': '11:30 AM',
            '14:00': '2:00 PM',
            '14:30': '2:30 PM',
            '15:00': '3:00 PM',
            '15:30': '3:30 PM',
            '16:00': '4:00 PM',
            '16:30': '4:30 PM',
            '18:00': '6:00 PM',
            '18:30': '6:30 PM',
            '19:00': '7:00 PM',
            '19:30': '7:30 PM',
        }
        return time_map.get(self.preferred_time, self.preferred_time)

    def __str__(self):
        return f"{self.opd_number} - {self.patient_name} with Dr. {self.doctor.user.get_full_name()}"
