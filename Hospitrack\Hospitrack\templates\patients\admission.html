{% extends 'base.html' %}

{% block title %}Patient Admission - HospiTrack{% endblock %}

{% block content %}
<div class="space-y-6">
    <!-- Header -->
    <div class="bg-white shadow rounded-lg p-6">
        <div class="flex justify-between items-center">
            <div>
                <h1 class="text-3xl font-bold text-gray-900">Patient Admission</h1>
                <p class="text-gray-600 mt-2">Admit a patient to the hospital</p>
            </div>
            <div class="flex space-x-3">
                <a href="{% url 'patients:admission_management' %}" class="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
                    <i class="fas fa-list mr-2"></i>
                    Admission Management
                </a>
                <a href="{% url 'patients:admission_list' %}" class="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
                    <i class="fas fa-bed mr-2"></i>
                    View Admissions
                </a>
                <a href="{% url 'admin_dashboard' %}" class="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
                    <i class="fas fa-arrow-left mr-2"></i>
                    Back to Dashboard
                </a>
            </div>
        </div>
    </div>

    <!-- Admission Form -->
    <div class="bg-white shadow rounded-lg p-6">
        <form method="post" class="space-y-6">
            {% csrf_token %}
            
            <!-- Patient and Doctor Selection -->
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">
                        Select Patient <span class="text-red-500">*</span>
                    </label>
                    <select name="patient" required class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500 bg-white">
                        <option value="">Choose Patient</option>
                        {% for patient in patients %}
                        <option value="{{ patient.id }}">
                            {{ patient.patient_id }} - {{ patient.full_name }} ({{ patient.age }} years)
                        </option>
                        {% endfor %}
                    </select>
                </div>
                
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">
                        Attending Doctor <span class="text-red-500">*</span>
                    </label>
                    <select name="doctor" required class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500 bg-white">
                        <option value="">Choose Doctor</option>
                        {% for doctor in doctors %}
                        <option value="{{ doctor.id }}">
                            Dr. {{ doctor.user.get_full_name }} - {{ doctor.specialization }}
                        </option>
                        {% endfor %}
                    </select>
                </div>
            </div>

            <!-- Admission Details -->
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">
                        Admission Type <span class="text-red-500">*</span>
                    </label>
                    <select name="admission_type" required class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500 bg-white">
                        <option value="">Select Type</option>
                        <option value="emergency">Emergency</option>
                        <option value="planned">Planned</option>
                        <option value="transfer">Transfer</option>
                        <option value="observation">Observation</option>
                    </select>
                </div>
                
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">
                        Expected Discharge Date
                    </label>
                    <input type="date" name="expected_discharge_date" class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500">
                </div>
            </div>

            <!-- Medical Information -->
            <div class="space-y-6">
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">
                        Reason for Admission <span class="text-red-500">*</span>
                    </label>
                    <textarea name="reason_for_admission" required rows="3" class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500 resize-vertical" placeholder="Describe the reason for admission"></textarea>
                </div>
                
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">
                        Primary Diagnosis <span class="text-red-500">*</span>
                    </label>
                    <textarea name="diagnosis" required rows="3" class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500 resize-vertical" placeholder="Primary diagnosis or suspected condition"></textarea>
                </div>
                
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">
                        Current Symptoms
                    </label>
                    <textarea name="symptoms" rows="3" class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500 resize-vertical" placeholder="Current symptoms and complaints"></textarea>
                </div>
                
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">
                            Medical History
                        </label>
                        <textarea name="medical_history" rows="4" class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500 resize-vertical" placeholder="Relevant medical history"></textarea>
                    </div>
                    
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">
                            Known Allergies
                        </label>
                        <textarea name="allergies" rows="4" class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500 resize-vertical" placeholder="Known allergies and reactions"></textarea>
                    </div>
                </div>
                
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">
                        Current Medications
                    </label>
                    <textarea name="current_medications" rows="3" class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500 resize-vertical" placeholder="Current medications and dosages"></textarea>
                </div>
            </div>

            <!-- Form Actions -->
            <div class="flex justify-end space-x-3 pt-6 border-t border-gray-200">
                <a href="{% url 'patients:admission_management' %}" class="px-6 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
                    Cancel
                </a>
                <button type="submit" class="px-6 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-orange-600 hover:bg-orange-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-orange-500">
                    <i class="fas fa-bed mr-2"></i>
                    Admit Patient
                </button>
            </div>
        </form>
    </div>
</div>

<script>
// Auto-populate patient details when selected
document.querySelector('select[name="patient"]').addEventListener('change', function() {
    const selectedOption = this.options[this.selectedIndex];
    if (selectedOption.value) {
        // You can add AJAX call here to fetch patient details if needed
        console.log('Patient selected:', selectedOption.text);
    }
});

// Auto-populate doctor details when selected
document.querySelector('select[name="doctor"]').addEventListener('change', function() {
    const selectedOption = this.options[this.selectedIndex];
    if (selectedOption.value) {
        console.log('Doctor selected:', selectedOption.text);
    }
});
</script>
{% endblock %}
