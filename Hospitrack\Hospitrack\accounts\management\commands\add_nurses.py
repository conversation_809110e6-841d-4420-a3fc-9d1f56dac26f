from django.core.management.base import BaseCommand
from django.contrib.auth import get_user_model
from accounts.models import Nurse
from staff.models import StaffMember, Department
from datetime import date, timedelta
import random

User = get_user_model()

class Command(BaseCommand):
    help = 'Add 50 nurses with realistic details'

    def handle(self, *args, **options):
        self.stdout.write('Adding 50 nurses...')
        
        # Nurse names (mix of male and female names)
        first_names = [
            '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>',
            '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>',
            '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>',
            '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>',
            '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>'
        ]
        
        last_names = [
            '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>',
            '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>',
            '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>',
            '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON>', '<PERSON>',
            '<PERSON>', 'Adams', 'Nelson', 'Baker', 'Hall', 'Rivera', 'Campbell', 'Mitchell', 'Carter', 'Roberts'
        ]
        
        departments = ['General Medicine', 'Emergency', 'Surgery', 'Pediatrics', 'Cardiology', 'Orthopedics']
        shifts = ['morning', 'evening', 'night']
        employment_types = ['full_time', 'part_time', 'contract']
        
        # Get or create departments
        dept_objects = []
        for dept_name in departments:
            dept, created = Department.objects.get_or_create(name=dept_name)
            dept_objects.append(dept)
        
        created_count = 0
        
        for i in range(1, 51):  # Create 50 nurses
            try:
                # Generate unique username and employee ID
                first_name = random.choice(first_names)
                last_name = random.choice(last_names)
                username = f"{first_name.lower()}.{last_name.lower()}{i}"
                employee_id = f"NUR{i:03d}"
                
                # Check if user already exists
                if User.objects.filter(username=username).exists():
                    username = f"{first_name.lower()}.{last_name.lower()}.{i}"
                
                # Create user account
                user = User.objects.create_user(
                    username=username,
                    email=f"{username}@hospitrack.com",
                    password='nurse123',
                    first_name=first_name,
                    last_name=last_name,
                    user_type='nurse'
                )
                
                # Generate realistic details
                hire_date = date.today() - timedelta(days=random.randint(30, 1825))  # 1 month to 5 years ago
                experience_years = random.randint(1, 15)
                salary = random.randint(35000, 65000)
                
                # Create staff member record
                staff_member = StaffMember.objects.create(
                    user=user,
                    employee_id=employee_id,
                    department=random.choice(dept_objects),
                    designation='nurse',
                    employment_type=random.choice(employment_types),
                    hire_date=hire_date,
                    salary=salary,
                    experience_years=experience_years,
                    emergency_contact_name=f"{random.choice(first_names)} {random.choice(last_names)}",
                    emergency_contact_phone=f"98765{random.randint(10000, 99999)}",
                    emergency_contact_relation=random.choice(['Spouse', 'Parent', 'Sibling', 'Friend']),
                    qualifications=f"BSN from {random.choice(['State University', 'Medical College', 'Nursing Institute', 'Health Sciences University'])}"
                )
                
                # Create nurse profile
                nurse = Nurse.objects.create(
                    user=user,
                    department=staff_member.department.name,
                    shift=random.choice(shifts),
                    license_number=f"RN{random.randint(100000, 999999)}"
                )
                
                created_count += 1
                self.stdout.write(f'✓ Created nurse: {first_name} {last_name} ({employee_id})')
                
            except Exception as e:
                self.stdout.write(f'✗ Error creating nurse {i}: {str(e)}')
                continue
        
        self.stdout.write(self.style.SUCCESS(f'Successfully created {created_count} nurses!'))
        self.stdout.write('\nNurse login format: firstname.lastname[number] / nurse123')
        self.stdout.write('Example: sarah.smith1 / nurse123')
