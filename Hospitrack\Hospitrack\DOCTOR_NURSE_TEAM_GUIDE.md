# Doctor-Nurse Team Management Guide

## 🎯 Complete System Overview

The HospiTrack system now allows doctors to see exactly which nurses are working under their specialty and track their leave status in real-time. This ensures proper team coordination and patient care planning.

## 🚀 How to Use the System

### For Doctors:

#### 1. **Login to Your Dashboard**
- URL: `http://127.0.0.1:8000/login/`
- Use your doctor credentials (e.g., `dr.cardiologist` / `doctor123`)

#### 2. **View Team Statistics on Dashboard**
Your dashboard now shows:
- **Available Nurses Count**: Green number showing nurses ready for duty
- **On Leave Count**: Red number showing nurses currently on leave
- **"View Team & Nurses" Button**: Click to see detailed team information

#### 3. **Access Detailed Team View**
- Click "View Team & Nurses" button
- URL: `http://127.0.0.1:8000/accounts/specialty-team/`
- See comprehensive team overview

#### 4. **Team Information Available**
- **Available Nurses**: 
  - Names and shift schedules
  - License numbers
  - Current availability status
- **Nurses on Leave**:
  - Leave type (Sick, Vacation, Emergency, etc.)
  - Leave duration and end dates
  - Days remaining
  - Reason for leave
- **Shift Distribution**:
  - Morning shift (6 AM - 2 PM)
  - Evening shift (2 PM - 10 PM)
  - Night shift (10 PM - 6 AM)

### For Admins:

#### 1. **Manage Nurse Leave Status**
- URL: `http://127.0.0.1:8000/accounts/nurses/`
- View all nurses with current leave status
- Click "Manage Leave" for any nurse

#### 2. **Set Leave Status**
- Choose leave type: Available, On Leave, Sick Leave, Vacation, Emergency Leave
- Set start and end dates
- Add reason for leave
- System automatically updates availability

## 📊 Current System Status

### **Live Demonstration Data:**

#### **Cardiology Team (Dr. Sarah Johnson):**
- **Total Nurses**: 2
- **Available**: 1 nurse
  - ✅ **John Torres** - Evening Shift (2 PM - 10 PM) - Available
- **On Leave**: 1 nurse
  - ❌ **Christine Martinez** - Night Shift (10 PM - 6 AM) - Vacation until Aug 2 (6 days remaining)

#### **Orthopedics Team (Dr. James Wilson):**
- **Total Nurses**: 1
- **Available**: 1 nurse
  - ✅ **Catherine Sanchez** - Evening Shift (2 PM - 10 PM) - Available

#### **Pediatrics Team (Dr. Lisa Thompson):**
- **Total Nurses**: 1
- **Available**: 1 nurse
  - ✅ **James Garcia** - Morning Shift (6 AM - 2 PM) - Available

#### **Emergency Medicine (No assigned doctor):**
- **Total Nurses**: 2
- **Available**: 1 nurse
  - ✅ **William Allen** - Night Shift (10 PM - 6 AM) - Available
- **On Leave**: 1 nurse
  - ❌ **Amy Adams** - Morning Shift (6 AM - 2 PM) - Sick Leave until Jul 29 (2 days remaining)

#### **General Medicine (No assigned doctor):**
- **Total Nurses**: 3
- **Available**: 2 nurses
  - ✅ **Christine Taylor** - Evening Shift (2 PM - 10 PM) - Available
  - ✅ **Mark Anderson** - Evening Shift (2 PM - 10 PM) - Available
- **On Leave**: 1 nurse
  - ❌ **Michelle Davis** - Morning Shift (6 AM - 2 PM) - Emergency Leave until Jul 27 (ending today)

## 🔒 Access Control Features

### **What Doctors CAN See:**
- ✅ All nurses in their own specialty
- ✅ Real-time availability status
- ✅ Leave details and duration
- ✅ Shift schedules
- ✅ Contact information

### **What Doctors CANNOT See:**
- ❌ Nurses from other specialties
- ❌ Leave details of nurses in other departments
- ❌ Admin-only management functions

### **Example Access Control:**
**Dr. Sarah Johnson (Cardiology)** can see:
- ✅ Christine Martinez (Cardiology)
- ✅ John Torres (Cardiology)

But **CANNOT** see:
- ❌ Catherine Sanchez (Orthopedics)
- ❌ Amy Adams (Emergency Medicine)
- ❌ James Garcia (Pediatrics)

## 🛠️ System Features

### **Automatic Updates:**
- Leave status automatically updates based on dates
- Expired leaves are cleared automatically
- Real-time availability calculations
- Dashboard statistics update instantly

### **Leave Types Supported:**
1. **Available** - Ready for duty
2. **On Leave** - General leave
3. **Sick Leave** - Medical leave
4. **Vacation** - Planned vacation
5. **Emergency Leave** - Urgent personal matters

### **Shift Management:**
- **Morning**: 6 AM - 2 PM
- **Evening**: 2 PM - 10 PM
- **Night**: 10 PM - 6 AM

## 🎮 Testing the System

### **Quick Test Steps:**

1. **Login as Cardiology Doctor:**
   ```
   Username: dr.cardiologist
   Password: doctor123
   ```

2. **Check Dashboard:**
   - Should see "1 Available, 1 On Leave" in team statistics
   - Click "View Team & Nurses"

3. **Verify Team View:**
   - See John Torres as available
   - See Christine Martinez on vacation
   - View leave details and shift information

4. **Test Admin Functions:**
   ```
   Username: admin
   Password: admin123
   ```
   - Go to Nurses list
   - Click "Manage Leave" for any nurse
   - Update leave status

### **URLs for Testing:**
- **Login**: `http://127.0.0.1:8000/login/`
- **Doctor Dashboard**: `http://127.0.0.1:8000/accounts/doctor-dashboard/`
- **Team View**: `http://127.0.0.1:8000/accounts/specialty-team/`
- **Admin Nurses**: `http://127.0.0.1:8000/accounts/nurses/`

## 🎯 Real-World Usage Scenarios

### **Scenario 1: Planning Patient Care**
Dr. Sarah Johnson (Cardiology) needs to assign a cardiac patient:
1. Checks team view
2. Sees John Torres available for evening shift
3. Knows Christine Martinez is on vacation until Aug 2
4. Plans patient care accordingly

### **Scenario 2: Emergency Coverage**
Amy Adams (Emergency Medicine) calls in sick:
1. Admin updates her status to "Sick Leave"
2. System immediately shows her as unavailable
3. Other staff can see the updated status
4. Emergency coverage can be arranged

### **Scenario 3: Shift Planning**
Dr. James Wilson (Orthopedics) planning next week:
1. Views team availability
2. Sees Catherine Sanchez available for evening shift
3. Plans surgeries and patient rounds accordingly
4. Knows exact nurse availability by shift

## 🚀 Benefits Achieved

### **For Doctors:**
- ✅ **Real-time team visibility** - Know exactly who's available
- ✅ **Leave awareness** - Plan around nurse availability
- ✅ **Shift coordination** - See nurse schedules by shift
- ✅ **Specialty focus** - Only see relevant team members

### **For Nurses:**
- ✅ **Professional tracking** - Proper leave documentation
- ✅ **Status transparency** - Clear visibility of leave status
- ✅ **Shift organization** - Organized by specialty and shift

### **For Admins:**
- ✅ **Centralized management** - Control all nurse leave from one place
- ✅ **Real-time updates** - Immediate system-wide status changes
- ✅ **Planning support** - Help with staffing decisions

## 🎉 System Ready!

The doctor-nurse team management system is now fully operational and ready for use. Doctors can see their specialty nurses, track leave status, and plan patient care effectively while maintaining proper access controls and data security.

**Test it now at: http://127.0.0.1:8000/**