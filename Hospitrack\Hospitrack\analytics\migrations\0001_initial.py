# Generated by Django 5.2.4 on 2025-07-29 15:28

import django.db.models.deletion
import django.utils.timezone
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('accounts', '0005_nurse_experience_years_nurse_qualifications'),
        ('beds', '0001_initial'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='PredictiveModel',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100)),
                ('model_type', models.CharField(choices=[('patient_flow', 'Patient Flow Prediction'), ('bed_demand', 'Bed Demand Forecast'), ('staff_scheduling', 'Staff Scheduling Optimization'), ('resource_planning', 'Resource Planning'), ('risk_assessment', 'Risk Assessment')], max_length=20)),
                ('prediction_date', models.DateField()),
                ('target_date', models.DateField()),
                ('predicted_value', models.DecimalField(decimal_places=4, max_digits=15)),
                ('confidence_score', models.DecimalField(decimal_places=4, default=0, max_digits=5)),
                ('model_version', models.CharField(max_length=50)),
                ('input_features', models.JSONField(default=dict)),
                ('model_parameters', models.JSONField(default=dict)),
                ('actual_value', models.DecimalField(blank=True, decimal_places=4, max_digits=15, null=True)),
                ('accuracy_score', models.DecimalField(blank=True, decimal_places=4, max_digits=5, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
            ],
            options={
                'ordering': ['-prediction_date', '-target_date'],
            },
        ),
        migrations.CreateModel(
            name='AlertRule',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100)),
                ('description', models.TextField()),
                ('alert_type', models.CharField(choices=[('threshold', 'Threshold Alert'), ('trend', 'Trend Alert'), ('anomaly', 'Anomaly Detection'), ('prediction', 'Prediction Alert')], max_length=20)),
                ('severity', models.CharField(choices=[('low', 'Low'), ('medium', 'Medium'), ('high', 'High'), ('critical', 'Critical')], max_length=10)),
                ('metric_name', models.CharField(max_length=100)),
                ('threshold_value', models.DecimalField(blank=True, decimal_places=4, max_digits=15, null=True)),
                ('condition', models.CharField(choices=[('>', 'Greater than'), ('<', 'Less than'), ('>=', 'Greater than or equal'), ('<=', 'Less than or equal'), ('==', 'Equal to'), ('!=', 'Not equal to')], max_length=10)),
                ('email_enabled', models.BooleanField(default=True)),
                ('sms_enabled', models.BooleanField(default=False)),
                ('is_active', models.BooleanField(default=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('notify_users', models.ManyToManyField(blank=True, to=settings.AUTH_USER_MODEL)),
            ],
        ),
        migrations.CreateModel(
            name='Alert',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('triggered_at', models.DateTimeField(auto_now_add=True)),
                ('metric_value', models.DecimalField(decimal_places=4, max_digits=15)),
                ('message', models.TextField()),
                ('is_acknowledged', models.BooleanField(default=False)),
                ('acknowledged_at', models.DateTimeField(blank=True, null=True)),
                ('is_resolved', models.BooleanField(default=False)),
                ('resolved_at', models.DateTimeField(blank=True, null=True)),
                ('resolution_notes', models.TextField(blank=True, null=True)),
                ('acknowledged_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to=settings.AUTH_USER_MODEL)),
                ('resolved_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='resolved_alerts', to=settings.AUTH_USER_MODEL)),
                ('rule', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='alerts', to='analytics.alertrule')),
            ],
            options={
                'ordering': ['-triggered_at'],
            },
        ),
        migrations.CreateModel(
            name='AnalyticsMetric',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100)),
                ('metric_type', models.CharField(choices=[('patient_flow', 'Patient Flow'), ('bed_utilization', 'Bed Utilization'), ('staff_efficiency', 'Staff Efficiency'), ('financial', 'Financial'), ('quality', 'Quality Metrics'), ('operational', 'Operational'), ('predictive', 'Predictive')], max_length=20)),
                ('value', models.DecimalField(decimal_places=4, max_digits=15)),
                ('unit', models.CharField(blank=True, max_length=50, null=True)),
                ('frequency', models.CharField(choices=[('hourly', 'Hourly'), ('daily', 'Daily'), ('weekly', 'Weekly'), ('monthly', 'Monthly'), ('quarterly', 'Quarterly'), ('yearly', 'Yearly')], max_length=20)),
                ('date_recorded', models.DateTimeField(default=django.utils.timezone.now)),
                ('metadata', models.JSONField(blank=True, default=dict)),
                ('department', models.CharField(blank=True, max_length=100, null=True)),
                ('specialty', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='accounts.medicalspecialty')),
            ],
            options={
                'ordering': ['-date_recorded'],
                'indexes': [models.Index(fields=['metric_type', 'date_recorded'], name='analytics_a_metric__39b8c0_idx'), models.Index(fields=['name', 'date_recorded'], name='analytics_a_name_96b6f0_idx')],
            },
        ),
        migrations.CreateModel(
            name='BedUtilizationMetrics',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('date', models.DateField()),
                ('total_beds', models.IntegerField()),
                ('occupied_beds', models.IntegerField(default=0)),
                ('available_beds', models.IntegerField(default=0)),
                ('maintenance_beds', models.IntegerField(default=0)),
                ('utilization_rate', models.DecimalField(decimal_places=2, default=0, max_digits=5)),
                ('turnover_rate', models.DecimalField(decimal_places=2, default=0, max_digits=5)),
                ('average_length_of_stay', models.DecimalField(decimal_places=2, default=0, max_digits=5)),
                ('revenue_per_bed', models.DecimalField(decimal_places=2, default=0, max_digits=10)),
                ('total_revenue', models.DecimalField(decimal_places=2, default=0, max_digits=12)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('ward', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='utilization_metrics', to='beds.ward')),
            ],
            options={
                'ordering': ['-date'],
                'unique_together': {('date', 'ward')},
            },
        ),
        migrations.CreateModel(
            name='FinancialMetrics',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('date', models.DateField()),
                ('consultation_revenue', models.DecimalField(decimal_places=2, default=0, max_digits=12)),
                ('bed_revenue', models.DecimalField(decimal_places=2, default=0, max_digits=12)),
                ('procedure_revenue', models.DecimalField(decimal_places=2, default=0, max_digits=12)),
                ('pharmacy_revenue', models.DecimalField(decimal_places=2, default=0, max_digits=12)),
                ('other_revenue', models.DecimalField(decimal_places=2, default=0, max_digits=12)),
                ('staff_costs', models.DecimalField(decimal_places=2, default=0, max_digits=12)),
                ('operational_costs', models.DecimalField(decimal_places=2, default=0, max_digits=12)),
                ('equipment_costs', models.DecimalField(decimal_places=2, default=0, max_digits=12)),
                ('total_revenue', models.DecimalField(decimal_places=2, default=0, max_digits=12)),
                ('total_costs', models.DecimalField(decimal_places=2, default=0, max_digits=12)),
                ('net_profit', models.DecimalField(decimal_places=2, default=0, max_digits=12)),
                ('profit_margin', models.DecimalField(decimal_places=2, default=0, max_digits=5)),
                ('department', models.CharField(blank=True, max_length=100, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('specialty', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='accounts.medicalspecialty')),
            ],
            options={
                'ordering': ['-date'],
                'unique_together': {('date', 'department', 'specialty')},
            },
        ),
        migrations.CreateModel(
            name='PatientFlowMetrics',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('date', models.DateField()),
                ('hour', models.IntegerField(blank=True, null=True)),
                ('total_admissions', models.IntegerField(default=0)),
                ('emergency_admissions', models.IntegerField(default=0)),
                ('planned_admissions', models.IntegerField(default=0)),
                ('total_discharges', models.IntegerField(default=0)),
                ('average_length_of_stay', models.DecimalField(decimal_places=2, default=0, max_digits=5)),
                ('total_occupied_beds', models.IntegerField(default=0)),
                ('occupancy_rate', models.DecimalField(decimal_places=2, default=0, max_digits=5)),
                ('average_wait_time_minutes', models.IntegerField(default=0)),
                ('department', models.CharField(blank=True, max_length=100, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('specialty', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='accounts.medicalspecialty')),
            ],
            options={
                'ordering': ['-date', '-hour'],
                'unique_together': {('date', 'hour', 'department')},
            },
        ),
        migrations.CreateModel(
            name='QualityMetrics',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('date', models.DateField()),
                ('average_satisfaction_score', models.DecimalField(decimal_places=2, default=0, max_digits=3)),
                ('satisfaction_responses', models.IntegerField(default=0)),
                ('readmission_rate', models.DecimalField(decimal_places=2, default=0, max_digits=5)),
                ('infection_rate', models.DecimalField(decimal_places=2, default=0, max_digits=5)),
                ('mortality_rate', models.DecimalField(decimal_places=2, default=0, max_digits=5)),
                ('average_wait_time', models.IntegerField(default=0)),
                ('appointment_punctuality', models.DecimalField(decimal_places=2, default=0, max_digits=5)),
                ('incident_count', models.IntegerField(default=0)),
                ('near_miss_count', models.IntegerField(default=0)),
                ('department', models.CharField(blank=True, max_length=100, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('specialty', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='accounts.medicalspecialty')),
            ],
            options={
                'ordering': ['-date'],
                'unique_together': {('date', 'department', 'specialty')},
            },
        ),
        migrations.CreateModel(
            name='StaffEfficiencyMetrics',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('date', models.DateField()),
                ('patients_seen', models.IntegerField(default=0)),
                ('appointments_completed', models.IntegerField(default=0)),
                ('average_consultation_time', models.IntegerField(default=0)),
                ('patient_satisfaction_score', models.DecimalField(blank=True, decimal_places=2, max_digits=3, null=True)),
                ('no_show_rate', models.DecimalField(decimal_places=2, default=0, max_digits=5)),
                ('scheduled_hours', models.DecimalField(decimal_places=2, default=8, max_digits=4)),
                ('actual_hours', models.DecimalField(decimal_places=2, default=0, max_digits=4)),
                ('overtime_hours', models.DecimalField(decimal_places=2, default=0, max_digits=4)),
                ('revenue_generated', models.DecimalField(decimal_places=2, default=0, max_digits=10)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('staff_member', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='efficiency_metrics', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'ordering': ['-date'],
                'unique_together': {('date', 'staff_member')},
            },
        ),
    ]
