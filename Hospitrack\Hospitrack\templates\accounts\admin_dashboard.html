{% extends 'base.html' %}

{% block title %}Admin Dashboard - HospiTrack{% endblock %}

{% block content %}
<div class="space-y-6">
    <!-- Header -->
    <div class="bg-white shadow rounded-lg p-6">
        <h1 class="text-3xl font-bold text-gray-900">Admin Dashboard</h1>
        <p class="text-gray-600 mt-2">Welcome back, {{ user.first_name }}! Here's your hospital overview.</p>
    </div>

    <!-- Statistics Cards -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <!-- Total Patients -->
        <div class="bg-white overflow-hidden shadow rounded-lg">
            <div class="p-5">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <i class="fas fa-users text-3xl text-blue-500"></i>
                    </div>
                    <div class="ml-5 w-0 flex-1">
                        <dl>
                            <dt class="text-sm font-medium text-gray-500 truncate">Total Patients</dt>
                            <dd class="text-3xl font-bold text-gray-900">{{ total_patients }}</dd>
                        </dl>
                    </div>
                </div>
            </div>
            <div class="bg-gray-50 px-5 py-3">
                <div class="text-sm">
                    <a href="{% url 'patients:list' %}" class="font-medium text-blue-700 hover:text-blue-900">
                        View all patients
                    </a>
                </div>
            </div>
        </div>

        <!-- Total Doctors -->
        <div class="bg-white overflow-hidden shadow rounded-lg">
            <div class="p-5">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <i class="fas fa-user-md text-3xl text-green-500"></i>
                    </div>
                    <div class="ml-5 w-0 flex-1">
                        <dl>
                            <dt class="text-sm font-medium text-gray-500 truncate">Total Doctors</dt>
                            <dd class="text-3xl font-bold text-gray-900">{{ total_doctors }}</dd>
                        </dl>
                    </div>
                </div>
            </div>
            <div class="bg-gray-50 px-5 py-3">
                <div class="text-sm">
                    <a href="{% url 'doctors_list' %}" class="font-medium text-green-700 hover:text-green-900">
                        View doctors
                    </a>
                </div>
            </div>
        </div>

        <!-- Total Nurses -->
        <div class="bg-white overflow-hidden shadow rounded-lg">
            <div class="p-5">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <i class="fas fa-user-nurse text-3xl text-purple-500"></i>
                    </div>
                    <div class="ml-5 w-0 flex-1">
                        <dl>
                            <dt class="text-sm font-medium text-gray-500 truncate">Total Nurses</dt>
                            <dd class="text-3xl font-bold text-gray-900">{{ total_nurses }}</dd>
                        </dl>
                    </div>
                </div>
            </div>
            <div class="bg-gray-50 px-5 py-3">
                <div class="text-sm">
                    <a href="{% url 'nurses_list' %}" class="font-medium text-purple-700 hover:text-purple-900">
                        View nurses
                    </a>
                </div>
            </div>
        </div>

        <!-- Today's Appointments -->
        <div class="bg-white overflow-hidden shadow rounded-lg">
            <div class="p-5">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <i class="fas fa-calendar-check text-3xl text-blue-500"></i>
                    </div>
                    <div class="ml-5 w-0 flex-1">
                        <dl>
                            <dt class="text-sm font-medium text-gray-500 truncate">Today's Appointments</dt>
                            <dd class="text-3xl font-bold text-gray-900">{{ today_appointments }}</dd>
                        </dl>
                    </div>
                </div>
            </div>
            <div class="bg-gray-50 px-5 py-3">
                <div class="text-sm">
                    <a href="{% url 'appointments:list' %}" class="font-medium text-blue-700 hover:text-blue-900">
                        View appointments
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Additional Statistics -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">


        <!-- Total Beds -->
        <div class="bg-white overflow-hidden shadow rounded-lg">
            <div class="p-5">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <i class="fas fa-bed text-3xl text-orange-500"></i>
                    </div>
                    <div class="ml-5 w-0 flex-1">
                        <dl>
                            <dt class="text-sm font-medium text-gray-500 truncate">Total Beds</dt>
                            <dd class="text-3xl font-bold text-gray-900">{{ total_beds }}</dd>
                        </dl>
                    </div>
                </div>
            </div>
            <div class="bg-gray-50 px-5 py-3">
                <div class="text-sm">
                    <a href="{% url 'beds:management' %}" class="font-medium text-orange-700 hover:text-orange-900">
                        Manage beds
                    </a>
                </div>
            </div>
        </div>

        <!-- Available Beds -->
        <div class="bg-white overflow-hidden shadow rounded-lg">
            <div class="p-5">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <i class="fas fa-check-circle text-3xl text-green-500"></i>
                    </div>
                    <div class="ml-5 w-0 flex-1">
                        <dl>
                            <dt class="text-sm font-medium text-gray-500 truncate">Available Beds</dt>
                            <dd class="text-3xl font-bold text-green-600">{{ available_beds }}</dd>
                        </dl>
                    </div>
                </div>
            </div>
        </div>

        <!-- Occupied Beds -->
        <div class="bg-white overflow-hidden shadow rounded-lg">
            <div class="p-5">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <i class="fas fa-user-injured text-3xl text-red-500"></i>
                    </div>
                    <div class="ml-5 w-0 flex-1">
                        <dl>
                            <dt class="text-sm font-medium text-gray-500 truncate">Occupied Beds</dt>
                            <dd class="text-3xl font-bold text-red-600">{{ occupied_beds }}</dd>
                        </dl>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Bed Management Overview -->
    <div class="bg-white shadow rounded-lg p-6">
        <h2 class="text-xl font-bold text-gray-900 mb-4">Bed Management Overview</h2>
        <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
            <div class="text-center p-4 bg-green-50 rounded-lg">
                <div class="text-2xl font-bold text-green-600">{{ available_beds }}</div>
                <div class="text-sm text-green-800">Available</div>
            </div>
            <div class="text-center p-4 bg-red-50 rounded-lg">
                <div class="text-2xl font-bold text-red-600">{{ occupied_beds }}</div>
                <div class="text-sm text-red-800">Occupied</div>
            </div>
            <div class="text-center p-4 bg-yellow-50 rounded-lg">
                <div class="text-2xl font-bold text-yellow-600">{{ maintenance_beds }}</div>
                <div class="text-sm text-yellow-800">Maintenance</div>
            </div>
            <div class="text-center p-4 bg-blue-50 rounded-lg">
                <div class="text-2xl font-bold text-blue-600">{{ emergency_beds }}</div>
                <div class="text-sm text-blue-800">Emergency</div>
            </div>
        </div>
    </div>

    <!-- Quick Actions -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        <!-- Patient Registration -->
        <div class="bg-white shadow rounded-lg p-6">
            <h3 class="text-lg font-medium text-gray-900 mb-4">Patient Management</h3>
            <div class="space-y-3">
                <a href="{% url 'patients:register' %}" class="block w-full bg-blue-600 hover:bg-blue-700 text-white text-center py-2 px-4 rounded-md text-sm font-medium">
                    <i class="fas fa-user-plus mr-2"></i>Register New Patient
                </a>
                <a href="{% url 'patients:list' %}" class="block w-full bg-gray-600 hover:bg-gray-700 text-white text-center py-2 px-4 rounded-md text-sm font-medium">
                    <i class="fas fa-list mr-2"></i>View All Patients
                </a>
            </div>
        </div>

        <!-- Appointment Management -->
        <div class="bg-white shadow rounded-lg p-6">
            <h3 class="text-lg font-medium text-gray-900 mb-4">Appointment Management</h3>
            <div class="space-y-3">
                <a href="{% url 'appointments:schedule' %}" class="block w-full bg-green-600 hover:bg-green-700 text-white text-center py-2 px-4 rounded-md text-sm font-medium">
                    <i class="fas fa-calendar-plus mr-2"></i>Schedule Appointment
                </a>
                <a href="{% url 'appointments:opd_management' %}" class="block w-full bg-purple-600 hover:bg-purple-700 text-white text-center py-2 px-4 rounded-md text-sm font-medium">
                    <i class="fas fa-stethoscope mr-2"></i>OPD Management
                </a>
                <a href="{% url 'patients:admission_management' %}" class="block w-full bg-orange-600 hover:bg-orange-700 text-white text-center py-2 px-4 rounded-md text-sm font-medium">
                    <i class="fas fa-bed mr-2"></i>Hospital Admissions
                </a>
            </div>
        </div>

        <!-- Staff Management -->
        <div class="bg-white shadow rounded-lg p-6">
            <h3 class="text-lg font-medium text-gray-900 mb-4">Staff Management</h3>
            <div class="space-y-3">
                <a href="{% url 'doctor_create' %}" class="block w-full bg-green-600 hover:bg-green-700 text-white text-center py-2 px-4 rounded-md text-sm font-medium">
                    <i class="fas fa-user-md mr-2"></i>Add New Doctor
                </a>
                <a href="{% url 'nurse_create' %}" class="block w-full bg-purple-600 hover:bg-purple-700 text-white text-center py-2 px-4 rounded-md text-sm font-medium">
                    <i class="fas fa-user-nurse mr-2"></i>Add New Nurse
                </a>
                <a href="{% url 'doctors_list' %}" class="block w-full bg-gray-600 hover:bg-gray-700 text-white text-center py-2 px-4 rounded-md text-sm font-medium">
                    <i class="fas fa-users mr-2"></i>View All Doctors
                </a>
                <a href="{% url 'nurses_list' %}" class="block w-full bg-gray-500 hover:bg-gray-600 text-white text-center py-2 px-4 rounded-md text-sm font-medium">
                    <i class="fas fa-users mr-2"></i>View All Nurses
                </a>
            </div>
        </div>
    </div>

    <!-- Recent Patients -->
    <div class="bg-white shadow rounded-lg p-6">
        <div class="flex justify-between items-center mb-4">
            <h2 class="text-xl font-bold text-gray-900">Patient Search & Recent Registrations</h2>
            <div class="flex items-center space-x-3">
                {% if search_query %}
                <span class="text-sm text-green-600 font-medium">{{ recent_patients|length }} result(s) for "{{ search_query }}"</span>
                {% else %}
                <span class="text-sm text-gray-500">{{ recent_patients|length }} of {{ total_patients }} patients</span>
                {% endif %}
                <a href="{% url 'patients:list' %}" class="text-sm text-blue-600 hover:text-blue-800">View All →</a>
            </div>
        </div>

        <!-- Search Form -->
        <div class="mb-4">
            <form method="GET" action="" class="flex items-center space-x-3">
                <div class="flex-1">
                    <input type="text" name="search" value="{{ search_query|default:'' }}"
                           placeholder="Search patients by ID, name, phone, or email..."
                           class="w-full px-4 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500">
                </div>
                <button type="submit" class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500">
                    <i class="fas fa-search mr-1"></i>Search
                </button>
                {% if search_query %}
                <a href="{% url 'admin_dashboard' %}" class="px-4 py-2 bg-gray-500 text-white rounded-md hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-gray-500">
                    <i class="fas fa-times mr-1"></i>Clear
                </a>
                {% endif %}
            </form>
        </div>

        {% if recent_patients %}
        <div class="overflow-x-auto">
            <table class="min-w-full divide-y divide-gray-200">
                <thead class="bg-gray-50">
                    <tr>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Patient ID</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Name</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Age</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Phone</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Registered</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                    </tr>
                </thead>
                <tbody class="bg-white divide-y divide-gray-200">
                    {% for patient in recent_patients %}
                    <tr class="{% if search_query %}bg-green-50 border-l-4 border-green-400 hover:bg-green-100{% else %}hover:bg-gray-50{% endif %}">
                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium {% if search_query %}text-green-900{% else %}text-gray-900{% endif %}">
                            {{ patient.patient_id }}
                            {% if search_query %}
                            <span class="ml-2 inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-green-100 text-green-800">
                                <i class="fas fa-search mr-1"></i>Match
                            </span>
                            {% endif %}
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm {% if search_query %}text-green-900 font-medium{% else %}text-gray-900{% endif %}">{{ patient.full_name }}</td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm {% if search_query %}text-green-900{% else %}text-gray-900{% endif %}">{{ patient.age }}</td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm {% if search_query %}text-green-900{% else %}text-gray-900{% endif %}">{{ patient.phone }}</td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm {% if search_query %}text-green-700{% else %}text-gray-500{% endif %}">{{ patient.created_at|date:"M d, Y" }}</td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                            <a href="{% url 'patients:detail' patient.id %}" class="{% if search_query %}text-green-600 hover:text-green-900{% else %}text-blue-600 hover:text-blue-900{% endif %}">
                                <i class="fas fa-eye mr-1"></i>View
                            </a>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
        {% else %}
        {% if search_query %}
        <!-- No search results -->
        <div class="text-center py-8">
            <div class="inline-flex items-center justify-center w-16 h-16 bg-gray-100 rounded-full mb-4">
                <i class="fas fa-search text-2xl text-gray-400"></i>
            </div>
            <h3 class="text-lg font-medium text-gray-900 mb-2">No patients found</h3>
            <p class="text-gray-500 mb-4">No patients match your search for "{{ search_query }}"</p>
            <a href="?" class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700">
                <i class="fas fa-arrow-left mr-2"></i>
                Show All Patients
            </a>
        </div>
        {% else %}
        <!-- No patients at all -->
        <div class="text-center py-8">
            <div class="inline-flex items-center justify-center w-16 h-16 bg-gray-100 rounded-full mb-4">
                <i class="fas fa-user-plus text-2xl text-gray-400"></i>
            </div>
            <h3 class="text-lg font-medium text-gray-900 mb-2">No patients registered</h3>
            <p class="text-gray-500 mb-4">No patients have been registered yet.</p>
            <a href="{% url 'patients:register' %}" class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-green-600 hover:bg-green-700">
                <i class="fas fa-user-plus mr-2"></i>
                Register First Patient
            </a>
        </div>
        {% endif %}
        {% endif %}
    </div>
</div>

<script>
// Auto-submit search form on Enter key
document.addEventListener('DOMContentLoaded', function() {
    const searchInput = document.querySelector('input[name="search"]');
    if (searchInput) {
        searchInput.addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                e.preventDefault();
                this.form.submit();
            }
        });
    }
});
</script>
{% endblock %}
