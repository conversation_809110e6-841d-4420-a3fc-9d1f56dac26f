{% extends 'base.html' %}

{% block title %}Add Update for {{ patient.full_name }} - HospiTrack{% endblock %}

{% block content %}
<div class="max-w-3xl mx-auto space-y-6">
    <!-- Header -->
    <div class="bg-white shadow rounded-lg p-6">
        <div class="flex items-center justify-between">
            <div>
                <h1 class="text-3xl font-bold text-gray-900">Add Patient Update</h1>
                <p class="text-gray-600 mt-2">{{ patient.full_name }} ({{ patient.patient_id }})</p>
                <div class="mt-2 flex items-center space-x-4 text-sm text-gray-500">
                    <span>{{ patient.age }} years old</span>
                    <span>{{ patient.get_gender_display }}</span>
                    <span>Blood Group: {{ patient.blood_group }}</span>
                </div>
            </div>
            <a href="{% url 'patients:detail' patient.id %}" class="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
                <i class="fas fa-arrow-left mr-2"></i>
                Back to Patient
            </a>
        </div>
    </div>

    <!-- Update Form -->
    <div class="bg-white shadow rounded-lg p-6">
        <form method="post" class="space-y-6">
            {% csrf_token %}
            
            <div>
                <label for="{{ form.update_text.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-2">
                    Patient Update <span class="text-red-500">*</span>
                </label>
                <div class="text-sm text-gray-500 mb-2">
                    Describe the patient's current condition, treatment progress, or any observations.
                </div>
                {{ form.update_text }}
                {% if form.update_text.errors %}
                    <p class="mt-1 text-sm text-red-600">{{ form.update_text.errors.0 }}</p>
                {% endif %}
            </div>

            <div>
                <label for="{{ form.vital_signs.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-2">
                    Vital Signs
                </label>
                <div class="text-sm text-gray-500 mb-2">
                    Record vital signs (e.g., BP: 120/80, Temp: 98.6°F, Pulse: 72, Resp: 16)
                </div>
                {{ form.vital_signs }}
                {% if form.vital_signs.errors %}
                    <p class="mt-1 text-sm text-red-600">{{ form.vital_signs.errors.0 }}</p>
                {% endif %}
            </div>

            <div>
                <label for="{{ form.medications_given.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-2">
                    Medications Given
                </label>
                <div class="text-sm text-gray-500 mb-2">
                    List any medications administered, dosage, and time.
                </div>
                {{ form.medications_given }}
                {% if form.medications_given.errors %}
                    <p class="mt-1 text-sm text-red-600">{{ form.medications_given.errors.0 }}</p>
                {% endif %}
            </div>

            <div>
                <label for="{{ form.notes.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-2">
                    Additional Notes
                </label>
                <div class="text-sm text-gray-500 mb-2">
                    Any additional observations, patient complaints, or care instructions.
                </div>
                {{ form.notes }}
                {% if form.notes.errors %}
                    <p class="mt-1 text-sm text-red-600">{{ form.notes.errors.0 }}</p>
                {% endif %}
            </div>

            <!-- Quick Vital Signs Templates -->
            <div class="bg-gray-50 p-4 rounded-lg">
                <h3 class="text-sm font-medium text-gray-900 mb-3">Quick Templates</h3>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-3">
                    <button type="button" onclick="fillVitals('BP: 120/80, Temp: 98.6°F, Pulse: 72, Resp: 16')" 
                            class="text-left p-2 bg-white border border-gray-200 rounded text-sm hover:bg-gray-50">
                        <strong>Normal Vitals:</strong> BP: 120/80, Temp: 98.6°F, Pulse: 72, Resp: 16
                    </button>
                    <button type="button" onclick="fillUpdate('Patient is stable and responding well to treatment.')" 
                            class="text-left p-2 bg-white border border-gray-200 rounded text-sm hover:bg-gray-50">
                        <strong>Stable:</strong> Patient is stable and responding well to treatment.
                    </button>
                    <button type="button" onclick="fillUpdate('Patient reports feeling better. Pain level decreased.')" 
                            class="text-left p-2 bg-white border border-gray-200 rounded text-sm hover:bg-gray-50">
                        <strong>Improving:</strong> Patient reports feeling better. Pain level decreased.
                    </button>
                    <button type="button" onclick="fillMedications('Paracetamol 500mg - 1 tablet, Administered at ' + new Date().toLocaleTimeString())" 
                            class="text-left p-2 bg-white border border-gray-200 rounded text-sm hover:bg-gray-50">
                        <strong>Pain Relief:</strong> Paracetamol 500mg administered
                    </button>
                </div>
            </div>

            <!-- Form Actions -->
            <div class="flex justify-end space-x-4 pt-6 border-t border-gray-200">
                <a href="{% url 'patients:detail' patient.id %}" class="px-6 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
                    Cancel
                </a>
                <button type="submit" class="px-6 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-green-600 hover:bg-green-700">
                    <i class="fas fa-save mr-2"></i>
                    Save Update
                </button>
            </div>
        </form>
    </div>

    <!-- Recent Updates -->
    {% if patient.updates.all %}
    <div class="bg-white shadow rounded-lg p-6">
        <h2 class="text-lg font-medium text-gray-900 mb-4">Recent Updates</h2>
        <div class="space-y-4">
            {% for update in patient.updates.all|slice:":5" %}
            <div class="border-l-4 border-blue-400 pl-4 py-2">
                <div class="flex justify-between items-start">
                    <div class="flex-1">
                        <p class="text-sm text-gray-900">{{ update.update_text }}</p>
                        {% if update.vital_signs %}
                        <p class="text-xs text-gray-600 mt-1">
                            <strong>Vitals:</strong> {{ update.vital_signs }}
                        </p>
                        {% endif %}
                        {% if update.medications_given %}
                        <p class="text-xs text-gray-600 mt-1">
                            <strong>Medications:</strong> {{ update.medications_given }}
                        </p>
                        {% endif %}
                        <div class="text-xs text-gray-500 mt-2">
                            By {{ update.updated_by.get_full_name }} on {{ update.created_at|date:"M d, Y H:i" }}
                        </div>
                    </div>
                </div>
            </div>
            {% endfor %}
        </div>
    </div>
    {% endif %}
</div>

<style>
/* Custom styles for form fields */
.form-control {
    @apply w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-1 focus:ring-green-500 focus:border-green-500;
}

textarea.form-control {
    @apply resize-vertical;
}
</style>

<script>
function fillVitals(text) {
    document.getElementById('{{ form.vital_signs.id_for_label }}').value = text;
}

function fillUpdate(text) {
    document.getElementById('{{ form.update_text.id_for_label }}').value = text;
}

function fillMedications(text) {
    document.getElementById('{{ form.medications_given.id_for_label }}').value = text;
}

// Auto-save draft functionality
let draftTimer;
function saveDraft() {
    clearTimeout(draftTimer);
    draftTimer = setTimeout(() => {
        const formData = {
            update_text: document.getElementById('{{ form.update_text.id_for_label }}').value,
            vital_signs: document.getElementById('{{ form.vital_signs.id_for_label }}').value,
            medications_given: document.getElementById('{{ form.medications_given.id_for_label }}').value,
            notes: document.getElementById('{{ form.notes.id_for_label }}').value
        };
        localStorage.setItem('patient_update_draft_{{ patient.id }}', JSON.stringify(formData));
    }, 2000);
}

// Load draft on page load
window.addEventListener('load', () => {
    const draft = localStorage.getItem('patient_update_draft_{{ patient.id }}');
    if (draft) {
        const data = JSON.parse(draft);
        if (data.update_text) document.getElementById('{{ form.update_text.id_for_label }}').value = data.update_text;
        if (data.vital_signs) document.getElementById('{{ form.vital_signs.id_for_label }}').value = data.vital_signs;
        if (data.medications_given) document.getElementById('{{ form.medications_given.id_for_label }}').value = data.medications_given;
        if (data.notes) document.getElementById('{{ form.notes.id_for_label }}').value = data.notes;
    }
});

// Add event listeners for auto-save
document.addEventListener('DOMContentLoaded', () => {
    const inputs = ['{{ form.update_text.id_for_label }}', '{{ form.vital_signs.id_for_label }}', '{{ form.medications_given.id_for_label }}', '{{ form.notes.id_for_label }}'];
    inputs.forEach(id => {
        const element = document.getElementById(id);
        if (element) {
            element.addEventListener('input', saveDraft);
        }
    });
});

// Clear draft on successful submit
document.querySelector('form').addEventListener('submit', () => {
    localStorage.removeItem('patient_update_draft_{{ patient.id }}');
});
</script>
{% endblock %}
