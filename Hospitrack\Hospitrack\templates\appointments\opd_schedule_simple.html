{% extends 'base.html' %}

{% block title %}OPD Management - HospiTrack{% endblock %}

{% block content %}
<div class="space-y-6">
    <!-- Header -->
    <div class="bg-white shadow rounded-lg p-6">
        <div class="flex justify-between items-center">
            <div>
                <h1 class="text-3xl font-bold text-gray-900">OPD Management</h1>
                <p class="text-gray-600 mt-2">Daily Outpatient Appointments - {{ today|date:"F d, Y" }}</p>
            </div>
            <div class="flex space-x-3">
                <a href="{% url 'appointments:opd_registration' %}" class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-green-600 hover:bg-green-700">
                    <i class="fas fa-user-plus mr-2"></i>
                    New Patient Registration
                </a>
                <a href="{% url 'appointments:opd_queue' %}" class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700">
                    <i class="fas fa-list mr-2"></i>
                    View Queue
                </a>
            </div>
        </div>
    </div>

    <!-- Today's Summary -->
    <div class="bg-white shadow rounded-lg p-6">
        <div class="flex items-center justify-between mb-4">
            <h2 class="text-lg font-medium text-gray-900">Today's Appointments Summary</h2>
            <span class="px-3 py-1 bg-blue-100 text-blue-800 rounded-full text-sm font-medium">
                Total: {{ total_appointments }}
            </span>
        </div>
        
        {% if total_appointments > 0 %}
        <div class="text-sm text-gray-600">
            <p>{{ total_appointments }} patient{{ total_appointments|pluralize }} registered for OPD consultation today</p>
        </div>
        {% else %}
        <div class="text-center py-8">
            <i class="fas fa-calendar-day text-4xl text-gray-400 mb-4"></i>
            <h3 class="text-lg font-medium text-gray-900 mb-2">No Appointments Today</h3>
            <p class="text-gray-500 mb-4">No patients have registered for OPD consultation today.</p>
            <a href="{% url 'appointments:opd_registration' %}" class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-green-600 hover:bg-green-700">
                <i class="fas fa-user-plus mr-2"></i>
                Register First Patient
            </a>
        </div>
        {% endif %}
    </div>

    <!-- Doctor-wise Appointments -->
    {% if doctor_appointments %}
    <div class="space-y-4">
        {% for doctor_name, data in doctor_appointments.items %}
        <div class="bg-white shadow rounded-lg p-6">
            <div class="flex items-center justify-between mb-4">
                <div>
                    <h3 class="text-lg font-medium text-gray-900">Dr. {{ doctor_name }}</h3>
                    <p class="text-sm text-gray-600">{{ data.doctor.specialization }}</p>
                </div>
                <span class="px-3 py-1 bg-green-100 text-green-800 rounded-full text-sm font-medium">
                    {{ data.appointments|length }} patient{{ data.appointments|length|pluralize }}
                </span>
            </div>
            
            <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-gray-200">
                    <thead class="bg-gray-50">
                        <tr>
                            <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">OPD #</th>
                            <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Patient Name</th>
                            <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Age</th>
                            <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Chief Complaint</th>
                            <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Appointment Time</th>
                            <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                            <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200">
                        {% for appointment in data.appointments %}
                        <tr class="hover:bg-gray-50 {% if appointment.is_emergency %}bg-red-50{% endif %}">
                            <td class="px-4 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                                <div class="text-sm font-medium text-gray-900">{{ appointment.opd_number }}</div>
                                <div class="text-xs text-gray-500">Queue: {{ appointment.queue_number }}
                                {% if appointment.is_emergency %}
                                <span class="ml-1 text-red-500" title="Emergency">⚡</span>
                                {% endif %}
                                </div>
                            </td>
                            <td class="px-4 py-4 whitespace-nowrap">
                                <div class="text-sm font-medium text-gray-900">{{ appointment.patient_name }}</div>
                                <div class="text-xs text-gray-500">{{ appointment.phone }}</div>
                                <div class="text-xs text-gray-400">Registered: {{ appointment.registration_time|time:"g:i A" }}</div>
                            </td>
                            <td class="px-4 py-4 whitespace-nowrap text-sm text-gray-900">
                                {{ appointment.age }}Y / {{ appointment.get_gender_display }}
                            </td>
                            <td class="px-4 py-4 text-sm text-gray-900">
                                <div class="max-w-xs truncate" title="{{ appointment.chief_complaint }}">
                                    {{ appointment.chief_complaint }}
                                </div>
                            </td>
                            <td class="px-4 py-4 whitespace-nowrap text-sm text-gray-500">
                                {{ appointment.get_preferred_time_display }}
                            </td>
                            <td class="px-4 py-4 whitespace-nowrap">
                                <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full
                                    {% if appointment.status == 'registered' %}bg-blue-100 text-blue-800
                                    {% elif appointment.status == 'waiting' %}bg-yellow-100 text-yellow-800
                                    {% elif appointment.status == 'consulting' %}bg-green-100 text-green-800
                                    {% elif appointment.status == 'completed' %}bg-purple-100 text-purple-800
                                    {% else %}bg-gray-100 text-gray-800{% endif %}">
                                    {{ appointment.get_status_display }}
                                </span>
                            </td>
                            <td class="px-4 py-4 whitespace-nowrap text-sm font-medium">
                                <div class="flex space-x-2">
                                    <a href="{% url 'appointments:opd_detail' appointment.id %}" class="text-blue-600 hover:text-blue-900 text-xs">
                                        View Details
                                    </a>
                                    <a href="{% url 'appointments:opd_edit' appointment.id %}" class="text-green-600 hover:text-green-900 text-xs">
                                        Edit
                                    </a>
                                    {% if appointment.status != 'cancelled' and appointment.status != 'completed' %}
                                    <a href="{% url 'appointments:opd_cancel' appointment.id %}" class="text-orange-600 hover:text-orange-900 text-xs">
                                        Cancel
                                    </a>
                                    {% endif %}
                                    {% if user.user_type == 'admin' %}
                                    <a href="{% url 'appointments:opd_remove' appointment.id %}" class="text-red-600 hover:text-red-900 text-xs">
                                        Remove
                                    </a>
                                    {% endif %}
                                </div>
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        </div>
        {% endfor %}
    </div>
    {% endif %}

    <!-- Available Doctors -->
    <div class="bg-white shadow rounded-lg p-6">
        <h2 class="text-lg font-medium text-gray-900 mb-4">Available Specialist Doctors</h2>
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {% for doctor in all_doctors %}
            <div class="border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow">
                <div class="flex items-center justify-between">
                    <div>
                        <h3 class="text-sm font-medium text-gray-900">Dr. {{ doctor.user.get_full_name }}</h3>
                        <p class="text-xs text-gray-600">{{ doctor.specialization }}</p>
                        <p class="text-xs text-green-600 font-medium">₹{{ doctor.consultation_fee }}</p>
                    </div>
                    <div class="text-right">
                        <span class="text-xs text-gray-400">Available</span>
                    </div>
                </div>
            </div>
            {% endfor %}
        </div>
    </div>

    <!-- Quick Actions -->
    <div class="bg-blue-50 border border-blue-200 rounded-lg p-4">
        <div class="flex">
            <div class="flex-shrink-0">
                <i class="fas fa-info-circle text-blue-400"></i>
            </div>
            <div class="ml-3">
                <h3 class="text-sm font-medium text-blue-800">Quick Actions</h3>
                <div class="mt-2 text-sm text-blue-700">
                    <ul class="list-disc list-inside space-y-1">
                        <li><strong>Register New Patient:</strong> Click "New Patient Registration" to add walk-in patients</li>
                        <li><strong>View Queue:</strong> Click "View Queue" to see real-time patient status</li>
                        <li><strong>Emergency Cases:</strong> Emergency patients get priority in the queue</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
