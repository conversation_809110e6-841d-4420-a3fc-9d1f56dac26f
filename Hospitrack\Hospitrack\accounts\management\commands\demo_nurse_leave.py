from django.core.management.base import BaseCommand
from django.db import transaction
from accounts.models import Nurse
from datetime import date, timedelta
import random

class Command(BaseCommand):
    help = 'Set some nurses on leave for demonstration purposes'

    def handle(self, *args, **options):
        self.stdout.write(self.style.SUCCESS('Setting up nurse leave demonstration...'))
        
        with transaction.atomic():
            nurses = list(Nurse.objects.all())
            
            if len(nurses) < 3:
                self.stdout.write(self.style.WARNING('Not enough nurses to demonstrate leave system'))
                return
            
            # Set some nurses on different types of leave
            leave_scenarios = [
                {
                    'status': 'sick_leave',
                    'reason': 'Flu symptoms - doctor advised rest',
                    'days': 3
                },
                {
                    'status': 'vacation',
                    'reason': 'Annual vacation with family',
                    'days': 7
                },
                {
                    'status': 'emergency_leave',
                    'reason': 'Family emergency',
                    'days': 2
                }
            ]
            
            # Select random nurses for leave
            selected_nurses = random.sample(nurses, min(3, len(nurses)))
            
            for i, nurse in enumerate(selected_nurses):
                if i < len(leave_scenarios):
                    scenario = leave_scenarios[i]
                    
                    # Set leave dates
                    start_date = date.today() - timedelta(days=random.randint(0, 2))
                    end_date = start_date + timedelta(days=scenario['days'])
                    
                    nurse.leave_status = scenario['status']
                    nurse.leave_start_date = start_date
                    nurse.leave_end_date = end_date
                    nurse.leave_reason = scenario['reason']
                    nurse.save()
                    
                    self.stdout.write(
                        f'Set {nurse.user.get_full_name()} on {scenario["status"]} '
                        f'from {start_date} to {end_date}'
                    )
            
            # Show summary
            available_count = Nurse.objects.filter(leave_status='available').count()
            on_leave_count = Nurse.objects.exclude(leave_status='available').count()
            
            self.stdout.write(
                self.style.SUCCESS(
                    f'Nurse leave demonstration setup complete:\n'
                    f'- Available nurses: {available_count}\n'
                    f'- Nurses on leave: {on_leave_count}'
                )
            )