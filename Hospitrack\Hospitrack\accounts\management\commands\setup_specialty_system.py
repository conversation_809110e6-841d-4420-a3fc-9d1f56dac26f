from django.core.management.base import BaseCommand
from django.db import transaction
from accounts.models import MedicalSpecialty, Doctor, Nurse

class Command(BaseCommand):
    help = 'Set up the specialty-based access control system'

    def handle(self, *args, **options):
        self.stdout.write(self.style.SUCCESS('Setting up specialty-based access control system...'))
        
        with transaction.atomic():
            # Create medical specialties
            specialties_data = [
                {'name': 'Cardiology', 'description': 'Heart and cardiovascular system'},
                {'name': 'Nephrology', 'description': 'Kidney and urinary system'},
                {'name': 'Pulmonology', 'description': 'Lungs and respiratory system'},
                {'name': 'Hepatology', 'description': 'Liver and hepatic system'},
                {'name': 'Orthopedics', 'description': 'Bones, joints, and musculoskeletal system'},
                {'name': 'Pediatrics', 'description': 'Children and adolescent medicine'},
                {'name': 'ENT', 'description': 'Ear, Nose, and Throat'},
                {'name': 'Neurosurgery', 'description': 'Brain and nervous system surgery'},
                {'name': 'Neurology', 'description': 'Brain and nervous system'},
                {'name': 'Psychiatry', 'description': 'Mental health and psychiatric disorders'},
                {'name': 'General Medicine', 'description': 'General medical care'},
                {'name': 'Emergency Medicine', 'description': 'Emergency and critical care'},
                {'name': 'Surgery', 'description': 'General surgical procedures'},
            ]
            
            created_specialties = {}
            for specialty_data in specialties_data:
                specialty, created = MedicalSpecialty.objects.get_or_create(
                    name=specialty_data['name'],
                    defaults={'description': specialty_data['description']}
                )
                created_specialties[specialty.name] = specialty
                if created:
                    self.stdout.write(f'Created specialty: {specialty.name}')
                else:
                    self.stdout.write(f'Specialty already exists: {specialty.name}')
            
            # Map existing doctor specializations to new specialty system
            specialty_mapping = {
                'Cardiologist': 'Cardiology',
                'Nephrologist': 'Nephrology',
                'Pulmonologist': 'Pulmonology',
                'Hepatologist': 'Hepatology',
                'Orthopedist': 'Orthopedics',
                'Pediatrician': 'Pediatrics',
                'ENT Specialist': 'ENT',
                'Neurosurgeon': 'Neurosurgery',
                'Neurologist': 'Neurology',
                'Psychiatrist': 'Psychiatry',
                'General Medicine': 'General Medicine',
                'Emergency Medicine': 'Emergency Medicine',
                'Surgery': 'Surgery',
            }
            
            # Update existing doctors
            doctors_updated = 0
            for doctor in Doctor.objects.all():
                if not doctor.specialty_id:  # Only update if specialty not already set
                    specialty_name = specialty_mapping.get(doctor.specialization)
                    if specialty_name and specialty_name in created_specialties:
                        doctor.specialty = created_specialties[specialty_name]
                        doctor.save()
                        doctors_updated += 1
                        self.stdout.write(f'Updated doctor {doctor.user.get_full_name()} with specialty {specialty_name}')
            
            # Update existing nurses based on their department
            nurses_updated = 0
            for nurse in Nurse.objects.all():
                if not nurse.specialty_id:  # Only update if specialty not already set
                    # Map nurse departments to specialties
                    department_mapping = {
                        'Orthopedics': 'Orthopedics',
                        'Cardiology': 'Cardiology',
                        'General Medicine': 'General Medicine',
                        'Emergency': 'Emergency Medicine',
                        'Pediatrics': 'Pediatrics',
                        'Surgery': 'Surgery',
                    }
                    
                    specialty_name = department_mapping.get(nurse.department)
                    if specialty_name and specialty_name in created_specialties:
                        nurse.specialty = created_specialties[specialty_name]
                        nurse.save()
                        nurses_updated += 1
                        self.stdout.write(f'Updated nurse {nurse.user.get_full_name()} with specialty {specialty_name}')
            
            self.stdout.write(
                self.style.SUCCESS(
                    f'Successfully set up specialty system:\n'
                    f'- Created {len(created_specialties)} specialties\n'
                    f'- Updated {doctors_updated} doctors\n'
                    f'- Updated {nurses_updated} nurses'
                )
            )