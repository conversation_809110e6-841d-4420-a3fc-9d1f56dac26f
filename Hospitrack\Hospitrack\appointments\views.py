from django.shortcuts import render, redirect, get_object_or_404
from django.contrib.auth.decorators import login_required
from django.contrib import messages
from datetime import date
from .models import Appointment, OPDSchedule, PatientOPDSchedule, OPDRegistration

@login_required
def appointment_list(request):
    """List all appointments"""
    appointments = Appointment.objects.all().order_by('-appointment_date', '-appointment_time')
    return render(request, 'appointments/list.html', {'appointments': appointments})

@login_required
def appointment_create(request):
    """Create a new appointment"""
    if request.user.user_type not in ['admin', 'doctor']:
        messages.error(request, 'Access denied. Admin or Doctor privileges required.')
        return redirect('appointments:list')

    if request.method == 'POST':
        try:
            from datetime import datetime
            from patients.models import Patient
            from accounts.models import Doctor

            # Get form data
            patient_id = request.POST.get('patient')
            doctor_id = request.POST.get('doctor')
            appointment_date = request.POST.get('appointment_date')
            appointment_time = request.POST.get('appointment_time')
            appointment_type = request.POST.get('appointment_type', 'consultation')
            reason = request.POST.get('reason', '')
            notes = request.POST.get('notes', '')

            # Validate required fields
            if not all([patient_id, doctor_id, appointment_date, appointment_time]):
                messages.error(request, 'Please fill in all required fields.')
                return render(request, 'appointments/create.html', get_appointment_context())

            # Get patient and doctor objects
            patient = Patient.objects.get(id=patient_id)
            doctor = Doctor.objects.get(user_id=doctor_id)

            # Parse date and time
            appointment_datetime = datetime.strptime(f"{appointment_date} {appointment_time}", "%Y-%m-%d %H:%M")

            # Check for conflicting appointments
            existing_appointment = Appointment.objects.filter(
                doctor=doctor,
                appointment_date=appointment_datetime.date(),
                appointment_time=appointment_datetime.time(),
                status__in=['scheduled', 'in_progress']
            ).first()

            if existing_appointment:
                messages.error(request, f'Dr. {doctor.user.get_full_name()} already has an appointment at this time.')
                return render(request, 'appointments/create.html', get_appointment_context())

            # Generate appointment ID
            import random
            import string
            appointment_id = f"APT{random.randint(1000, 9999)}"
            while Appointment.objects.filter(appointment_id=appointment_id).exists():
                appointment_id = f"APT{random.randint(1000, 9999)}"

            # Create appointment
            appointment = Appointment.objects.create(
                appointment_id=appointment_id,
                patient=patient,
                doctor=doctor,
                appointment_date=appointment_datetime.date(),
                appointment_time=appointment_datetime.time(),
                appointment_type=appointment_type,
                reason=reason,
                notes=notes,
                status='scheduled',
                created_by=request.user
            )

            messages.success(request, f'Appointment scheduled successfully for {patient.full_name} with Dr. {doctor.user.get_full_name()} on {appointment_datetime.strftime("%B %d, %Y at %I:%M %p")}.')
            return redirect('appointments:list')

        except Patient.DoesNotExist:
            messages.error(request, 'Selected patient not found.')
        except Doctor.DoesNotExist:
            messages.error(request, 'Selected doctor not found.')
        except ValueError as e:
            messages.error(request, 'Invalid date or time format.')
        except Exception as e:
            messages.error(request, f'Error creating appointment: {str(e)}')

    return render(request, 'appointments/create.html', get_appointment_context())

def get_appointment_context():
    """Get context data for appointment forms"""
    from patients.models import Patient
    from accounts.models import Doctor

    return {
        'patients': Patient.objects.filter(is_active=True).order_by('first_name', 'last_name'),
        'doctors': Doctor.objects.all().order_by('user__first_name', 'user__last_name'),
        'appointment_types': [
            ('consultation', 'Consultation'),
            ('follow_up', 'Follow-up'),
            ('emergency', 'Emergency'),
            ('surgery', 'Surgery'),
            ('checkup', 'Regular Checkup'),
        ]
    }

@login_required
def appointment_schedule(request):
    """View appointment schedule"""
    from datetime import datetime, time
    from accounts.models import Doctor

    current_date = datetime.now().date()

    # Get today's appointments
    today_appointments = Appointment.objects.filter(appointment_date=current_date)
    pending_appointments = today_appointments.filter(status='scheduled')
    completed_appointments = today_appointments.filter(status='completed')

    # Get available doctors
    available_doctors = Doctor.objects.all()

    # Generate time slots
    morning_slots = []
    afternoon_slots = []
    evening_slots = []

    # Morning slots (9 AM - 12 PM)
    for hour in range(9, 12):
        for minute in [0, 30]:
            slot_time = time(hour, minute)
            slot_str = slot_time.strftime("%I:%M %p")

            # Check if slot is booked
            appointment = today_appointments.filter(appointment_time=slot_time).first()
            if appointment:
                morning_slots.append({
                    'time': slot_str,
                    'is_booked': True,
                    'patient_name': appointment.patient.get_full_name(),
                    'doctor_name': appointment.doctor.user.get_full_name() if appointment.doctor else 'N/A'
                })
            else:
                morning_slots.append({
                    'time': slot_str,
                    'is_booked': False,
                    'patient_name': '',
                    'doctor_name': ''
                })

    # Afternoon slots (1 PM - 5 PM)
    for hour in range(13, 17):
        for minute in [0, 30]:
            slot_time = time(hour, minute)
            slot_str = slot_time.strftime("%I:%M %p")

            appointment = today_appointments.filter(appointment_time=slot_time).first()
            if appointment:
                afternoon_slots.append({
                    'time': slot_str,
                    'is_booked': True,
                    'patient_name': appointment.patient.get_full_name(),
                    'doctor_name': appointment.doctor.user.get_full_name() if appointment.doctor else 'N/A'
                })
            else:
                afternoon_slots.append({
                    'time': slot_str,
                    'is_booked': False,
                    'patient_name': '',
                    'doctor_name': ''
                })

    # Evening slots (6 PM - 9 PM)
    for hour in range(18, 21):
        for minute in [0, 30]:
            slot_time = time(hour, minute)
            slot_str = slot_time.strftime("%I:%M %p")

            appointment = today_appointments.filter(appointment_time=slot_time).first()
            if appointment:
                evening_slots.append({
                    'time': slot_str,
                    'is_booked': True,
                    'patient_name': appointment.patient.get_full_name(),
                    'doctor_name': appointment.doctor.user.get_full_name() if appointment.doctor else 'N/A'
                })
            else:
                evening_slots.append({
                    'time': slot_str,
                    'is_booked': False,
                    'patient_name': '',
                    'doctor_name': ''
                })

    context = {
        'current_date': current_date,
        'today_appointments': today_appointments,
        'pending_appointments': pending_appointments,
        'completed_appointments': completed_appointments,
        'available_doctors': available_doctors,
        'morning_slots': morning_slots,
        'afternoon_slots': afternoon_slots,
        'evening_slots': evening_slots,
    }

    return render(request, 'appointments/schedule.html', context)

@login_required
def opd_schedule(request):
    """View daily OPD appointments - Simple view"""
    from datetime import date

    today = date.today()

    # Get today's OPD registrations only
    today_appointments = OPDRegistration.objects.filter(
        registration_date=today
    ).select_related('doctor__user').order_by('doctor', 'queue_number')

    # Group appointments by doctor
    doctor_appointments = {}
    for appointment in today_appointments:
        doctor_name = appointment.doctor.user.get_full_name()
        if doctor_name not in doctor_appointments:
            doctor_appointments[doctor_name] = {
                'doctor': appointment.doctor,
                'appointments': []
            }
        doctor_appointments[doctor_name]['appointments'].append(appointment)

    # Get all doctors for new appointments
    from accounts.models import Doctor
    all_doctors = Doctor.objects.all().order_by('specialization', 'user__first_name')

    context = {
        'today': today,
        'doctor_appointments': doctor_appointments,
        'all_doctors': all_doctors,
        'total_appointments': today_appointments.count(),
    }

    return render(request, 'appointments/opd_schedule_simple.html', context)

@login_required
def opd_schedule_create(request):
    """Create OPD schedule for patients (Admin only)"""
    if request.user.user_type != 'admin':
        messages.error(request, 'Access denied. Admin privileges required.')
        return redirect('appointments:list')

    if request.method == 'POST':
        try:
            from datetime import datetime
            from patients.models import Patient
            from accounts.models import Doctor
            from .models import PatientOPDSchedule

            # Get form data
            patient_id = request.POST.get('patient')
            doctor_id = request.POST.get('doctor')
            schedule_date = request.POST.get('schedule_date')
            time_slot = request.POST.get('time_slot')
            reason = request.POST.get('reason', '')
            special_instructions = request.POST.get('special_instructions', '')
            is_recurring = request.POST.get('is_recurring') == 'on'
            recurring_weeks = int(request.POST.get('recurring_weeks', 1))

            # Validate required fields
            if not all([patient_id, doctor_id, schedule_date, time_slot, reason]):
                messages.error(request, 'Please fill in all required fields.')
                return render(request, 'appointments/opd_schedule_create.html', get_opd_context())

            # Get patient and doctor objects
            patient = Patient.objects.get(id=patient_id)
            doctor = Doctor.objects.get(user_id=doctor_id)

            # Parse date
            schedule_date_obj = datetime.strptime(schedule_date, "%Y-%m-%d").date()

            # Check for conflicting schedules
            existing_schedule = PatientOPDSchedule.objects.filter(
                doctor=doctor,
                schedule_date=schedule_date_obj,
                time_slot=time_slot,
                status__in=['scheduled', 'in_progress']
            ).first()

            if existing_schedule:
                messages.error(request, f'Dr. {doctor.user.get_full_name()} already has an OPD schedule at this time.')
                return render(request, 'appointments/opd_schedule_create.html', get_opd_context())

            # Generate schedule ID
            import random
            schedule_id = f"OPD{random.randint(1000, 9999)}"
            while PatientOPDSchedule.objects.filter(schedule_id=schedule_id).exists():
                schedule_id = f"OPD{random.randint(1000, 9999)}"

            # Create OPD schedule
            opd_schedule = PatientOPDSchedule.objects.create(
                schedule_id=schedule_id,
                patient=patient,
                doctor=doctor,
                schedule_date=schedule_date_obj,
                time_slot=time_slot,
                reason=reason,
                special_instructions=special_instructions,
                is_recurring=is_recurring,
                recurring_weeks=recurring_weeks,
                status='scheduled',
                created_by=request.user
            )

            # Create recurring schedules if requested
            if is_recurring and recurring_weeks > 1:
                from datetime import timedelta
                for week in range(1, recurring_weeks):
                    next_date = schedule_date_obj + timedelta(weeks=week)
                    next_schedule_id = f"OPD{random.randint(1000, 9999)}"
                    while PatientOPDSchedule.objects.filter(schedule_id=next_schedule_id).exists():
                        next_schedule_id = f"OPD{random.randint(1000, 9999)}"

                    PatientOPDSchedule.objects.create(
                        schedule_id=next_schedule_id,
                        patient=patient,
                        doctor=doctor,
                        schedule_date=next_date,
                        time_slot=time_slot,
                        reason=reason,
                        special_instructions=special_instructions,
                        is_recurring=True,
                        recurring_weeks=1,
                        status='scheduled',
                        created_by=request.user
                    )

            success_msg = f'OPD schedule created successfully for {patient.full_name} with Dr. {doctor.user.get_full_name()}'
            if is_recurring and recurring_weeks > 1:
                success_msg += f' for {recurring_weeks} weeks'
            messages.success(request, success_msg)
            return redirect('appointments:opd_schedule')

        except Patient.DoesNotExist:
            messages.error(request, 'Selected patient not found.')
        except Doctor.DoesNotExist:
            messages.error(request, 'Selected doctor not found.')
        except ValueError as e:
            messages.error(request, 'Invalid date format.')
        except Exception as e:
            messages.error(request, f'Error creating OPD schedule: {str(e)}')

    return render(request, 'appointments/opd_schedule_create.html', get_opd_context())

@login_required
def opd_registration(request):
    """OPD Registration for walk-in patients - Admin only"""
    # Check if user is admin
    if request.user.user_type != 'admin':
        messages.error(request, 'Only administrators can register patients for OPD.')
        return redirect('appointments:opd_queue')

    if request.method == 'POST':
        try:
            from datetime import datetime
            from accounts.models import Doctor
            from .models import OPDRegistration

            # Debug: Print received data
            print("POST data received:", dict(request.POST))

            # Get form data
            patient_name = request.POST.get('patient_name', '').strip()
            age = request.POST.get('age', '').strip()
            gender = request.POST.get('gender', '').strip()
            phone = request.POST.get('phone', '').strip()
            address = request.POST.get('address', '').strip()
            doctor_id = request.POST.get('doctor', '').strip()
            visit_type = request.POST.get('visit_type', 'new').strip()
            chief_complaint = request.POST.get('chief_complaint', '').strip()
            symptoms = request.POST.get('symptoms', '').strip()
            preferred_time = request.POST.get('preferred_time', '').strip()
            is_emergency = request.POST.get('is_emergency') == 'on'
            payment_method = request.POST.get('payment_method', 'cash').strip()

            # Validate required fields
            missing_fields = []
            if not patient_name: missing_fields.append('Patient Name')
            if not age: missing_fields.append('Age')
            if not gender: missing_fields.append('Gender')
            if not phone: missing_fields.append('Phone')
            if not address: missing_fields.append('Address')
            if not doctor_id: missing_fields.append('Doctor')
            if not chief_complaint: missing_fields.append('Chief Complaint')
            if not preferred_time: missing_fields.append('Preferred Time')

            if missing_fields:
                error_msg = f'Please fill in the following required fields: {", ".join(missing_fields)}'
                print(f"Validation error: {error_msg}")
                print(f"Doctor ID received: '{doctor_id}'")
                messages.error(request, error_msg)
                return render(request, 'appointments/opd_registration.html', get_opd_registration_context())

            # Get doctor
            try:
                doctor = Doctor.objects.get(user_id=int(doctor_id))
                print(f"Doctor found: {doctor.user.get_full_name()}")
            except (Doctor.DoesNotExist, ValueError):
                messages.error(request, 'Selected doctor not found. Please select a valid doctor.')
                return render(request, 'appointments/opd_registration.html', get_opd_registration_context())

            # Validate age
            try:
                age_int = int(age)
                if age_int < 0 or age_int > 120:
                    messages.error(request, 'Age must be between 0 and 120 years.')
                    return render(request, 'appointments/opd_registration.html', get_opd_registration_context())
            except ValueError:
                messages.error(request, 'Please enter a valid age.')
                return render(request, 'appointments/opd_registration.html', get_opd_registration_context())

            # Create OPD registration
            opd_registration = OPDRegistration.objects.create(
                patient_name=patient_name,
                age=age_int,
                gender=gender,
                phone=phone,
                address=address,
                doctor=doctor,
                visit_type=visit_type,
                chief_complaint=chief_complaint,
                symptoms=symptoms,
                preferred_time=preferred_time,
                is_emergency=is_emergency,
                payment_method=payment_method,
                status='registered',
                created_by=request.user
            )

            print(f"OPD Registration created successfully: {opd_registration.opd_number}")
            messages.success(request, f'OPD Registration successful! OPD Number: {opd_registration.opd_number}. Queue Number: {opd_registration.queue_number}. Please proceed to payment counter.')
            return redirect('appointments:opd_queue')

        except Doctor.DoesNotExist:
            messages.error(request, 'Selected doctor not found.')
        except ValueError as e:
            messages.error(request, 'Invalid age format.')
        except Exception as e:
            messages.error(request, f'Error in OPD registration: {str(e)}')

    return render(request, 'appointments/opd_registration.html', get_opd_registration_context())

@login_required
def opd_queue(request):
    """View OPD queue for today"""
    from datetime import date
    from .models import OPDRegistration

    today = date.today()

    # Get today's OPD registrations - filter by doctor if user is a doctor
    if request.user.user_type == 'doctor':
        # Get the doctor instance for the current user
        try:
            from accounts.models import Doctor
            doctor = Doctor.objects.get(user=request.user)
            opd_registrations = OPDRegistration.objects.filter(
                registration_date=today,
                doctor=doctor
            ).order_by('queue_number')
        except Doctor.DoesNotExist:
            opd_registrations = OPDRegistration.objects.none()
    else:
        # Admin or other users can see all OPD registrations
        opd_registrations = OPDRegistration.objects.filter(registration_date=today).order_by('queue_number')

    # Group by doctor
    doctors_queue = {}
    for registration in opd_registrations:
        doctor_name = registration.doctor.user.get_full_name()
        if doctor_name not in doctors_queue:
            doctors_queue[doctor_name] = {
                'doctor': registration.doctor,
                'registrations': [],
                'waiting': 0,
                'consulting': 0,
                'completed': 0
            }
        doctors_queue[doctor_name]['registrations'].append(registration)

        if registration.status == 'waiting':
            doctors_queue[doctor_name]['waiting'] += 1
        elif registration.status == 'consulting':
            doctors_queue[doctor_name]['consulting'] += 1
        elif registration.status == 'completed':
            doctors_queue[doctor_name]['completed'] += 1

    context = {
        'today': today,
        'opd_registrations': opd_registrations,
        'doctors_queue': doctors_queue,
        'total_registrations': opd_registrations.count(),
        'waiting_count': opd_registrations.filter(status='waiting').count(),
        'consulting_count': opd_registrations.filter(status='consulting').count(),
        'completed_count': opd_registrations.filter(status='completed').count(),
    }

    return render(request, 'appointments/opd_queue.html', context)

@login_required
def opd_management(request):
    """Simplified OPD Management - Admin only"""
    # Check if user is admin
    if request.user.user_type != 'admin':
        messages.error(request, 'Only administrators can access OPD management.')
        return redirect('dashboard')

    from datetime import date
    from .models import OPDRegistration
    from accounts.models import Doctor

    today = date.today()

    # Get today's OPD registrations
    opd_registrations = OPDRegistration.objects.filter(registration_date=today).order_by('queue_number')

    # Get all doctors for the registration form
    doctors = Doctor.objects.all()

    context = {
        'today': today,
        'opd_registrations': opd_registrations,
        'doctors': doctors,
        'total_registrations': opd_registrations.count(),
        'waiting_count': opd_registrations.filter(status='waiting').count(),
        'consulting_count': opd_registrations.filter(status='consulting').count(),
        'completed_count': opd_registrations.filter(status='completed').count(),
    }

    return render(request, 'appointments/opd_management.html', context)

@login_required
def opd_detail(request, registration_id):
    """View OPD registration details"""
    from django.shortcuts import get_object_or_404

    registration = get_object_or_404(OPDRegistration, id=registration_id)

    context = {
        'registration': registration,
    }

    return render(request, 'appointments/opd_detail.html', context)

@login_required
def opd_edit(request, registration_id):
    """Edit OPD registration details"""
    from django.shortcuts import get_object_or_404

    registration = get_object_or_404(OPDRegistration, id=registration_id)

    if request.method == 'POST':
        try:
            # Update registration details
            registration.patient_name = request.POST.get('patient_name', '').strip()
            registration.age = int(request.POST.get('age', 0))
            registration.gender = request.POST.get('gender', '').strip()
            registration.phone = request.POST.get('phone', '').strip()
            registration.address = request.POST.get('address', '').strip()
            registration.chief_complaint = request.POST.get('chief_complaint', '').strip()
            registration.symptoms = request.POST.get('symptoms', '').strip()
            registration.preferred_time = request.POST.get('preferred_time', '').strip()
            registration.visit_type = request.POST.get('visit_type', 'new')
            registration.is_emergency = request.POST.get('is_emergency') == 'on'
            registration.payment_method = request.POST.get('payment_method', 'cash')

            # Validate required fields
            if not all([registration.patient_name, registration.age, registration.gender,
                       registration.phone, registration.address, registration.chief_complaint,
                       registration.preferred_time]):
                messages.error(request, 'Please fill in all required fields.')
                return render(request, 'appointments/opd_edit.html', {
                    'registration': registration,
                    **get_opd_registration_context()
                })

            registration.save()
            messages.success(request, f'✅ OPD registration {registration.opd_number} updated successfully!')
            return redirect('appointments:opd_detail', registration_id=registration.id)

        except ValueError:
            messages.error(request, 'Invalid age format.')
        except Exception as e:
            messages.error(request, f'Error updating registration: {str(e)}')

    context = {
        'registration': registration,
        **get_opd_registration_context()
    }

    return render(request, 'appointments/opd_edit.html', context)

@login_required
def opd_cancel(request, registration_id):
    """Cancel OPD registration"""
    from django.shortcuts import get_object_or_404

    registration = get_object_or_404(OPDRegistration, id=registration_id)

    if request.method == 'POST':
        # Update status to cancelled
        registration.status = 'cancelled'
        registration.save()

        messages.success(request, f'✅ OPD registration {registration.opd_number} has been cancelled successfully!')
        return redirect('appointments:opd_schedule')

    context = {
        'registration': registration,
    }

    return render(request, 'appointments/opd_cancel.html', context)

@login_required
def opd_remove(request, registration_id):
    """Completely remove OPD registration from the system"""
    from django.shortcuts import get_object_or_404

    # Only admin users can remove registrations
    if request.user.user_type != 'admin':
        messages.error(request, 'Access denied. Admin privileges required.')
        return redirect('appointments:opd_schedule')

    registration = get_object_or_404(OPDRegistration, id=registration_id)

    if request.method == 'POST':
        # Store info for success message before deletion
        opd_number = registration.opd_number
        patient_name = registration.patient_name

        # Delete the OPD registration (this does NOT delete the patient record)
        registration.delete()

        messages.success(request, f'✅ OPD registration {opd_number} for {patient_name} has been completely removed from the system!')
        return redirect('appointments:opd_schedule')

    context = {
        'registration': registration,
    }

    return render(request, 'appointments/opd_remove.html', context)

def get_opd_registration_context():
    """Get context data for OPD registration"""
    from accounts.models import Doctor

    return {
        'doctors': Doctor.objects.all().order_by('specialization', 'user__first_name'),
        'time_slots': PatientOPDSchedule.TIME_SLOT_CHOICES,
        'visit_types': OPDRegistration.VISIT_TYPE_CHOICES,
        'gender_choices': OPDRegistration.GENDER_CHOICES,
        'payment_methods': [
            ('cash', 'Cash'),
            ('card', 'Card'),
            ('upi', 'UPI'),
            ('insurance', 'Insurance'),
        ]
    }

def get_opd_context():
    """Get context data for OPD schedule forms"""
    from patients.models import Patient
    from accounts.models import Doctor

    return {
        'patients': Patient.objects.filter(is_active=True).order_by('first_name', 'last_name'),
        'doctors': Doctor.objects.all().order_by('specialization', 'user__first_name'),
        'time_slots': PatientOPDSchedule.TIME_SLOT_CHOICES,
    }

@login_required
def appointment_detail(request, appointment_id):
    """View appointment details"""
    appointment = get_object_or_404(Appointment, id=appointment_id)
    return render(request, 'appointments/detail.html', {'appointment': appointment})

@login_required
def appointment_edit(request, appointment_id):
    """Edit appointment"""
    appointment = get_object_or_404(Appointment, id=appointment_id)
    return render(request, 'appointments/edit.html', {'appointment': appointment})

@login_required
def opd_schedule_list(request):
    """List OPD schedules"""
    schedules = OPDSchedule.objects.filter(is_active=True).order_by('weekday', 'start_time')
    return render(request, 'appointments/opd_schedule.html', {'schedules': schedules})

@login_required
def opd_schedule_create(request):
    """Create OPD schedule"""
    return render(request, 'appointments/opd_schedule_create.html')

@login_required
def today_appointments(request):
    """Today's appointments for doctors"""
    if request.user.user_type == 'doctor':
        from accounts.models import Doctor
        try:
            doctor = Doctor.objects.get(user=request.user)
            appointments = Appointment.objects.filter(
                doctor=doctor,
                appointment_date=date.today()
            ).order_by('appointment_time')
        except Doctor.DoesNotExist:
            appointments = []
    else:
        appointments = Appointment.objects.filter(
            appointment_date=date.today()
        ).order_by('appointment_time')

    return render(request, 'appointments/today.html', {'appointments': appointments})
