from django.core.management.base import BaseCommand
from django.contrib.auth import get_user_model
from accounts.models import Nurse, MedicalSpecialty

User = get_user_model()

class Command(BaseCommand):
    help = 'Create nurses for each specialty department'

    def handle(self, *args, **options):
        self.stdout.write('Creating specialty nurses...')
        
        # Nurse data organized by specialty
        nurses_data = {
            'Emergency Medicine': [
                {'username': 'emma.johnson1', 'first_name': '<PERSON>', 'last_name': '<PERSON>', 'shift': 'morning'},
                {'username': 'liam.williams2', 'first_name': '<PERSON>', 'last_name': '<PERSON>', 'shift': 'evening'},
                {'username': 'olivia.brown3', 'first_name': '<PERSON>', 'last_name': '<PERSON>', 'shift': 'night'},
                {'username': 'noah.davis4', 'first_name': '<PERSON>', 'last_name': '<PERSON>', 'shift': 'morning'},
                {'username': 'ava.miller5', 'first_name': '<PERSON>', 'last_name': '<PERSON>', 'shift': 'evening'},
                {'username': 'ethan.wilson6', 'first_name': '<PERSON>', 'last_name': '<PERSON>', 'shift': 'night'},
                {'username': 'sophia.moore7', 'first_name': 'Sophia', 'last_name': '<PERSON>', 'shift': 'morning'},
                {'username': 'mason.taylor8', 'first_name': '<PERSON>', 'last_name': '<PERSON>', 'shift': 'evening'},
                {'username': 'charlotte.anderson9', 'first_name': 'Charlotte', 'last_name': 'Anderson', 'shift': 'night'},
                {'username': 'logan.thomas10', 'first_name': 'Logan', 'last_name': 'Thomas', 'shift': 'morning'},
            ],
            'Oncology': [
                {'username': 'amelia.jackson11', 'first_name': 'Amelia', 'last_name': 'Jackson', 'shift': 'morning'},
                {'username': 'lucas.white12', 'first_name': 'Lucas', 'last_name': 'White', 'shift': 'evening'},
                {'username': 'harper.harris13', 'first_name': 'Harper', 'last_name': 'Harris', 'shift': 'night'},
                {'username': 'elijah.martin14', 'first_name': 'Elijah', 'last_name': 'Martin', 'shift': 'morning'},
                {'username': 'evelyn.thompson15', 'first_name': 'Evelyn', 'last_name': 'Thompson', 'shift': 'evening'},
                {'username': 'james.garcia16', 'first_name': 'James', 'last_name': 'Garcia', 'shift': 'night'},
                {'username': 'abigail.martinez17', 'first_name': 'Abigail', 'last_name': 'Martinez', 'shift': 'morning'},
                {'username': 'benjamin.robinson18', 'first_name': 'Benjamin', 'last_name': 'Robinson', 'shift': 'evening'},
                {'username': 'emily.clark19', 'first_name': 'Emily', 'last_name': 'Clark', 'shift': 'night'},
                {'username': 'alexander.rodriguez20', 'first_name': 'Alexander', 'last_name': 'Rodriguez', 'shift': 'morning'},
            ],
            'Anesthesiology': [
                {'username': 'elizabeth.lewis21', 'first_name': 'Elizabeth', 'last_name': 'Lewis', 'shift': 'morning'},
                {'username': 'michael.lee22', 'first_name': 'Michael', 'last_name': 'Lee', 'shift': 'evening'},
                {'username': 'mia.walker23', 'first_name': 'Mia', 'last_name': 'Walker', 'shift': 'night'},
                {'username': 'daniel.hall24', 'first_name': 'Daniel', 'last_name': 'Hall', 'shift': 'morning'},
                {'username': 'grace.allen25', 'first_name': 'Grace', 'last_name': 'Allen', 'shift': 'evening'},
                {'username': 'matthew.young26', 'first_name': 'Matthew', 'last_name': 'Young', 'shift': 'night'},
                {'username': 'chloe.hernandez27', 'first_name': 'Chloe', 'last_name': 'Hernandez', 'shift': 'morning'},
                {'username': 'andrew.king28', 'first_name': 'Andrew', 'last_name': 'King', 'shift': 'evening'},
                {'username': 'victoria.wright29', 'first_name': 'Victoria', 'last_name': 'Wright', 'shift': 'night'},
                {'username': 'joshua.lopez30', 'first_name': 'Joshua', 'last_name': 'Lopez', 'shift': 'morning'},
            ],
            'Dermatology': [
                {'username': 'madison.hill31', 'first_name': 'Madison', 'last_name': 'Hill', 'shift': 'morning'},
                {'username': 'christopher.scott32', 'first_name': 'Christopher', 'last_name': 'Scott', 'shift': 'evening'},
                {'username': 'zoe.green33', 'first_name': 'Zoe', 'last_name': 'Green', 'shift': 'night'},
                {'username': 'ryan.adams34', 'first_name': 'Ryan', 'last_name': 'Adams', 'shift': 'morning'},
                {'username': 'lily.baker35', 'first_name': 'Lily', 'last_name': 'Baker', 'shift': 'evening'},
                {'username': 'nathan.gonzalez36', 'first_name': 'Nathan', 'last_name': 'Gonzalez', 'shift': 'night'},
                {'username': 'hannah.nelson37', 'first_name': 'Hannah', 'last_name': 'Nelson', 'shift': 'morning'},
                {'username': 'tyler.carter38', 'first_name': 'Tyler', 'last_name': 'Carter', 'shift': 'evening'},
                {'username': 'aria.mitchell39', 'first_name': 'Aria', 'last_name': 'Mitchell', 'shift': 'night'},
                {'username': 'caleb.perez40', 'first_name': 'Caleb', 'last_name': 'Perez', 'shift': 'morning'},
            ],
            'Radiology': [
                {'username': 'layla.roberts41', 'first_name': 'Layla', 'last_name': 'Roberts', 'shift': 'morning'},
                {'username': 'isaac.turner42', 'first_name': 'Isaac', 'last_name': 'Turner', 'shift': 'evening'},
                {'username': 'nora.phillips43', 'first_name': 'Nora', 'last_name': 'Phillips', 'shift': 'night'},
                {'username': 'gabriel.campbell44', 'first_name': 'Gabriel', 'last_name': 'Campbell', 'shift': 'morning'},
                {'username': 'stella.parker45', 'first_name': 'Stella', 'last_name': 'Parker', 'shift': 'evening'},
                {'username': 'owen.evans46', 'first_name': 'Owen', 'last_name': 'Evans', 'shift': 'night'},
                {'username': 'aurora.edwards47', 'first_name': 'Aurora', 'last_name': 'Edwards', 'shift': 'morning'},
                {'username': 'levi.collins48', 'first_name': 'Levi', 'last_name': 'Collins', 'shift': 'evening'},
                {'username': 'hazel.stewart49', 'first_name': 'Hazel', 'last_name': 'Stewart', 'shift': 'night'},
                {'username': 'wyatt.sanchez50', 'first_name': 'Wyatt', 'last_name': 'Sanchez', 'shift': 'morning'},
            ]
        }
        
        created_count = 0
        for specialty_name, nurses in nurses_data.items():
            try:
                specialty = MedicalSpecialty.objects.get(name=specialty_name)
                self.stdout.write(f'\nCreating nurses for {specialty_name}:')
                
                for i, nurse_data in enumerate(nurses, 1):
                    try:
                        # Check if user already exists
                        if User.objects.filter(username=nurse_data['username']).exists():
                            self.stdout.write(f'  - Nurse {nurse_data["username"]} already exists')
                            continue
                        
                        # Create user account
                        user = User.objects.create_user(
                            username=nurse_data['username'],
                            email=f'{nurse_data["username"]}@hospitrack.com',
                            password='nurse123',
                            first_name=nurse_data['first_name'],
                            last_name=nurse_data['last_name'],
                            user_type='nurse'
                        )
                        
                        # Create nurse profile
                        nurse = Nurse.objects.create(
                            user=user,
                            specialty=specialty,
                            department=specialty.name,  # For backward compatibility
                            shift=nurse_data['shift'],
                            license_number=f'{specialty_name[:4].upper()}{i:03d}',
                            leave_status='available'
                        )
                        
                        created_count += 1
                        self.stdout.write(f'  ✓ Created: {user.get_full_name()} - {nurse.shift} shift')
                        
                    except Exception as e:
                        self.stdout.write(f'  ✗ Error creating {nurse_data["username"]}: {str(e)}')
                        
            except MedicalSpecialty.DoesNotExist:
                self.stdout.write(f'✗ Specialty {specialty_name} not found')
        
        self.stdout.write(f'\n✅ Created {created_count} specialty nurses.')
        self.stdout.write(f'📊 Total nurses: {Nurse.objects.count()}')
        self.stdout.write(f'🔑 All nurses password: nurse123')