from django.contrib import admin
from .models import (
    AnalyticsMetric, PatientFlowMetrics, StaffEfficiencyMetrics,
    BedUtilizationMetrics, FinancialMetrics, QualityMetrics,
    PredictiveModel, AlertRule, Alert
)

@admin.register(AnalyticsMetric)
class AnalyticsMetricAdmin(admin.ModelAdmin):
    list_display = ['name', 'metric_type', 'value', 'unit', 'frequency', 'date_recorded', 'department']
    list_filter = ['metric_type', 'frequency', 'date_recorded', 'department', 'specialty']
    search_fields = ['name', 'department']
    date_hierarchy = 'date_recorded'
    ordering = ['-date_recorded']

@admin.register(PatientFlowMetrics)
class PatientFlowMetricsAdmin(admin.ModelAdmin):
    list_display = ['date', 'hour', 'total_admissions', 'total_discharges', 'occupancy_rate', 'department']
    list_filter = ['date', 'department', 'specialty']
    date_hierarchy = 'date'
    ordering = ['-date', '-hour']

@admin.register(StaffEfficiencyMetrics)
class StaffEfficiencyMetricsAdmin(admin.ModelAdmin):
    list_display = ['staff_member', 'date', 'patients_seen', 'appointments_completed', 'revenue_generated']
    list_filter = ['date', 'staff_member__user_type']
    search_fields = ['staff_member__first_name', 'staff_member__last_name']
    date_hierarchy = 'date'
    ordering = ['-date']

@admin.register(BedUtilizationMetrics)
class BedUtilizationMetricsAdmin(admin.ModelAdmin):
    list_display = ['ward', 'date', 'total_beds', 'occupied_beds', 'utilization_rate', 'total_revenue']
    list_filter = ['date', 'ward']
    date_hierarchy = 'date'
    ordering = ['-date']

@admin.register(FinancialMetrics)
class FinancialMetricsAdmin(admin.ModelAdmin):
    list_display = ['date', 'total_revenue', 'total_costs', 'net_profit', 'profit_margin', 'department']
    list_filter = ['date', 'department', 'specialty']
    date_hierarchy = 'date'
    ordering = ['-date']

@admin.register(QualityMetrics)
class QualityMetricsAdmin(admin.ModelAdmin):
    list_display = ['date', 'average_satisfaction_score', 'readmission_rate', 'average_wait_time', 'department']
    list_filter = ['date', 'department', 'specialty']
    date_hierarchy = 'date'
    ordering = ['-date']

@admin.register(PredictiveModel)
class PredictiveModelAdmin(admin.ModelAdmin):
    list_display = ['name', 'model_type', 'prediction_date', 'target_date', 'predicted_value', 'confidence_score']
    list_filter = ['model_type', 'prediction_date', 'model_version']
    search_fields = ['name']
    date_hierarchy = 'prediction_date'
    ordering = ['-prediction_date']

@admin.register(AlertRule)
class AlertRuleAdmin(admin.ModelAdmin):
    list_display = ['name', 'alert_type', 'severity', 'metric_name', 'condition', 'threshold_value', 'is_active']
    list_filter = ['alert_type', 'severity', 'is_active']
    search_fields = ['name', 'metric_name']
    filter_horizontal = ['notify_users']

@admin.register(Alert)
class AlertAdmin(admin.ModelAdmin):
    list_display = ['rule', 'triggered_at', 'metric_value', 'is_acknowledged', 'is_resolved']
    list_filter = ['triggered_at', 'is_acknowledged', 'is_resolved', 'rule__severity']
    search_fields = ['rule__name', 'message']
    date_hierarchy = 'triggered_at'
    ordering = ['-triggered_at']
    readonly_fields = ['triggered_at']
