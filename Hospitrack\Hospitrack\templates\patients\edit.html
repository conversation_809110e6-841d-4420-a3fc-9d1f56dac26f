{% extends 'base.html' %}

{% block title %}Edit {{ patient.full_name }} - HospiTrack{% endblock %}

{% block content %}
<div class="max-w-4xl mx-auto space-y-6">
    <!-- Header -->
    <div class="bg-white shadow rounded-lg p-6">
        <div class="flex items-center justify-between">
            <div>
                <h1 class="text-3xl font-bold text-gray-900">Edit Patient Information</h1>
                <p class="text-gray-600 mt-2">{{ patient.full_name }} ({{ patient.patient_id }})</p>
            </div>
            <div class="flex space-x-3">
                <a href="{% url 'patients:detail' patient.id %}" class="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
                    <i class="fas fa-arrow-left mr-2"></i>
                    Back to Details
                </a>
            </div>
        </div>
    </div>

    <!-- Edit Form -->
    <div class="bg-white shadow rounded-lg p-6">
        <form method="post" class="space-y-6">
            {% csrf_token %}
            
            <!-- Personal Information -->
            <div>
                <h3 class="text-lg font-medium text-gray-900 mb-4">Personal Information</h3>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">
                            Patient ID <span class="text-red-500">*</span>
                        </label>
                        <input type="text" name="patient_id" value="{{ patient.patient_id }}" required class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500">
                    </div>
                    <div></div>

                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">
                            First Name <span class="text-red-500">*</span>
                        </label>
                        <input type="text" name="first_name" value="{{ patient.first_name }}" required class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500">
                    </div>

                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">
                            Last Name <span class="text-red-500">*</span>
                        </label>
                        <input type="text" name="last_name" value="{{ patient.last_name }}" required class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500">
                    </div>
                    
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">
                            Date of Birth <span class="text-red-500">*</span>
                        </label>
                        <input type="date" name="date_of_birth" value="{{ patient.date_of_birth|date:'Y-m-d' }}" required class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500">
                    </div>

                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">
                            Gender <span class="text-red-500">*</span>
                        </label>
                        <select name="gender" required class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500 bg-white">
                            <option value="">Select Gender</option>
                            <option value="M" {% if patient.gender == 'M' %}selected{% endif %}>Male</option>
                            <option value="F" {% if patient.gender == 'F' %}selected{% endif %}>Female</option>
                            <option value="O" {% if patient.gender == 'O' %}selected{% endif %}>Other</option>
                        </select>
                    </div>

                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">
                            Blood Group <span class="text-red-500">*</span>
                        </label>
                        <select name="blood_group" required class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500 bg-white">
                            <option value="">Select Blood Group</option>
                            <option value="A+" {% if patient.blood_group == 'A+' %}selected{% endif %}>A+</option>
                            <option value="A-" {% if patient.blood_group == 'A-' %}selected{% endif %}>A-</option>
                            <option value="B+" {% if patient.blood_group == 'B+' %}selected{% endif %}>B+</option>
                            <option value="B-" {% if patient.blood_group == 'B-' %}selected{% endif %}>B-</option>
                            <option value="AB+" {% if patient.blood_group == 'AB+' %}selected{% endif %}>AB+</option>
                            <option value="AB-" {% if patient.blood_group == 'AB-' %}selected{% endif %}>AB-</option>
                            <option value="O+" {% if patient.blood_group == 'O+' %}selected{% endif %}>O+</option>
                            <option value="O-" {% if patient.blood_group == 'O-' %}selected{% endif %}>O-</option>
                        </select>
                    </div>
                </div>
            </div>

            <!-- Contact Information -->
            <div>
                <h3 class="text-lg font-medium text-gray-900 mb-4">Contact Information</h3>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">
                            Phone Number <span class="text-red-500">*</span>
                        </label>
                        <input type="tel" name="phone" value="{{ patient.phone }}" required class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500">
                    </div>

                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">
                            Email Address
                        </label>
                        <input type="email" name="email" value="{{ patient.email }}" class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500">
                    </div>
                </div>

                <div class="mt-6">
                    <label class="block text-sm font-medium text-gray-700 mb-2">
                        Address <span class="text-red-500">*</span>
                    </label>
                    <textarea name="address" rows="3" required class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500 resize-vertical">{{ patient.address }}</textarea>
                </div>
            </div>

            <!-- Emergency Contact -->
            <div>
                <h3 class="text-lg font-medium text-gray-900 mb-4">Emergency Contact</h3>
                <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">
                            Contact Name <span class="text-red-500">*</span>
                        </label>
                        <input type="text" name="emergency_contact_name" value="{{ patient.emergency_contact_name }}" required class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500">
                    </div>

                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">
                            Contact Phone <span class="text-red-500">*</span>
                        </label>
                        <input type="tel" name="emergency_contact_phone" value="{{ patient.emergency_contact_phone }}" required class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500">
                    </div>

                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">
                            Relationship <span class="text-red-500">*</span>
                        </label>
                        <input type="text" name="emergency_contact_relation" value="{{ patient.emergency_contact_relation }}" required class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500">
                    </div>
                </div>
            </div>

            <!-- Medical Information -->
            <div>
                <h3 class="text-lg font-medium text-gray-900 mb-4">Medical Information</h3>
                <div class="space-y-6">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">
                            Medical History
                        </label>
                        <textarea name="medical_history" rows="3" class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500 resize-vertical">{{ patient.medical_history }}</textarea>
                    </div>

                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">
                            Allergies
                        </label>
                        <textarea name="allergies" rows="2" class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500 resize-vertical">{{ patient.allergies }}</textarea>
                    </div>

                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">
                            Current Medications
                        </label>
                        <textarea name="current_medications" rows="2" class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500 resize-vertical">{{ patient.current_medications }}</textarea>
                    </div>
                    
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">
                            Insurance Number
                        </label>
                        <input type="text" name="insurance_number" value="{{ patient.insurance_number }}" class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500">
                    </div>
                </div>
            </div>

            <!-- Form Actions -->
            <div class="flex justify-end space-x-4 pt-6 border-t border-gray-200">
                <a href="{% url 'patients:detail' patient.id %}" class="px-6 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
                    Cancel
                </a>
                <button type="submit" class="px-6 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700">
                    <i class="fas fa-save mr-2"></i>
                    Update Patient
                </button>
            </div>
        </form>
    </div>
</div>

<style>
/* Custom styles for form fields */
.form-control {
    @apply w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500;
}

select.form-control {
    @apply bg-white;
}

textarea.form-control {
    @apply resize-vertical;
}
</style>
{% endblock %}
