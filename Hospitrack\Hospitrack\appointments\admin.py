from django.contrib import admin
from .models import OPDSchedule, PatientOPDSchedule, OPDRegistration

# Removed regular Appointment admin - using only OPD system

@admin.register(OPDSchedule)
class OPDScheduleAdmin(admin.ModelAdmin):
    list_display = ['doctor', 'weekday', 'start_time', 'end_time', 'is_active']
    list_filter = ['weekday', 'is_active']
    search_fields = ['doctor__user__first_name', 'doctor__user__last_name']

@admin.register(PatientOPDSchedule)
class PatientOPDScheduleAdmin(admin.ModelAdmin):
    list_display = ['schedule_id', 'patient', 'doctor', 'schedule_date', 'time_slot', 'status']
    list_filter = ['status', 'schedule_date', 'doctor__specialization', 'is_recurring']
    search_fields = ['schedule_id', 'patient__first_name', 'patient__last_name', 'doctor__user__first_name', 'doctor__user__last_name']
    readonly_fields = ['schedule_id', 'created_at', 'updated_at']

    def save_model(self, request, obj, form, change):
        if not obj.schedule_id:
            import random
            obj.schedule_id = f"OPD{random.randint(1000, 9999)}"
            while PatientOPDSchedule.objects.filter(schedule_id=obj.schedule_id).exists():
                obj.schedule_id = f"OPD{random.randint(1000, 9999)}"

        if not obj.created_by:
            obj.created_by = request.user

        super().save_model(request, obj, form, change)

@admin.register(OPDRegistration)
class OPDRegistrationAdmin(admin.ModelAdmin):
    list_display = ['opd_number', 'patient_name', 'age', 'doctor', 'registration_date', 'queue_number', 'status', 'is_paid']
    list_filter = ['status', 'registration_date', 'doctor__specialization', 'is_emergency', 'is_paid', 'visit_type']
    search_fields = ['opd_number', 'patient_name', 'phone', 'doctor__user__first_name', 'doctor__user__last_name']
    readonly_fields = ['opd_number', 'registration_date', 'registration_time', 'queue_number', 'created_at', 'updated_at']

    def save_model(self, request, obj, form, change):
        if not obj.created_by:
            obj.created_by = request.user
        super().save_model(request, obj, form, change)
