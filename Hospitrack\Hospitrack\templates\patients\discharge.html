{% extends 'base.html' %}

{% block title %}Discharge {{ patient.full_name }} - HospiTrack{% endblock %}

{% block content %}
<div class="max-w-4xl mx-auto space-y-6">
    <!-- Header -->
    <div class="bg-white shadow rounded-lg p-6">
        <div class="flex items-center justify-between">
            <div>
                <h1 class="text-3xl font-bold text-gray-900">Patient Discharge</h1>
                <p class="text-gray-600 mt-2">{{ patient.full_name }} ({{ patient.patient_id }})</p>
                <div class="mt-2 flex items-center space-x-4 text-sm text-gray-500">
                    <span>{{ patient.age }} years old</span>
                    <span>{{ patient.get_gender_display }}</span>
                    <span>Blood Group: {{ patient.blood_group }}</span>
                </div>
            </div>
            <a href="{% url 'patients:detail' patient.id %}" class="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
                <i class="fas fa-arrow-left mr-2"></i>
                Back to Patient
            </a>
        </div>
    </div>

    <!-- Discharge Form -->
    <div class="bg-white shadow rounded-lg p-6">
        <form method="post" class="space-y-6">
            {% csrf_token %}
            
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">
                        Discharge Date & Time <span class="text-red-500">*</span>
                    </label>
                    <input type="datetime-local" name="discharge_date" required class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500">
                </div>

                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">
                        Follow-up Date
                    </label>
                    <input type="date" name="follow_up_date" class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500">
                </div>
            </div>

            <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">
                    Final Diagnosis <span class="text-red-500">*</span>
                </label>
                <div class="text-sm text-gray-500 mb-2">
                    Provide the final diagnosis and medical condition assessment.
                </div>
                <textarea name="final_diagnosis" rows="3" required class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500 resize-vertical" placeholder="Enter the final diagnosis..."></textarea>
            </div>

            <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">
                    Treatment Given <span class="text-red-500">*</span>
                </label>
                <div class="text-sm text-gray-500 mb-2">
                    Describe all treatments, procedures, and interventions provided during the stay.
                </div>
                <textarea name="treatment_given" rows="4" required class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500 resize-vertical" placeholder="Enter treatments and procedures provided..."></textarea>
            </div>

            <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">
                    Discharge Summary <span class="text-red-500">*</span>
                </label>
                <div class="text-sm text-gray-500 mb-2">
                    Comprehensive summary of the patient's hospital stay, condition at discharge, and overall progress.
                </div>
                <textarea name="discharge_summary" rows="5" required class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500 resize-vertical" placeholder="Enter comprehensive discharge summary..."></textarea>
            </div>

            <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">
                    Medications Prescribed
                </label>
                <div class="text-sm text-gray-500 mb-2">
                    List all medications prescribed for home use, including dosage and duration.
                </div>
                <textarea name="medications_prescribed" rows="3" class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500 resize-vertical" placeholder="e.g., Paracetamol 500mg - 1 tablet twice daily for 5 days"></textarea>
            </div>

            <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">
                    Follow-up Instructions
                </label>
                <div class="text-sm text-gray-500 mb-2">
                    Provide detailed instructions for post-discharge care, activity restrictions, and follow-up appointments.
                </div>
                <textarea name="follow_up_instructions" rows="4" class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500 resize-vertical" placeholder="Enter follow-up care instructions..."></textarea>
            </div>

            <!-- Quick Templates -->
            <div class="bg-gray-50 p-4 rounded-lg">
                <h3 class="text-sm font-medium text-gray-900 mb-3">Quick Templates</h3>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-3">
                    <button type="button" onclick="fillDiagnosis('Patient recovered well from treatment. Condition stable at discharge.')" 
                            class="text-left p-2 bg-white border border-gray-200 rounded text-sm hover:bg-gray-50">
                        <strong>Recovery:</strong> Patient recovered well from treatment
                    </button>
                    <button type="button" onclick="fillTreatment('Conservative management with medications and monitoring. No surgical intervention required.')" 
                            class="text-left p-2 bg-white border border-gray-200 rounded text-sm hover:bg-gray-50">
                        <strong>Conservative:</strong> Medical management without surgery
                    </button>
                    <button type="button" onclick="fillInstructions('Take medications as prescribed. Rest for 1 week. Follow up in 2 weeks. Return if symptoms worsen.')" 
                            class="text-left p-2 bg-white border border-gray-200 rounded text-sm hover:bg-gray-50">
                        <strong>Standard:</strong> Standard follow-up instructions
                    </button>
                    <button type="button" onclick="fillMedications('Paracetamol 500mg - 1 tablet twice daily for 5 days\\nAmoxicillin 250mg - 1 capsule three times daily for 7 days')" 
                            class="text-left p-2 bg-white border border-gray-200 rounded text-sm hover:bg-gray-50">
                        <strong>Common:</strong> Common discharge medications
                    </button>
                </div>
            </div>

            <!-- Form Actions -->
            <div class="flex justify-end space-x-4 pt-6 border-t border-gray-200">
                <a href="{% url 'patients:detail' patient.id %}" class="px-6 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
                    Cancel
                </a>
                <button type="submit" class="px-6 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-orange-600 hover:bg-orange-700">
                    <i class="fas fa-sign-out-alt mr-2"></i>
                    Discharge Patient
                </button>
            </div>
        </form>
    </div>

    <!-- Patient Summary -->
    <div class="bg-white shadow rounded-lg p-6">
        <h2 class="text-lg font-medium text-gray-900 mb-4">Patient Summary</h2>
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
                <h3 class="font-medium text-gray-900 mb-2">Personal Information</h3>
                <dl class="space-y-1 text-sm">
                    <div class="flex justify-between">
                        <dt class="text-gray-500">Name:</dt>
                        <dd class="text-gray-900">{{ patient.full_name }}</dd>
                    </div>
                    <div class="flex justify-between">
                        <dt class="text-gray-500">Age:</dt>
                        <dd class="text-gray-900">{{ patient.age }} years</dd>
                    </div>
                    <div class="flex justify-between">
                        <dt class="text-gray-500">Blood Group:</dt>
                        <dd class="text-gray-900">{{ patient.blood_group }}</dd>
                    </div>
                    <div class="flex justify-between">
                        <dt class="text-gray-500">Phone:</dt>
                        <dd class="text-gray-900">{{ patient.phone }}</dd>
                    </div>
                </dl>
            </div>
            
            <div>
                <h3 class="font-medium text-gray-900 mb-2">Medical Information</h3>
                <dl class="space-y-1 text-sm">
                    {% if patient.allergies %}
                    <div>
                        <dt class="text-gray-500">Allergies:</dt>
                        <dd class="text-red-600">{{ patient.allergies }}</dd>
                    </div>
                    {% endif %}
                    {% if patient.current_medications %}
                    <div>
                        <dt class="text-gray-500">Current Medications:</dt>
                        <dd class="text-gray-900">{{ patient.current_medications }}</dd>
                    </div>
                    {% endif %}
                    <div>
                        <dt class="text-gray-500">Admitted:</dt>
                        <dd class="text-gray-900">{{ patient.created_at|date:"M d, Y" }}</dd>
                    </div>
                </dl>
            </div>
        </div>
    </div>
</div>

<style>
/* Custom styles for form fields */
.form-control {
    @apply w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-1 focus:ring-orange-500 focus:border-orange-500;
}

textarea.form-control {
    @apply resize-vertical;
}
</style>

<script>
function fillDiagnosis(text) {
    document.querySelector('textarea[name="final_diagnosis"]').value = text;
}

function fillTreatment(text) {
    document.querySelector('textarea[name="treatment_given"]').value = text;
}

function fillInstructions(text) {
    document.querySelector('textarea[name="follow_up_instructions"]').value = text;
}

function fillMedications(text) {
    document.querySelector('textarea[name="medications_prescribed"]').value = text;
}

function fillSummary(text) {
    document.querySelector('textarea[name="discharge_summary"]').value = text;
}

// Set default discharge date to now
window.addEventListener('load', () => {
    const now = new Date();
    const year = now.getFullYear();
    const month = String(now.getMonth() + 1).padStart(2, '0');
    const day = String(now.getDate()).padStart(2, '0');
    const hours = String(now.getHours()).padStart(2, '0');
    const minutes = String(now.getMinutes()).padStart(2, '0');
    
    const dateTimeString = `${year}-${month}-${day}T${hours}:${minutes}`;
    const dischargeDateField = document.querySelector('input[name="discharge_date"]');
    if (dischargeDateField && !dischargeDateField.value) {
        dischargeDateField.value = dateTimeString;
    }
});
</script>
{% endblock %}
