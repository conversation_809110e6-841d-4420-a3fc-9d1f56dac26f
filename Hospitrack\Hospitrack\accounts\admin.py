from django.contrib import admin
from .models import User, Doctor, Nurse, <PERSON><PERSON>, MedicalSpecialty

@admin.register(MedicalSpecialty)
class MedicalSpecialtyAdmin(admin.ModelAdmin):
    list_display = ['name', 'description', 'is_active', 'created_at']
    list_filter = ['is_active', 'created_at']
    search_fields = ['name', 'description']
    ordering = ['name']

@admin.register(User)
class UserAdmin(admin.ModelAdmin):
    list_display = ['username', 'first_name', 'last_name', 'user_type', 'email', 'is_active']
    list_filter = ['user_type', 'is_active', 'created_at']
    search_fields = ['username', 'first_name', 'last_name', 'email']
    ordering = ['username']

@admin.register(Doctor)
class DoctorAdmin(admin.ModelAdmin):
    list_display = ['user', 'specialty', 'specialization', 'license_number', 'is_available']
    list_filter = ['specialty', 'is_available', 'user__created_at']
    search_fields = ['user__first_name', 'user__last_name', 'license_number', 'specialization']
    ordering = ['user__first_name']

@admin.register(Nurse)
class NurseAdmin(admin.ModelAdmin):
    list_display = ['user', 'specialty', 'department', 'shift', 'license_number']
    list_filter = ['specialty', 'shift', 'user__created_at']
    search_fields = ['user__first_name', 'user__last_name', 'license_number', 'department']
    ordering = ['user__first_name']

@admin.register(Admin)
class AdminAdmin(admin.ModelAdmin):
    list_display = ['user', 'employee_id', 'department']
    search_fields = ['user__first_name', 'user__last_name', 'employee_id']
    ordering = ['user__first_name']
