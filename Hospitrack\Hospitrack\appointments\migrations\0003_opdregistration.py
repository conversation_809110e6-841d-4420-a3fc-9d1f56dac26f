# Generated by Django 5.2.4 on 2025-07-23 00:40

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('accounts', '0001_initial'),
        ('appointments', '0002_patientopdschedule'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='OPDRegistration',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('opd_number', models.CharField(max_length=20, unique=True)),
                ('registration_date', models.DateField(auto_now_add=True)),
                ('registration_time', models.TimeField(auto_now_add=True)),
                ('patient_name', models.CharField(max_length=100)),
                ('age', models.IntegerField()),
                ('gender', models.CharField(choices=[('M', 'Male'), ('F', 'Female'), ('O', 'Other')], max_length=1)),
                ('phone', models.CharField(max_length=15)),
                ('address', models.TextField()),
                ('visit_type', models.CharField(choices=[('new', 'New Patient'), ('follow_up', 'Follow-up'), ('consultation', 'Consultation'), ('emergency', 'Emergency')], default='new', max_length=15)),
                ('chief_complaint', models.TextField(help_text='Main reason for visit')),
                ('symptoms', models.TextField(blank=True, null=True)),
                ('preferred_time', models.CharField(choices=[('09:00', '9:00 AM'), ('09:30', '9:30 AM'), ('10:00', '10:00 AM'), ('10:30', '10:30 AM'), ('11:00', '11:00 AM'), ('11:30', '11:30 AM'), ('14:00', '2:00 PM'), ('14:30', '2:30 PM'), ('15:00', '3:00 PM'), ('15:30', '3:30 PM'), ('16:00', '4:00 PM'), ('16:30', '4:30 PM'), ('18:00', '6:00 PM'), ('18:30', '6:30 PM'), ('19:00', '7:00 PM'), ('19:30', '7:30 PM')], max_length=5)),
                ('consultation_fee', models.DecimalField(decimal_places=2, max_digits=8)),
                ('is_emergency', models.BooleanField(default=False)),
                ('status', models.CharField(choices=[('registered', 'Registered'), ('waiting', 'Waiting'), ('consulting', 'Consulting'), ('completed', 'Completed'), ('cancelled', 'Cancelled')], default='registered', max_length=15)),
                ('queue_number', models.IntegerField(blank=True, null=True)),
                ('consultation_start_time', models.TimeField(blank=True, null=True)),
                ('consultation_end_time', models.TimeField(blank=True, null=True)),
                ('is_paid', models.BooleanField(default=False)),
                ('payment_method', models.CharField(choices=[('cash', 'Cash'), ('card', 'Card'), ('upi', 'UPI'), ('insurance', 'Insurance')], default='cash', max_length=20)),
                ('receptionist_notes', models.TextField(blank=True, null=True)),
                ('doctor_notes', models.TextField(blank=True, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('created_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to=settings.AUTH_USER_MODEL)),
                ('doctor', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='opd_registrations', to='accounts.doctor')),
            ],
            options={
                'ordering': ['registration_date', 'queue_number', 'registration_time'],
                'unique_together': {('doctor', 'registration_date', 'queue_number')},
            },
        ),
    ]
