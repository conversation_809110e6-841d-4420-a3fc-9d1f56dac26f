#!/usr/bin/env python
"""
Final comprehensive test for database synchronization fixes
"""
import os
import sys
import django
from django.test import Client

# Setup Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'hospitrack.settings')
django.setup()

from accounts.models import User, Doctor, Nurse, MedicalSpecialty
from patients.models import Patient
from appointments.models import PatientOPDSchedule

def test_admin_doctor_update_flow():
    """Test complete admin -> doctor update flow"""
    print("🔧 Testing Admin Doctor Update Flow")
    print("=" * 60)
    
    client = Client()
    results = {'passed': 0, 'failed': 0, 'errors': []}
    
    # Login as admin
    client.login(username='admin', password='admin123')
    
    try:
        # Get a doctor
        doctor = Doctor.objects.first()
        if not doctor:
            results['failed'] += 1
            results['errors'].append("No doctors found")
            return results
        
        original_specialization = doctor.specialization
        print(f"   📋 Doctor: Dr. {doctor.user.get_full_name()}")
        print(f"   📋 Original specialization: {original_specialization}")
        
        # Update doctor via admin interface
        new_specialization = "Updated Emergency Medicine"
        form_data = {
            'first_name': doctor.user.first_name,
            'last_name': doctor.user.last_name,
            'email': doctor.user.email,
            'phone': doctor.user.phone or '**********',
            'specialization': new_specialization,
            'license_number': doctor.license_number,
            'department': 'Emergency',
            'experience_years': 10,
            'consultation_fee': 750,
            'qualifications': 'Updated qualifications'
        }
        
        response = client.post(f'/doctors/{doctor.user.id}/edit/', form_data)
        
        if response.status_code == 302:
            print("   ✅ Admin update successful")
            results['passed'] += 1
            
            # Refresh doctor from database
            doctor.refresh_from_db()
            
            # Check if all fields were updated
            if doctor.specialization == new_specialization:
                print(f"   ✅ Specialization updated: {doctor.specialization}")
                results['passed'] += 1
            else:
                print(f"   ❌ Specialization not updated: {doctor.specialization}")
                results['failed'] += 1
                results['errors'].append("Specialization not updated")
            
            if doctor.specialty and doctor.specialty.name == new_specialization:
                print(f"   ✅ Specialty FK updated: {doctor.specialty.name}")
                results['passed'] += 1
            else:
                print(f"   ❌ Specialty FK not updated: {doctor.specialty}")
                results['failed'] += 1
                results['errors'].append("Specialty FK not updated")
            
            if doctor.experience_years == 10:
                print(f"   ✅ Experience years updated: {doctor.experience_years}")
                results['passed'] += 1
            else:
                print(f"   ❌ Experience years not updated: {doctor.experience_years}")
                results['failed'] += 1
                results['errors'].append("Experience years not updated")
                
        else:
            print(f"   ❌ Admin update failed: {response.status_code}")
            results['failed'] += 1
            results['errors'].append(f"Admin update failed: {response.status_code}")
            
    except Exception as e:
        print(f"   ❌ Exception: {str(e)}")
        results['failed'] += 1
        results['errors'].append(f"Exception: {str(e)}")
    
    return results

def test_admin_nurse_update_flow():
    """Test complete admin -> nurse update flow"""
    print(f"\n🔧 Testing Admin Nurse Update Flow")
    print("=" * 60)
    
    client = Client()
    results = {'passed': 0, 'failed': 0, 'errors': []}
    
    # Login as admin
    client.login(username='admin', password='admin123')
    
    try:
        # Get a nurse
        nurse = Nurse.objects.first()
        if not nurse:
            results['failed'] += 1
            results['errors'].append("No nurses found")
            return results
        
        print(f"   📋 Nurse: {nurse.user.get_full_name()}")
        print(f"   📋 Original department: {nurse.department}")
        
        # Test nurse edit functionality
        new_specialty = "Updated Cardiology"
        form_data = {
            'first_name': nurse.user.first_name,
            'last_name': nurse.user.last_name,
            'email': nurse.user.email,
            'phone': nurse.user.phone or '**********',
            'specialty': new_specialty,
            'license_number': nurse.license_number,
            'shift': nurse.shift,
            'experience_years': 8,
            'qualifications': 'Updated nursing qualifications'
        }
        
        response = client.post(f'/nurses/{nurse.user.id}/edit/', form_data)
        
        if response.status_code == 302:
            print("   ✅ Admin nurse update successful")
            results['passed'] += 1
            
            # Refresh nurse from database
            nurse.refresh_from_db()
            
            # Check if fields were updated
            if nurse.department == new_specialty:
                print(f"   ✅ Department updated: {nurse.department}")
                results['passed'] += 1
            else:
                print(f"   ❌ Department not updated: {nurse.department}")
                results['failed'] += 1
                results['errors'].append("Nurse department not updated")
            
            if nurse.specialty and nurse.specialty.name == new_specialty:
                print(f"   ✅ Nurse specialty FK updated: {nurse.specialty.name}")
                results['passed'] += 1
            else:
                print(f"   ❌ Nurse specialty FK not updated: {nurse.specialty}")
                results['failed'] += 1
                results['errors'].append("Nurse specialty FK not updated")
                
        else:
            print(f"   ❌ Admin nurse update failed: {response.status_code}")
            results['failed'] += 1
            results['errors'].append(f"Admin nurse update failed: {response.status_code}")
            
    except Exception as e:
        print(f"   ❌ Exception: {str(e)}")
        results['failed'] += 1
        results['errors'].append(f"Exception: {str(e)}")
    
    return results

def test_opd_scheduling_with_updated_doctor():
    """Test OPD scheduling with updated doctor"""
    print(f"\n🔧 Testing OPD Scheduling with Updated Doctor")
    print("=" * 60)
    
    client = Client()
    results = {'passed': 0, 'failed': 0, 'errors': []}
    
    # Login as admin
    client.login(username='admin', password='admin123')
    
    try:
        # Get doctor and patient
        doctor = Doctor.objects.first()
        patient = Patient.objects.first()
        
        if not doctor or not patient:
            results['failed'] += 1
            results['errors'].append("Missing doctor or patient for OPD test")
            return results
        
        print(f"   📋 Doctor: Dr. {doctor.user.get_full_name()} - {doctor.specialization}")
        print(f"   📋 Patient: {patient.full_name}")
        
        # Create OPD schedule
        from datetime import date, timedelta
        tomorrow = date.today() + timedelta(days=1)
        
        form_data = {
            'patient': patient.id,
            'doctor': doctor.user.id,
            'schedule_date': tomorrow.strftime('%Y-%m-%d'),
            'time_slot': '10:00',
            'reason': 'Follow-up consultation',
            'special_instructions': 'Test OPD with updated doctor'
        }
        
        response = client.post('/appointments/opd-schedule/create/', form_data)
        
        if response.status_code == 302:
            print("   ✅ OPD schedule creation successful")
            results['passed'] += 1
            
            # Verify schedule in database
            schedule = PatientOPDSchedule.objects.filter(
                doctor=doctor,
                patient=patient,
                schedule_date=tomorrow
            ).first()
            
            if schedule:
                print(f"   ✅ OPD schedule found: {schedule.schedule_id}")
                results['passed'] += 1
                
                # Verify doctor relationship
                if schedule.doctor.user.id == doctor.user.id:
                    print(f"   ✅ Doctor relationship correct")
                    results['passed'] += 1
                else:
                    print(f"   ❌ Doctor relationship incorrect")
                    results['failed'] += 1
                    results['errors'].append("Doctor relationship incorrect")
                    
                # Verify doctor specialization is reflected
                if schedule.doctor.specialization == doctor.specialization:
                    print(f"   ✅ Doctor specialization reflected: {schedule.doctor.specialization}")
                    results['passed'] += 1
                else:
                    print(f"   ❌ Doctor specialization not reflected")
                    results['failed'] += 1
                    results['errors'].append("Doctor specialization not reflected")
                    
            else:
                print("   ❌ OPD schedule not found in database")
                results['failed'] += 1
                results['errors'].append("OPD schedule not created")
                
        else:
            print(f"   ❌ OPD schedule creation failed: {response.status_code}")
            results['failed'] += 1
            results['errors'].append(f"OPD schedule creation failed: {response.status_code}")
            
    except Exception as e:
        print(f"   ❌ Exception: {str(e)}")
        results['failed'] += 1
        results['errors'].append(f"Exception: {str(e)}")
    
    return results

def test_admin_dashboard_reflects_changes():
    """Test that admin dashboard reflects all changes"""
    print(f"\n🔧 Testing Admin Dashboard Reflects Changes")
    print("=" * 60)
    
    client = Client()
    results = {'passed': 0, 'failed': 0, 'errors': []}
    
    # Login as admin
    client.login(username='admin', password='admin123')
    
    try:
        # Get current counts
        doctor_count = Doctor.objects.count()
        nurse_count = Nurse.objects.count()
        specialty_count = MedicalSpecialty.objects.count()
        
        print(f"   📊 Current counts - Doctors: {doctor_count}, Nurses: {nurse_count}, Specialties: {specialty_count}")
        
        # Test admin dashboard
        response = client.get('/admin-dashboard/')
        
        if response.status_code == 200:
            print("   ✅ Admin dashboard accessible")
            results['passed'] += 1
            
            content = response.content.decode()
            
            # Check if counts are displayed
            if str(doctor_count) in content:
                print(f"   ✅ Doctor count displayed: {doctor_count}")
                results['passed'] += 1
            else:
                print(f"   ❌ Doctor count not displayed")
                results['failed'] += 1
                results['errors'].append("Doctor count not displayed")
            
            if str(nurse_count) in content:
                print(f"   ✅ Nurse count displayed: {nurse_count}")
                results['passed'] += 1
            else:
                print(f"   ❌ Nurse count not displayed")
                results['failed'] += 1
                results['errors'].append("Nurse count not displayed")
            
            # Check if updated specializations are shown
            updated_specialties = MedicalSpecialty.objects.filter(name__icontains='Updated').count()
            if updated_specialties > 0:
                print(f"   ✅ Updated specialties found: {updated_specialties}")
                results['passed'] += 1
            else:
                print(f"   ⚠️  No updated specialties found (may be normal)")
                
        else:
            print(f"   ❌ Admin dashboard failed: {response.status_code}")
            results['failed'] += 1
            results['errors'].append(f"Admin dashboard failed: {response.status_code}")
            
    except Exception as e:
        print(f"   ❌ Exception: {str(e)}")
        results['failed'] += 1
        results['errors'].append(f"Exception: {str(e)}")
    
    return results

def main():
    """Run final comprehensive database sync tests"""
    print("🎯 Final Database Synchronization Testing")
    print("=" * 80)
    print("Testing complete admin -> doctor/nurse -> OPD scheduling flow")
    print("=" * 80)
    
    all_results = {'passed': 0, 'failed': 0, 'errors': []}
    
    # Run comprehensive test suites
    test_suites = [
        ("Admin Doctor Update Flow", test_admin_doctor_update_flow),
        ("Admin Nurse Update Flow", test_admin_nurse_update_flow),
        ("OPD Scheduling with Updated Doctor", test_opd_scheduling_with_updated_doctor),
        ("Admin Dashboard Reflects Changes", test_admin_dashboard_reflects_changes)
    ]
    
    for suite_name, test_function in test_suites:
        print(f"\n🧪 Running {suite_name}...")
        results = test_function()
        all_results['passed'] += results['passed']
        all_results['failed'] += results['failed']
        all_results['errors'].extend(results['errors'])
        
        print(f"   📊 {suite_name} Results: {results['passed']} passed, {results['failed']} failed")
    
    # Final summary
    print(f"\n" + "=" * 80)
    print(f"🎯 FINAL DATABASE SYNC TEST RESULTS")
    print(f"=" * 80)
    print(f"✅ Total Passed: {all_results['passed']}")
    print(f"❌ Total Failed: {all_results['failed']}")
    
    if all_results['passed'] + all_results['failed'] > 0:
        success_rate = (all_results['passed']/(all_results['passed']+all_results['failed']))*100
        print(f"📊 Success Rate: {success_rate:.1f}%")
    
    if all_results['errors']:
        print(f"\n🔍 Issues Found:")
        for i, error in enumerate(all_results['errors'], 1):
            print(f"   {i}. {error}")
    
    # Final status
    if all_results['failed'] == 0:
        print(f"\n🎉 DATABASE SYNC STATUS: ✅ FULLY RESOLVED")
        print(f"   ✅ Admin can update doctor/nurse details")
        print(f"   ✅ Changes are properly synchronized in database")
        print(f"   ✅ OPD scheduling works with updated doctors")
        print(f"   ✅ Admin dashboard reflects all changes")
        print(f"\n🚀 All database synchronization issues have been resolved!")
    elif all_results['failed'] <= 2:
        print(f"\n⚠️  DATABASE SYNC STATUS: 🟡 MOSTLY RESOLVED")
        print(f"   Most database synchronization issues have been fixed.")
        print(f"   Minor issues may remain.")
    else:
        print(f"\n❌ DATABASE SYNC STATUS: 🔴 NEEDS MORE WORK")
        print(f"   Significant database synchronization issues remain.")
    
    return all_results['failed'] == 0

if __name__ == '__main__':
    try:
        success = main()
        sys.exit(0 if success else 1)
    except Exception as e:
        print(f"❌ Final database sync test execution failed: {str(e)}")
        sys.exit(1)