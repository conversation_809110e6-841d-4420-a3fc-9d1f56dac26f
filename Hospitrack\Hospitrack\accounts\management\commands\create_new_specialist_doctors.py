from django.core.management.base import BaseCommand
from django.contrib.auth import get_user_model
from accounts.models import Doctor, MedicalSpecialty

User = get_user_model()

class Command(BaseCommand):
    help = 'Create new specialist doctors based on updated credentials'

    def handle(self, *args, **options):
        self.stdout.write('Creating new specialist doctors...')
        
        # First, create the specialties
        specialties_data = [
            {'name': 'Emergency Medicine', 'description': 'Emergency and trauma care'},
            {'name': 'Oncology', 'description': 'Cancer treatment and care'},
            {'name': 'Anesthesiology', 'description': 'Anesthesia and pain management'},
            {'name': 'Dermatology', 'description': 'Skin, hair, and nail disorders'},
            {'name': 'Radiology', 'description': 'Medical imaging and diagnostics'},
        ]
        
        for specialty_data in specialties_data:
            specialty, created = MedicalSpecialty.objects.get_or_create(
                name=specialty_data['name'],
                defaults={'description': specialty_data['description']}
            )
            if created:
                self.stdout.write(f'✓ Created specialty: {specialty.name}')
            else:
                self.stdout.write(f'- Specialty already exists: {specialty.name}')
        
        doctors_data = [
            {
                'username': 'dr.emergency',
                'email': '<EMAIL>',
                'first_name': '<PERSON>',
                'last_name': '<PERSON>',
                'specialty_name': 'Emergency Medicine',
                'license_number': 'EMER001',
                'consultation_fee': 800.00,
            },
            {
                'username': 'dr.oncology',
                'email': '<EMAIL>',
                'first_name': 'Sophia',
                'last_name': 'Chen',
                'specialty_name': 'Oncology',
                'license_number': 'ONCO001',
                'consultation_fee': 1000.00,
            },
            {
                'username': 'dr.anesthesiology',
                'email': '<EMAIL>',
                'first_name': 'Marcus',
                'last_name': 'Rodriguez',
                'specialty_name': 'Anesthesiology',
                'license_number': 'ANES001',
                'consultation_fee': 750.00,
            },
            {
                'username': 'dr.dermatology',
                'email': '<EMAIL>',
                'first_name': 'Isabella',
                'last_name': 'Thompson',
                'specialty_name': 'Dermatology',
                'license_number': 'DERM001',
                'consultation_fee': 650.00,
            },
            {
                'username': 'dr.radiology',
                'email': '<EMAIL>',
                'first_name': 'Benjamin',
                'last_name': 'Kumar',
                'specialty_name': 'Radiology',
                'license_number': 'RADI001',
                'consultation_fee': 700.00,
            }
        ]
        
        created_count = 0
        for doctor_data in doctors_data:
            try:
                # Check if user already exists
                if User.objects.filter(username=doctor_data['username']).exists():
                    self.stdout.write(f'- Doctor {doctor_data["username"]} already exists')
                    continue
                
                # Get the specialty
                specialty = MedicalSpecialty.objects.get(name=doctor_data['specialty_name'])
                
                # Create user account
                user = User.objects.create_user(
                    username=doctor_data['username'],
                    email=doctor_data['email'],
                    password='doctor123',
                    first_name=doctor_data['first_name'],
                    last_name=doctor_data['last_name'],
                    user_type='doctor'
                )
                
                # Create doctor profile
                doctor = Doctor.objects.create(
                    user=user,
                    specialty=specialty,
                    specialization=specialty.name,  # For backward compatibility
                    license_number=doctor_data['license_number'],
                    department=specialty.name,
                    consultation_fee=doctor_data['consultation_fee'],
                    available_from='09:00',
                    available_to='17:00',
                    is_available=True
                )
                
                created_count += 1
                self.stdout.write(f'✓ Created: Dr. {user.get_full_name()} - {doctor.specialty.name}')
                
            except Exception as e:
                self.stdout.write(f'✗ Error creating {doctor_data["username"]}: {str(e)}')
        
        self.stdout.write(f'\n✅ Created {created_count} new specialist doctors.')
        self.stdout.write(f'📊 Total doctors: {Doctor.objects.count()}')
        self.stdout.write(f'🔑 All doctors password: doctor123')