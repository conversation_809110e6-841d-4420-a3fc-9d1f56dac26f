{% extends 'base.html' %}

{% block title %}Assign Bed - HospiTrack{% endblock %}

{% block content %}
<div class="max-w-4xl mx-auto space-y-6">
    <!-- Header -->
    <div class="bg-white shadow rounded-lg p-6">
        <div class="flex items-center justify-between">
            <div>
                <h1 class="text-3xl font-bold text-gray-900">Assign Bed to Patient</h1>
                <p class="text-gray-600 mt-2">Select an available bed for patient admission</p>
            </div>
            <a href="{% url 'beds:management' %}" class="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
                <i class="fas fa-arrow-left mr-2"></i>
                Back to Bed Management
            </a>
        </div>
    </div>

    <!-- Assignment Form -->
    <div class="bg-white shadow rounded-lg p-6">
        <form method="post" class="space-y-6">
            {% csrf_token %}
            
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">
                        Select Patient <span class="text-red-500">*</span>
                    </label>
                    <select name="patient" required class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500">
                        <option value="">Choose a patient...</option>
                        <option value="1">PAT001 - Alice Brown</option>
                        <option value="2">PAT002 - David Wilson</option>
                    </select>
                </div>
                
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">
                        Select Ward <span class="text-red-500">*</span>
                    </label>
                    <select name="ward" required class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500" onchange="loadBeds(this.value)">
                        <option value="">Choose a ward...</option>
                        <option value="1">General Ward A - Floor 1</option>
                        <option value="2">General Ward B - Floor 2</option>
                        <option value="3">ICU - Floor 3</option>
                        <option value="4">Emergency Ward - Floor 1</option>
                        <option value="5">Private Ward - Floor 4</option>
                    </select>
                </div>
            </div>

            <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">
                    Select Bed <span class="text-red-500">*</span>
                </label>
                <div id="bedSelection" class="grid grid-cols-2 sm:grid-cols-4 md:grid-cols-6 lg:grid-cols-8 gap-3">
                    <div class="text-center text-gray-500 col-span-full py-8">
                        Please select a ward first to view available beds
                    </div>
                </div>
                <input type="hidden" name="bed" id="selectedBed" required>
            </div>

            <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">
                    Assignment Notes
                </label>
                <textarea name="notes" rows="3" class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500" placeholder="Any special notes or instructions for this bed assignment..."></textarea>
            </div>

            <!-- Form Actions -->
            <div class="flex justify-end space-x-4 pt-6 border-t border-gray-200">
                <a href="{% url 'beds:management' %}" class="px-6 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
                    Cancel
                </a>
                <button type="submit" class="px-6 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700">
                    <i class="fas fa-bed mr-2"></i>
                    Assign Bed
                </button>
            </div>
        </form>
    </div>

    <!-- Available Beds Summary -->
    <div class="bg-white shadow rounded-lg p-6">
        <h2 class="text-xl font-bold text-gray-900 mb-4">Bed Availability Summary</h2>
        <div class="grid grid-cols-1 md:grid-cols-5 gap-4">
            <div class="text-center p-4 bg-blue-50 rounded-lg">
                <div class="text-2xl font-bold text-blue-600">20</div>
                <div class="text-sm text-blue-800">General Ward A</div>
                <div class="text-xs text-blue-600">15 available</div>
            </div>
            <div class="text-center p-4 bg-green-50 rounded-lg">
                <div class="text-2xl font-bold text-green-600">20</div>
                <div class="text-sm text-green-800">General Ward B</div>
                <div class="text-xs text-green-600">18 available</div>
            </div>
            <div class="text-center p-4 bg-red-50 rounded-lg">
                <div class="text-2xl font-bold text-red-600">10</div>
                <div class="text-sm text-red-800">ICU</div>
                <div class="text-xs text-red-600">8 available</div>
            </div>
            <div class="text-center p-4 bg-yellow-50 rounded-lg">
                <div class="text-2xl font-bold text-yellow-600">15</div>
                <div class="text-sm text-yellow-800">Emergency</div>
                <div class="text-xs text-yellow-600">12 available</div>
            </div>
            <div class="text-center p-4 bg-purple-50 rounded-lg">
                <div class="text-2xl font-bold text-purple-600">8</div>
                <div class="text-sm text-purple-800">Private Ward</div>
                <div class="text-xs text-purple-600">6 available</div>
            </div>
        </div>
    </div>

    <!-- Recent Assignments -->
    <div class="bg-white shadow rounded-lg p-6">
        <h2 class="text-xl font-bold text-gray-900 mb-4">Recent Bed Assignments</h2>
        <div class="space-y-3">
            <div class="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                <div>
                    <div class="font-medium text-gray-900">Alice Brown (PAT001)</div>
                    <div class="text-sm text-gray-600">Assigned to General Ward A - Bed 05</div>
                </div>
                <div class="text-sm text-gray-500">2 hours ago</div>
            </div>
            <div class="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                <div>
                    <div class="font-medium text-gray-900">David Wilson (PAT002)</div>
                    <div class="text-sm text-gray-600">Assigned to ICU - Bed 03</div>
                </div>
                <div class="text-sm text-gray-500">5 hours ago</div>
            </div>
        </div>
    </div>
</div>

<script>
let selectedBedElement = null;

function loadBeds(wardId) {
    const bedSelection = document.getElementById('bedSelection');
    const selectedBedInput = document.getElementById('selectedBed');
    
    if (!wardId) {
        bedSelection.innerHTML = '<div class="text-center text-gray-500 col-span-full py-8">Please select a ward first to view available beds</div>';
        selectedBedInput.value = '';
        return;
    }
    
    // Mock bed data - in real implementation, this would be an AJAX call
    const mockBeds = {
        '1': { name: 'General Ward A', beds: Array.from({length: 20}, (_, i) => ({id: i+1, number: String(i+1).padStart(2, '0'), available: Math.random() > 0.3})) },
        '2': { name: 'General Ward B', beds: Array.from({length: 20}, (_, i) => ({id: i+21, number: String(i+1).padStart(2, '0'), available: Math.random() > 0.2})) },
        '3': { name: 'ICU', beds: Array.from({length: 10}, (_, i) => ({id: i+41, number: String(i+1).padStart(2, '0'), available: Math.random() > 0.4})) },
        '4': { name: 'Emergency Ward', beds: Array.from({length: 15}, (_, i) => ({id: i+51, number: String(i+1).padStart(2, '0'), available: Math.random() > 0.3})) },
        '5': { name: 'Private Ward', beds: Array.from({length: 8}, (_, i) => ({id: i+66, number: String(i+1).padStart(2, '0'), available: Math.random() > 0.2})) }
    };
    
    const wardData = mockBeds[wardId];
    if (!wardData) return;
    
    let bedsHtml = '';
    wardData.beds.forEach(bed => {
        const isAvailable = bed.available;
        bedsHtml += `
            <div class="bed-option ${isAvailable ? 'available' : 'occupied'}" 
                 onclick="${isAvailable ? `selectBed(${bed.id}, '${bed.number}', this)` : ''}"
                 style="cursor: ${isAvailable ? 'pointer' : 'not-allowed'}">
                <div class="p-3 rounded-lg border-2 text-center ${isAvailable ? 'border-green-300 bg-green-50 hover:bg-green-100' : 'border-red-300 bg-red-50'}">
                    <div class="font-bold text-gray-900">${bed.number}</div>
                    <div class="text-xs ${isAvailable ? 'text-green-700' : 'text-red-700'}">
                        ${isAvailable ? 'Available' : 'Occupied'}
                    </div>
                    ${isAvailable ? '<i class="fas fa-check-circle text-green-500 mt-1"></i>' : '<i class="fas fa-user text-red-500 mt-1"></i>'}
                </div>
            </div>
        `;
    });
    
    bedSelection.innerHTML = bedsHtml;
    selectedBedInput.value = '';
}

function selectBed(bedId, bedNumber, element) {
    // Remove previous selection
    if (selectedBedElement) {
        selectedBedElement.querySelector('div').classList.remove('border-blue-500', 'bg-blue-100');
        selectedBedElement.querySelector('div').classList.add('border-green-300', 'bg-green-50');
    }
    
    // Add selection to current bed
    element.querySelector('div').classList.remove('border-green-300', 'bg-green-50');
    element.querySelector('div').classList.add('border-blue-500', 'bg-blue-100');
    
    selectedBedElement = element;
    document.getElementById('selectedBed').value = bedId;
}

// Pre-select patient if passed in URL
window.addEventListener('load', () => {
    const urlParams = new URLSearchParams(window.location.search);
    const patientId = urlParams.get('patient');
    if (patientId) {
        const patientSelect = document.querySelector('select[name="patient"]');
        patientSelect.value = patientId;
    }
});
</script>

<style>
.bed-option.available:hover .border-green-300 {
    border-color: #10b981;
}
</style>
{% endblock %}
