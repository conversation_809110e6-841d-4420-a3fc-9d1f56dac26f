{% extends 'base.html' %}

{% block title %}Appointment Schedule - HospiTrack{% endblock %}

{% block content %}
<div class="space-y-6">
    <!-- Header -->
    <div class="bg-white shadow rounded-lg p-6">
        <div class="flex justify-between items-center">
            <div>
                <h1 class="text-3xl font-bold text-gray-900">Appointment Schedule</h1>
                <p class="text-gray-600 mt-2">View and manage daily appointment schedules</p>
            </div>
            <div class="flex space-x-3">
                <a href="{% url 'appointments:list' %}" class="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
                    <i class="fas fa-arrow-left mr-2"></i>
                    Back to Appointments
                </a>
                {% if user.user_type == 'admin' or user.user_type == 'doctor' %}
                <a href="{% url 'appointments:create' %}" class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700">
                    <i class="fas fa-plus mr-2"></i>
                    New Appointment
                </a>
                {% endif %}
            </div>
        </div>
    </div>

    <!-- Schedule Overview -->
    <div class="grid grid-cols-1 md:grid-cols-4 gap-6">
        <div class="bg-white overflow-hidden shadow rounded-lg">
            <div class="p-5">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <i class="fas fa-calendar-day text-3xl text-blue-500"></i>
                    </div>
                    <div class="ml-5 w-0 flex-1">
                        <dl>
                            <dt class="text-sm font-medium text-gray-500 truncate">Today's Appointments</dt>
                            <dd class="text-3xl font-bold text-gray-900">{{ today_appointments.count }}</dd>
                        </dl>
                    </div>
                </div>
            </div>
        </div>

        <div class="bg-white overflow-hidden shadow rounded-lg">
            <div class="p-5">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <i class="fas fa-clock text-3xl text-green-500"></i>
                    </div>
                    <div class="ml-5 w-0 flex-1">
                        <dl>
                            <dt class="text-sm font-medium text-gray-500 truncate">Pending</dt>
                            <dd class="text-3xl font-bold text-green-600">{{ pending_appointments.count }}</dd>
                        </dl>
                    </div>
                </div>
            </div>
        </div>

        <div class="bg-white overflow-hidden shadow rounded-lg">
            <div class="p-5">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <i class="fas fa-check-circle text-3xl text-purple-500"></i>
                    </div>
                    <div class="ml-5 w-0 flex-1">
                        <dl>
                            <dt class="text-sm font-medium text-gray-500 truncate">Completed</dt>
                            <dd class="text-3xl font-bold text-purple-600">{{ completed_appointments.count }}</dd>
                        </dl>
                    </div>
                </div>
            </div>
        </div>

        <div class="bg-white overflow-hidden shadow rounded-lg">
            <div class="p-5">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <i class="fas fa-user-md text-3xl text-orange-500"></i>
                    </div>
                    <div class="ml-5 w-0 flex-1">
                        <dl>
                            <dt class="text-sm font-medium text-gray-500 truncate">Doctors Available</dt>
                            <dd class="text-3xl font-bold text-orange-600">{{ available_doctors.count }}</dd>
                        </dl>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Time Slots Schedule -->
    <div class="bg-white shadow rounded-lg p-6">
        <div class="flex justify-between items-center mb-6">
            <h2 class="text-lg font-medium text-gray-900">Today's Schedule - {{ current_date|date:"F d, Y" }}</h2>
            <div class="flex space-x-2">
                <button class="px-3 py-1 text-xs font-medium rounded-full bg-blue-100 text-blue-800">All</button>
                <button class="px-3 py-1 text-xs font-medium rounded-full bg-gray-100 text-gray-600">Morning</button>
                <button class="px-3 py-1 text-xs font-medium rounded-full bg-gray-100 text-gray-600">Afternoon</button>
                <button class="px-3 py-1 text-xs font-medium rounded-full bg-gray-100 text-gray-600">Evening</button>
            </div>
        </div>

        <!-- Schedule Grid -->
        <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
            <!-- Morning Slots -->
            <div>
                <h3 class="text-md font-semibold text-gray-900 mb-4 flex items-center">
                    <i class="fas fa-sun text-yellow-500 mr-2"></i>
                    Morning (9:00 AM - 12:00 PM)
                </h3>
                <div class="space-y-3">
                    {% for slot in morning_slots %}
                    <div class="border rounded-lg p-3 {% if slot.is_booked %}bg-red-50 border-red-200{% else %}bg-green-50 border-green-200{% endif %}">
                        <div class="flex justify-between items-center">
                            <div>
                                <div class="font-medium text-sm">{{ slot.time }}</div>
                                {% if slot.is_booked %}
                                <div class="text-xs text-gray-600">{{ slot.patient_name }}</div>
                                <div class="text-xs text-gray-500">Dr. {{ slot.doctor_name }}</div>
                                {% else %}
                                <div class="text-xs text-green-600">Available</div>
                                {% endif %}
                            </div>
                            <div>
                                {% if slot.is_booked %}
                                <span class="px-2 py-1 text-xs font-semibold rounded-full bg-red-100 text-red-800">Booked</span>
                                {% else %}
                                <span class="px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800">Free</span>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                    {% empty %}
                    <div class="text-center py-4 text-gray-500">
                        <i class="fas fa-calendar-times text-2xl mb-2"></i>
                        <p>No morning slots available</p>
                    </div>
                    {% endfor %}
                </div>
            </div>

            <!-- Afternoon Slots -->
            <div>
                <h3 class="text-md font-semibold text-gray-900 mb-4 flex items-center">
                    <i class="fas fa-sun text-orange-500 mr-2"></i>
                    Afternoon (1:00 PM - 5:00 PM)
                </h3>
                <div class="space-y-3">
                    {% for slot in afternoon_slots %}
                    <div class="border rounded-lg p-3 {% if slot.is_booked %}bg-red-50 border-red-200{% else %}bg-green-50 border-green-200{% endif %}">
                        <div class="flex justify-between items-center">
                            <div>
                                <div class="font-medium text-sm">{{ slot.time }}</div>
                                {% if slot.is_booked %}
                                <div class="text-xs text-gray-600">{{ slot.patient_name }}</div>
                                <div class="text-xs text-gray-500">Dr. {{ slot.doctor_name }}</div>
                                {% else %}
                                <div class="text-xs text-green-600">Available</div>
                                {% endif %}
                            </div>
                            <div>
                                {% if slot.is_booked %}
                                <span class="px-2 py-1 text-xs font-semibold rounded-full bg-red-100 text-red-800">Booked</span>
                                {% else %}
                                <span class="px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800">Free</span>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                    {% empty %}
                    <div class="text-center py-4 text-gray-500">
                        <i class="fas fa-calendar-times text-2xl mb-2"></i>
                        <p>No afternoon slots available</p>
                    </div>
                    {% endfor %}
                </div>
            </div>

            <!-- Evening Slots -->
            <div>
                <h3 class="text-md font-semibold text-gray-900 mb-4 flex items-center">
                    <i class="fas fa-moon text-blue-500 mr-2"></i>
                    Evening (6:00 PM - 9:00 PM)
                </h3>
                <div class="space-y-3">
                    {% for slot in evening_slots %}
                    <div class="border rounded-lg p-3 {% if slot.is_booked %}bg-red-50 border-red-200{% else %}bg-green-50 border-green-200{% endif %}">
                        <div class="flex justify-between items-center">
                            <div>
                                <div class="font-medium text-sm">{{ slot.time }}</div>
                                {% if slot.is_booked %}
                                <div class="text-xs text-gray-600">{{ slot.patient_name }}</div>
                                <div class="text-xs text-gray-500">Dr. {{ slot.doctor_name }}</div>
                                {% else %}
                                <div class="text-xs text-green-600">Available</div>
                                {% endif %}
                            </div>
                            <div>
                                {% if slot.is_booked %}
                                <span class="px-2 py-1 text-xs font-semibold rounded-full bg-red-100 text-red-800">Booked</span>
                                {% else %}
                                <span class="px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800">Free</span>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                    {% empty %}
                    <div class="text-center py-4 text-gray-500">
                        <i class="fas fa-calendar-times text-2xl mb-2"></i>
                        <p>No evening slots available</p>
                    </div>
                    {% endfor %}
                </div>
            </div>
        </div>
    </div>

    <!-- Doctors Schedule -->
    <div class="bg-white shadow rounded-lg p-6">
        <h2 class="text-lg font-medium text-gray-900 mb-6">Doctor Availability</h2>
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {% for doctor in available_doctors %}
            <div class="border rounded-lg p-4 bg-gray-50">
                <div class="flex items-center mb-3">
                    <div class="w-10 h-10 bg-green-100 rounded-full flex items-center justify-center mr-3">
                        <i class="fas fa-user-md text-green-600"></i>
                    </div>
                    <div>
                        <div class="font-medium text-gray-900">Dr. {{ doctor.user.get_full_name }}</div>
                        <div class="text-sm text-gray-500">{{ doctor.specialization|default:"General Medicine" }}</div>
                    </div>
                </div>
                <div class="text-sm">
                    <div class="flex justify-between mb-1">
                        <span class="text-gray-600">Available:</span>
                        <span class="text-green-600 font-medium">9:00 AM - 5:00 PM</span>
                    </div>
                    <div class="flex justify-between mb-1">
                        <span class="text-gray-600">Appointments:</span>
                        <span class="text-blue-600 font-medium">{{ doctor.today_appointments|default:0 }}</span>
                    </div>
                    <div class="flex justify-between">
                        <span class="text-gray-600">Status:</span>
                        <span class="px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800">Available</span>
                    </div>
                </div>
            </div>
            {% empty %}
            <div class="col-span-full text-center py-8 text-gray-500">
                <i class="fas fa-user-md text-4xl mb-4"></i>
                <p>No doctors available today</p>
            </div>
            {% endfor %}
        </div>
    </div>

    <!-- Schedule Legend -->
    <div class="bg-white shadow rounded-lg p-6">
        <h2 class="text-lg font-medium text-gray-900 mb-4">Schedule Legend</h2>
        <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
            <div class="flex items-center">
                <div class="w-4 h-4 bg-green-100 border border-green-200 rounded mr-3"></div>
                <span class="text-sm text-gray-700">Available Slot</span>
            </div>
            <div class="flex items-center">
                <div class="w-4 h-4 bg-red-100 border border-red-200 rounded mr-3"></div>
                <span class="text-sm text-gray-700">Booked Slot</span>
            </div>
            <div class="flex items-center">
                <div class="w-4 h-4 bg-yellow-100 border border-yellow-200 rounded mr-3"></div>
                <span class="text-sm text-gray-700">Pending Confirmation</span>
            </div>
            <div class="flex items-center">
                <div class="w-4 h-4 bg-gray-100 border border-gray-200 rounded mr-3"></div>
                <span class="text-sm text-gray-700">Unavailable</span>
            </div>
        </div>
    </div>
</div>
{% endblock %}
