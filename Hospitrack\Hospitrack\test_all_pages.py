#!/usr/bin/env python
"""
Comprehensive test script to test all pages and functionality
"""
import os
import sys
import django
from django.test import Client
from django.urls import reverse

# Setup Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'hospitrack.settings')
django.setup()

from accounts.models import User, Doctor, Nurse, Admin

def test_page_access():
    """Test access to all major pages"""
    print("🔍 Testing Page Access and Functionality")
    print("=" * 60)
    
    client = Client()
    
    # Test cases: (url, expected_status, login_required, user_type_required)
    test_cases = [
        # Public pages
        ('/', 200, False, None),  # Landing page
        ('/login/', 200, False, None),  # Login page
        
        # Admin pages
        ('/admin-dashboard/', 200, True, 'admin'),
        ('/doctors/', 200, True, 'admin'),
        ('/doctors/create/', 200, True, 'admin'),
        ('/nurses/', 200, True, 'admin'),
        ('/nurses/create/', 200, True, 'admin'),
        
        # Doctor pages
        ('/doctor-dashboard/', 200, True, 'doctor'),
        ('/specialty-team/', 200, True, 'doctor'),
        
        # Nurse pages
        ('/nurse-dashboard/', 200, True, 'nurse'),
        ('/nurses/leave/', 200, True, 'admin'),
        
        # Patient management
        ('/patients/', 200, True, None),
        ('/patients/register/', 200, True, None),
        ('/patients/admission/', 200, True, None),
        
        # Appointment management
        ('/appointments/', 200, True, None),
        ('/appointments/opd-schedule/', 200, True, None),
        ('/appointments/opd-queue/', 200, True, None),
        
        # Bed management
        ('/beds/', 200, True, None),
        
        # Staff management
        ('/staff/', 200, True, None),
    ]
    
    # Test credentials
    test_users = {
        'admin': {'username': 'admin', 'password': 'admin123'},
        'doctor': {'username': 'dr.emergency', 'password': 'doctor123'},
        'nurse': {'username': 'emma.johnson1', 'password': 'nurse123'}
    }
    
    results = {'passed': 0, 'failed': 0, 'errors': []}
    
    for url, expected_status, login_required, user_type in test_cases:
        try:
            print(f"\n📄 Testing: {url}")
            
            # Logout first
            client.logout()
            
            if login_required:
                if user_type:
                    # Login with specific user type
                    login_success = client.login(**test_users[user_type])
                    if not login_success:
                        print(f"   ❌ Login failed for {user_type}")
                        results['failed'] += 1
                        results['errors'].append(f"Login failed for {user_type} on {url}")
                        continue
                else:
                    # Login with admin by default
                    client.login(**test_users['admin'])
            
            # Test the page
            response = client.get(url)
            
            if response.status_code == expected_status:
                print(f"   ✅ Status: {response.status_code} (Expected: {expected_status})")
                results['passed'] += 1
            else:
                print(f"   ❌ Status: {response.status_code} (Expected: {expected_status})")
                results['failed'] += 1
                results['errors'].append(f"{url}: Got {response.status_code}, expected {expected_status}")
                
                # Print response content for debugging if it's an error
                if response.status_code >= 400:
                    print(f"   🔍 Response content preview: {str(response.content[:200])}")
            
        except Exception as e:
            print(f"   ❌ Exception: {str(e)}")
            results['failed'] += 1
            results['errors'].append(f"{url}: Exception - {str(e)}")
    
    return results

def test_form_submissions():
    """Test form submissions and data creation"""
    print(f"\n🔍 Testing Form Submissions")
    print("=" * 60)
    
    client = Client()
    results = {'passed': 0, 'failed': 0, 'errors': []}
    
    # Login as admin
    client.login(username='admin', password='admin123')
    
    # Test patient registration
    print(f"\n📝 Testing Patient Registration Form")
    try:
        response = client.post('/patients/register/', {
            'patient_id': 'TEST001',
            'first_name': 'Test',
            'last_name': 'Patient',
            'date_of_birth': '1990-01-01',
            'gender': 'M',
            'blood_group': 'O+',
            'phone': '**********',
            'email': '<EMAIL>',
            'address': 'Test Address',
            'emergency_contact_name': 'Emergency Contact',
            'emergency_contact_phone': '**********',
            'emergency_contact_relation': 'Father'
        })
        
        if response.status_code in [200, 302]:  # Success or redirect
            print(f"   ✅ Patient registration form submitted successfully")
            results['passed'] += 1
        else:
            print(f"   ❌ Patient registration failed: {response.status_code}")
            results['failed'] += 1
            results['errors'].append(f"Patient registration failed: {response.status_code}")
            
    except Exception as e:
        print(f"   ❌ Patient registration exception: {str(e)}")
        results['failed'] += 1
        results['errors'].append(f"Patient registration exception: {str(e)}")
    
    # Test doctor creation
    print(f"\n📝 Testing Doctor Creation Form")
    try:
        response = client.post('/doctors/create/', {
            'username': 'test.doctor',
            'email': '<EMAIL>',
            'first_name': 'Test',
            'last_name': 'Doctor',
            'password': 'testpass123',
            'specialization': 'General Medicine',
            'license_number': 'TEST123',
            'department': 'General Medicine',
            'consultation_fee': '500'
        })
        
        if response.status_code in [200, 302]:
            print(f"   ✅ Doctor creation form submitted successfully")
            results['passed'] += 1
        else:
            print(f"   ❌ Doctor creation failed: {response.status_code}")
            results['failed'] += 1
            results['errors'].append(f"Doctor creation failed: {response.status_code}")
            
    except Exception as e:
        print(f"   ❌ Doctor creation exception: {str(e)}")
        results['failed'] += 1
        results['errors'].append(f"Doctor creation exception: {str(e)}")
    
    return results

def test_specialty_system():
    """Test the specialty-based access control system"""
    print(f"\n🔍 Testing Specialty System")
    print("=" * 60)
    
    client = Client()
    results = {'passed': 0, 'failed': 0, 'errors': []}
    
    # Test doctor accessing specialty team
    print(f"\n👨‍⚕️ Testing Doctor Specialty Team Access")
    try:
        client.login(username='dr.emergency', password='doctor123')
        response = client.get('/specialty-team/')
        
        if response.status_code == 200:
            print(f"   ✅ Doctor can access specialty team page")
            results['passed'] += 1
            
            # Check if the response contains nurse information
            content = response.content.decode()
            if 'Emergency Medicine' in content:
                print(f"   ✅ Specialty information displayed correctly")
                results['passed'] += 1
            else:
                print(f"   ❌ Specialty information not found in response")
                results['failed'] += 1
                results['errors'].append("Specialty information not displayed")
        else:
            print(f"   ❌ Doctor specialty team access failed: {response.status_code}")
            results['failed'] += 1
            results['errors'].append(f"Specialty team access failed: {response.status_code}")
            
    except Exception as e:
        print(f"   ❌ Specialty team test exception: {str(e)}")
        results['failed'] += 1
        results['errors'].append(f"Specialty team test exception: {str(e)}")
    
    client.logout()
    
    # Test nurse leave management (admin access)
    print(f"\n👩‍⚕️ Testing Nurse Leave Management (Admin Access)")
    try:
        client.login(username='admin', password='admin123')
        response = client.get('/nurses/leave/')
        
        if response.status_code == 200:
            print(f"   ✅ Admin can access nurse leave management page")
            results['passed'] += 1
        else:
            print(f"   ❌ Admin nurse leave management access failed: {response.status_code}")
            results['failed'] += 1
            results['errors'].append(f"Admin nurse leave management failed: {response.status_code}")
            
    except Exception as e:
        print(f"   ❌ Nurse leave management exception: {str(e)}")
        results['failed'] += 1
        results['errors'].append(f"Nurse leave management exception: {str(e)}")
    
    return results

def test_database_integrity():
    """Test database integrity and relationships"""
    print(f"\n🔍 Testing Database Integrity")
    print("=" * 60)
    
    results = {'passed': 0, 'failed': 0, 'errors': []}
    
    try:
        # Test user counts
        total_users = User.objects.count()
        admin_users = User.objects.filter(user_type='admin').count()
        doctor_users = User.objects.filter(user_type='doctor').count()
        nurse_users = User.objects.filter(user_type='nurse').count()
        
        print(f"📊 User Statistics:")
        print(f"   Total Users: {total_users}")
        print(f"   Admins: {admin_users}")
        print(f"   Doctors: {doctor_users}")
        print(f"   Nurses: {nurse_users}")
        
        # Test profile relationships
        doctors_with_profiles = Doctor.objects.count()
        nurses_with_profiles = Nurse.objects.count()
        admins_with_profiles = Admin.objects.count()
        
        print(f"\n👥 Profile Relationships:")
        print(f"   Doctor profiles: {doctors_with_profiles}")
        print(f"   Nurse profiles: {nurses_with_profiles}")
        print(f"   Admin profiles: {admins_with_profiles}")
        
        # Check for orphaned users (users without profiles)
        orphaned_doctors = doctor_users - doctors_with_profiles
        orphaned_nurses = nurse_users - nurses_with_profiles
        orphaned_admins = admin_users - admins_with_profiles
        
        if orphaned_doctors == 0 and orphaned_nurses == 0 and orphaned_admins == 0:
            print(f"   ✅ No orphaned user accounts found")
            results['passed'] += 1
        else:
            print(f"   ❌ Found orphaned accounts: Doctors({orphaned_doctors}), Nurses({orphaned_nurses}), Admins({orphaned_admins})")
            results['failed'] += 1
            results['errors'].append(f"Orphaned accounts found")
        
        # Test specialty assignments
        from accounts.models import MedicalSpecialty
        specialties = MedicalSpecialty.objects.count()
        doctors_with_specialty = Doctor.objects.filter(specialty__isnull=False).count()
        nurses_with_specialty = Nurse.objects.filter(specialty__isnull=False).count()
        
        print(f"\n🏥 Specialty Assignments:")
        print(f"   Total specialties: {specialties}")
        print(f"   Doctors with specialty: {doctors_with_specialty}/{doctors_with_profiles}")
        print(f"   Nurses with specialty: {nurses_with_specialty}/{nurses_with_profiles}")
        
        if doctors_with_specialty > 0 and nurses_with_specialty > 0:
            print(f"   ✅ Specialty assignments working correctly")
            results['passed'] += 1
        else:
            print(f"   ❌ Specialty assignment issues found")
            results['failed'] += 1
            results['errors'].append("Specialty assignment issues")
            
    except Exception as e:
        print(f"   ❌ Database integrity test exception: {str(e)}")
        results['failed'] += 1
        results['errors'].append(f"Database integrity exception: {str(e)}")
    
    return results

def main():
    """Run all tests"""
    print("🚀 Starting Comprehensive HospiTrack Testing")
    print("=" * 80)
    
    all_results = {'passed': 0, 'failed': 0, 'errors': []}
    
    # Run all test suites
    test_suites = [
        ("Page Access", test_page_access),
        ("Form Submissions", test_form_submissions),
        ("Specialty System", test_specialty_system),
        ("Database Integrity", test_database_integrity)
    ]
    
    for suite_name, test_function in test_suites:
        print(f"\n🧪 Running {suite_name} Tests...")
        results = test_function()
        all_results['passed'] += results['passed']
        all_results['failed'] += results['failed']
        all_results['errors'].extend(results['errors'])
        
        print(f"   📊 {suite_name} Results: {results['passed']} passed, {results['failed']} failed")
    
    # Final summary
    print(f"\n" + "=" * 80)
    print(f"🎯 FINAL TEST RESULTS")
    print(f"=" * 80)
    print(f"✅ Total Passed: {all_results['passed']}")
    print(f"❌ Total Failed: {all_results['failed']}")
    print(f"📊 Success Rate: {(all_results['passed']/(all_results['passed']+all_results['failed']))*100:.1f}%")
    
    if all_results['errors']:
        print(f"\n🔍 Error Details:")
        for i, error in enumerate(all_results['errors'], 1):
            print(f"   {i}. {error}")
    
    overall_status = "✅ PASSED" if all_results['failed'] == 0 else "❌ FAILED"
    print(f"\n🎯 Overall Status: {overall_status}")
    
    return all_results['failed'] == 0

if __name__ == '__main__':
    try:
        success = main()
        sys.exit(0 if success else 1)
    except Exception as e:
        print(f"❌ Test execution failed: {str(e)}")
        sys.exit(1)