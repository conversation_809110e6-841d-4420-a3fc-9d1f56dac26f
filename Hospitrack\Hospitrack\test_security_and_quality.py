#!/usr/bin/env python
"""
Security vulnerabilities and code quality testing
"""
import os
import sys
import django
from django.test import Client
from django.conf import settings

# Setup Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'hospitrack.settings')
django.setup()

from accounts.models import User

def test_security_vulnerabilities():
    """Test for common security vulnerabilities"""
    print("🔒 Testing Security Vulnerabilities")
    print("=" * 80)
    
    client = Client()
    results = {'passed': 0, 'failed': 0, 'errors': []}
    
    # Test 1: SQL Injection Prevention
    print(f"\n💉 Testing SQL Injection Prevention")
    try:
        # Try SQL injection in login form
        response = client.post('/login/', {
            'username': "admin'; DROP TABLE accounts_user; --",
            'password': 'admin123'
        })
        
        # Should not crash and users table should still exist
        user_count = User.objects.count()
        if user_count > 0:
            print(f"   ✅ SQL injection prevented (users table intact: {user_count} users)")
            results['passed'] += 1
        else:
            print(f"   ❌ Possible SQL injection vulnerability")
            results['failed'] += 1
            results['errors'].append("Possible SQL injection vulnerability")
            
    except Exception as e:
        print(f"   ❌ SQL injection test exception: {str(e)}")
        results['failed'] += 1
        results['errors'].append(f"SQL injection test exception: {str(e)}")
    
    # Test 2: XSS Prevention
    print(f"\n🕷️ Testing XSS Prevention")
    try:
        client.login(username='admin', password='admin123')
        
        # Try XSS in patient registration
        response = client.post('/patients/register/', {
            'patient_id': 'XSS_TEST_001',
            'first_name': '<script>alert("XSS")</script>',
            'last_name': 'Test',
            'date_of_birth': '1990-01-01',
            'gender': 'M',
            'blood_group': 'O+',
            'phone': '**********',
            'email': '<EMAIL>',
            'address': 'Test Address',
            'emergency_contact_name': 'Contact',
            'emergency_contact_phone': '**********',
            'emergency_contact_relation': 'Father'
        })
        
        if response.status_code in [200, 302]:
            # Check if script tags are escaped in subsequent page loads
            response = client.get('/patients/')
            content = response.content.decode()
            
            if '<script>' not in content and '&lt;script&gt;' in content:
                print(f"   ✅ XSS prevented (script tags escaped)")
                results['passed'] += 1
            elif '<script>' not in content:
                print(f"   ✅ XSS prevented (script tags filtered)")
                results['passed'] += 1
            else:
                print(f"   ❌ Possible XSS vulnerability")
                results['failed'] += 1
                results['errors'].append("Possible XSS vulnerability")
        else:
            print(f"   ⚠️  Cannot test XSS - form submission failed")
            results['errors'].append("Cannot test XSS - form submission failed")
            
    except Exception as e:
        print(f"   ❌ XSS test exception: {str(e)}")
        results['failed'] += 1
        results['errors'].append(f"XSS test exception: {str(e)}")
    
    # Test 3: CSRF Protection
    print(f"\n🛡️ Testing CSRF Protection")
    try:
        # Try to submit form without CSRF token
        client.logout()
        response = client.post('/login/', {
            'username': 'admin',
            'password': 'admin123'
        }, HTTP_X_CSRFTOKEN='invalid_token')
        
        # Should be rejected or handled properly
        if response.status_code in [403, 200]:  # 403 for CSRF failure, 200 for form with errors
            print(f"   ✅ CSRF protection active")
            results['passed'] += 1
        else:
            print(f"   ❌ CSRF protection may be weak")
            results['failed'] += 1
            results['errors'].append("CSRF protection may be weak")
            
    except Exception as e:
        print(f"   ❌ CSRF test exception: {str(e)}")
        results['failed'] += 1
        results['errors'].append(f"CSRF test exception: {str(e)}")
    
    # Test 4: Password Security
    print(f"\n🔑 Testing Password Security")
    try:
        # Check if passwords are hashed
        admin_user = User.objects.get(username='admin')
        if admin_user.password.startswith('pbkdf2_') or admin_user.password.startswith('argon2'):
            print(f"   ✅ Passwords are properly hashed")
            results['passed'] += 1
        else:
            print(f"   ❌ Passwords may not be properly hashed")
            results['failed'] += 1
            results['errors'].append("Passwords may not be properly hashed")
        
        # Test password verification
        if admin_user.check_password('admin123'):
            print(f"   ✅ Password verification works correctly")
            results['passed'] += 1
        else:
            print(f"   ❌ Password verification failed")
            results['failed'] += 1
            results['errors'].append("Password verification failed")
            
    except Exception as e:
        print(f"   ❌ Password security test exception: {str(e)}")
        results['failed'] += 1
        results['errors'].append(f"Password security test exception: {str(e)}")
    
    # Test 5: Session Security
    print(f"\n🍪 Testing Session Security")
    try:
        # Check session configuration
        session_cookie_secure = getattr(settings, 'SESSION_COOKIE_SECURE', False)
        session_cookie_httponly = getattr(settings, 'SESSION_COOKIE_HTTPONLY', True)
        
        if session_cookie_httponly:
            print(f"   ✅ Session cookies are HTTP-only")
            results['passed'] += 1
        else:
            print(f"   ⚠️  Session cookies are not HTTP-only (development OK)")
            results['errors'].append("Session cookies not HTTP-only")
        
        # Test session timeout
        client.login(username='admin', password='admin123')
        response = client.get('/admin-dashboard/')
        if response.status_code == 200:
            print(f"   ✅ Session management works correctly")
            results['passed'] += 1
        else:
            print(f"   ❌ Session management issues")
            results['failed'] += 1
            results['errors'].append("Session management issues")
            
    except Exception as e:
        print(f"   ❌ Session security test exception: {str(e)}")
        results['failed'] += 1
        results['errors'].append(f"Session security test exception: {str(e)}")
    
    return results

def test_code_quality():
    """Test code quality and best practices"""
    print(f"\n📝 Testing Code Quality")
    print("=" * 50)
    
    results = {'passed': 0, 'failed': 0, 'errors': []}
    
    # Test 1: Settings Configuration
    print(f"\n⚙️ Testing Settings Configuration")
    try:
        # Check DEBUG setting
        if settings.DEBUG:
            print(f"   ⚠️  DEBUG is True (OK for development)")
            results['errors'].append("DEBUG is True (change for production)")
        else:
            print(f"   ✅ DEBUG is False (production ready)")
            results['passed'] += 1
        
        # Check SECRET_KEY
        if hasattr(settings, 'SECRET_KEY') and len(settings.SECRET_KEY) > 20:
            print(f"   ✅ SECRET_KEY is configured")
            results['passed'] += 1
        else:
            print(f"   ❌ SECRET_KEY is missing or too short")
            results['failed'] += 1
            results['errors'].append("SECRET_KEY issues")
        
        # Check ALLOWED_HOSTS
        if settings.ALLOWED_HOSTS:
            print(f"   ✅ ALLOWED_HOSTS is configured: {settings.ALLOWED_HOSTS}")
            results['passed'] += 1
        else:
            print(f"   ❌ ALLOWED_HOSTS is empty")
            results['failed'] += 1
            results['errors'].append("ALLOWED_HOSTS is empty")
            
    except Exception as e:
        print(f"   ❌ Settings test exception: {str(e)}")
        results['failed'] += 1
        results['errors'].append(f"Settings test exception: {str(e)}")
    
    # Test 2: Database Configuration
    print(f"\n🗄️ Testing Database Configuration")
    try:
        db_config = settings.DATABASES['default']
        
        if db_config['ENGINE']:
            print(f"   ✅ Database engine configured: {db_config['ENGINE']}")
            results['passed'] += 1
        else:
            print(f"   ❌ Database engine not configured")
            results['failed'] += 1
            results['errors'].append("Database engine not configured")
        
        # Test database connection
        from django.db import connection
        with connection.cursor() as cursor:
            cursor.execute("SELECT 1")
            result = cursor.fetchone()
            if result:
                print(f"   ✅ Database connection works")
                results['passed'] += 1
            else:
                print(f"   ❌ Database connection failed")
                results['failed'] += 1
                results['errors'].append("Database connection failed")
                
    except Exception as e:
        print(f"   ❌ Database test exception: {str(e)}")
        results['failed'] += 1
        results['errors'].append(f"Database test exception: {str(e)}")
    
    # Test 3: Installed Apps
    print(f"\n📦 Testing Installed Apps")
    try:
        required_apps = [
            'django.contrib.admin',
            'django.contrib.auth',
            'django.contrib.contenttypes',
            'django.contrib.sessions',
            'django.contrib.messages',
            'django.contrib.staticfiles',
            'accounts',
            'patients',
            'appointments',
            'beds',
            'staff'
        ]
        
        missing_apps = []
        for app in required_apps:
            if app not in settings.INSTALLED_APPS:
                missing_apps.append(app)
        
        if not missing_apps:
            print(f"   ✅ All required apps are installed")
            results['passed'] += 1
        else:
            print(f"   ❌ Missing apps: {missing_apps}")
            results['failed'] += 1
            results['errors'].append(f"Missing apps: {missing_apps}")
            
    except Exception as e:
        print(f"   ❌ Installed apps test exception: {str(e)}")
        results['failed'] += 1
        results['errors'].append(f"Installed apps test exception: {str(e)}")
    
    # Test 4: URL Configuration
    print(f"\n🔗 Testing URL Configuration")
    try:
        from django.urls import reverse
        
        # Test important URL patterns
        important_urls = [
            ('landing', '/'),
            ('login', '/login/'),
            ('admin_dashboard', '/admin-dashboard/'),
            ('doctors_list', '/doctors/'),
            ('nurses_list', '/nurses/'),
        ]
        
        url_errors = []
        for name, expected_url in important_urls:
            try:
                if name == 'landing':
                    # Landing page doesn't have a name, test directly
                    continue
                elif name == 'login':
                    actual_url = reverse('login')
                elif name == 'admin_dashboard':
                    actual_url = reverse('admin_dashboard')
                elif name == 'doctors_list':
                    actual_url = reverse('doctors_list')
                elif name == 'nurses_list':
                    actual_url = reverse('nurses_list')
                
                if actual_url == expected_url:
                    print(f"   ✅ URL pattern '{name}' works correctly")
                    results['passed'] += 1
                else:
                    print(f"   ❌ URL pattern '{name}' mismatch: {actual_url} != {expected_url}")
                    url_errors.append(f"{name}: {actual_url} != {expected_url}")
            except Exception as url_e:
                print(f"   ❌ URL pattern '{name}' failed: {str(url_e)}")
                url_errors.append(f"{name}: {str(url_e)}")
        
        if url_errors:
            results['failed'] += len(url_errors)
            results['errors'].extend(url_errors)
            
    except Exception as e:
        print(f"   ❌ URL configuration test exception: {str(e)}")
        results['failed'] += 1
        results['errors'].append(f"URL configuration test exception: {str(e)}")
    
    return results

def main():
    """Run all security and quality tests"""
    print("🔒 Security and Code Quality Testing")
    print("=" * 80)
    
    all_results = {'passed': 0, 'failed': 0, 'errors': []}
    
    # Run test suites
    test_suites = [
        ("Security Vulnerabilities", test_security_vulnerabilities),
        ("Code Quality", test_code_quality)
    ]
    
    for suite_name, test_function in test_suites:
        print(f"\n🧪 Running {suite_name} Tests...")
        results = test_function()
        all_results['passed'] += results['passed']
        all_results['failed'] += results['failed']
        all_results['errors'].extend(results['errors'])
        
        print(f"   📊 {suite_name} Results: {results['passed']} passed, {results['failed']} failed")
    
    # Final summary
    print(f"\n" + "=" * 80)
    print(f"🎯 SECURITY & QUALITY TEST RESULTS")
    print(f"=" * 80)
    print(f"✅ Total Passed: {all_results['passed']}")
    print(f"❌ Total Failed: {all_results['failed']}")
    
    if all_results['passed'] + all_results['failed'] > 0:
        success_rate = (all_results['passed']/(all_results['passed']+all_results['failed']))*100
        print(f"📊 Success Rate: {success_rate:.1f}%")
    
    if all_results['errors']:
        print(f"\n🔍 Issues and Warnings:")
        for i, error in enumerate(all_results['errors'], 1):
            print(f"   {i}. {error}")
    
    # Security status
    if all_results['failed'] == 0:
        print(f"\n🛡️ SECURITY STATUS: ✅ SECURE")
        print(f"   System follows security best practices.")
    elif all_results['failed'] <= 2:
        print(f"\n🛡️ SECURITY STATUS: 🟡 MOSTLY SECURE")
        print(f"   Minor security considerations identified.")
    else:
        print(f"\n🛡️ SECURITY STATUS: 🔴 NEEDS ATTENTION")
        print(f"   Security vulnerabilities need to be addressed.")
    
    return all_results['failed'] <= 2

if __name__ == '__main__':
    try:
        success = main()
        sys.exit(0 if success else 1)
    except Exception as e:
        print(f"❌ Security and quality test execution failed: {str(e)}")
        sys.exit(1)