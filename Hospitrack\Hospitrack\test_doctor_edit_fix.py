#!/usr/bin/env python
"""
Test the doctor edit functionality fix
"""
import os
import sys
import django
from django.test import Client

# Setup Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'hospitrack.settings')
django.setup()

from accounts.models import User, Doctor

def test_doctor_edit_fix():
    """Test that doctor edit functionality works correctly"""
    print("🔧 Testing Doctor Edit Fix")
    print("=" * 50)
    
    client = Client()
    results = {'passed': 0, 'failed': 0, 'errors': []}
    
    # Login as admin
    login_success = client.login(username='admin', password='admin123')
    if not login_success:
        print("   ❌ Admin login failed")
        return results
    
    print("   ✅ Admin logged in successfully")
    results['passed'] += 1
    
    # Get a doctor to test with
    try:
        doctor = Doctor.objects.first()
        if not doctor:
            print("   ❌ No doctors found in database")
            results['failed'] += 1
            results['errors'].append("No doctors found")
            return results
        
        print(f"   ✅ Found doctor: Dr. {doctor.user.get_full_name()} (ID: {doctor.user.id})")
        results['passed'] += 1
        
        # Test accessing doctor edit page
        edit_url = f'/doctors/{doctor.user.id}/edit/'
        print(f"   🔗 Testing URL: {edit_url}")
        
        response = client.get(edit_url)
        if response.status_code == 200:
            print("   ✅ Doctor edit page loads successfully")
            results['passed'] += 1
        else:
            print(f"   ❌ Doctor edit page failed: {response.status_code}")
            results['failed'] += 1
            results['errors'].append(f"Edit page failed: {response.status_code}")
            return results
        
        # Test form submission
        form_data = {
            'first_name': doctor.user.first_name,
            'last_name': doctor.user.last_name,
            'email': doctor.user.email,
            'phone': doctor.user.phone or '**********',
            'specialization': doctor.specialization,
            'license_number': doctor.license_number,
            'department': doctor.department,
            'experience_years': doctor.experience_years or 5,
            'consultation_fee': doctor.consultation_fee or 500,
            'qualifications': doctor.qualifications or 'Test qualifications'
        }
        
        response = client.post(edit_url, form_data)
        
        if response.status_code == 302:  # Should redirect after successful update
            print("   ✅ Doctor edit form submission successful")
            results['passed'] += 1
            
            # Check if redirect URL is correct
            redirect_url = response.url
            expected_url = f'/doctors/{doctor.user.id}/'
            
            if redirect_url == expected_url:
                print(f"   ✅ Redirect URL correct: {redirect_url}")
                results['passed'] += 1
            else:
                print(f"   ❌ Redirect URL incorrect: {redirect_url} (expected: {expected_url})")
                results['failed'] += 1
                results['errors'].append(f"Incorrect redirect URL")
            
            # Test accessing the detail page after edit
            response = client.get(redirect_url)
            if response.status_code == 200:
                print("   ✅ Doctor detail page accessible after edit")
                results['passed'] += 1
            else:
                print(f"   ❌ Doctor detail page failed after edit: {response.status_code}")
                results['failed'] += 1
                results['errors'].append(f"Detail page failed after edit: {response.status_code}")
                
        else:
            print(f"   ❌ Doctor edit form submission failed: {response.status_code}")
            results['failed'] += 1
            results['errors'].append(f"Form submission failed: {response.status_code}")
            
            # Print response content for debugging
            if response.status_code == 500:
                print("   🔍 Server error occurred")
                
    except Exception as e:
        print(f"   ❌ Exception during test: {str(e)}")
        results['failed'] += 1
        results['errors'].append(f"Exception: {str(e)}")
    
    return results

def test_all_doctor_edit_links():
    """Test that all doctor edit links work correctly"""
    print(f"\n🔗 Testing All Doctor Edit Links")
    print("=" * 50)
    
    client = Client()
    results = {'passed': 0, 'failed': 0, 'errors': []}
    
    # Login as admin
    client.login(username='admin', password='admin123')
    
    try:
        # Test doctors list page links
        response = client.get('/doctors/')
        if response.status_code == 200:
            print("   ✅ Doctors list page accessible")
            results['passed'] += 1
            
            # Check if page contains doctor links
            content = response.content.decode()
            if 'doctor_detail' in content:
                print("   ✅ Doctor detail links found in list")
                results['passed'] += 1
            else:
                print("   ⚠️  No doctor detail links found in list")
                results['errors'].append("No doctor detail links in list")
        else:
            print(f"   ❌ Doctors list page failed: {response.status_code}")
            results['failed'] += 1
            results['errors'].append(f"Doctors list failed: {response.status_code}")
        
        # Test a specific doctor detail page
        doctor = Doctor.objects.first()
        if doctor:
            detail_url = f'/doctors/{doctor.user.id}/'
            response = client.get(detail_url)
            
            if response.status_code == 200:
                print(f"   ✅ Doctor detail page accessible: {detail_url}")
                results['passed'] += 1
                
                # Check if edit links are present and correct
                content = response.content.decode()
                edit_url = f'/doctors/{doctor.user.id}/edit/'
                
                if edit_url in content:
                    print("   ✅ Edit links correctly formatted in detail page")
                    results['passed'] += 1
                else:
                    print("   ❌ Edit links not found or incorrectly formatted")
                    results['failed'] += 1
                    results['errors'].append("Edit links not found in detail page")
            else:
                print(f"   ❌ Doctor detail page failed: {response.status_code}")
                results['failed'] += 1
                results['errors'].append(f"Doctor detail failed: {response.status_code}")
                
    except Exception as e:
        print(f"   ❌ Exception during link test: {str(e)}")
        results['failed'] += 1
        results['errors'].append(f"Link test exception: {str(e)}")
    
    return results

def main():
    """Run all doctor edit tests"""
    print("🔧 Doctor Edit Functionality Fix Testing")
    print("=" * 80)
    
    all_results = {'passed': 0, 'failed': 0, 'errors': []}
    
    # Run test suites
    test_suites = [
        ("Doctor Edit Fix", test_doctor_edit_fix),
        ("Doctor Edit Links", test_all_doctor_edit_links)
    ]
    
    for suite_name, test_function in test_suites:
        print(f"\n🧪 Running {suite_name} Tests...")
        results = test_function()
        all_results['passed'] += results['passed']
        all_results['failed'] += results['failed']
        all_results['errors'].extend(results['errors'])
        
        print(f"   📊 {suite_name} Results: {results['passed']} passed, {results['failed']} failed")
    
    # Final summary
    print(f"\n" + "=" * 80)
    print(f"🎯 DOCTOR EDIT FIX TEST RESULTS")
    print(f"=" * 80)
    print(f"✅ Total Passed: {all_results['passed']}")
    print(f"❌ Total Failed: {all_results['failed']}")
    
    if all_results['passed'] + all_results['failed'] > 0:
        success_rate = (all_results['passed']/(all_results['passed']+all_results['failed']))*100
        print(f"📊 Success Rate: {success_rate:.1f}%")
    
    if all_results['errors']:
        print(f"\n🔍 Issues Found:")
        for i, error in enumerate(all_results['errors'], 1):
            print(f"   {i}. {error}")
    
    # Fix status
    if all_results['failed'] == 0:
        print(f"\n🎉 FIX STATUS: ✅ SUCCESSFUL")
        print(f"   Doctor edit functionality is now working correctly.")
        print(f"   The NoReverseMatch error has been resolved.")
    elif all_results['failed'] <= 1:
        print(f"\n⚠️  FIX STATUS: 🟡 MOSTLY SUCCESSFUL")
        print(f"   Minor issues remain but core functionality works.")
    else:
        print(f"\n❌ FIX STATUS: 🔴 NEEDS MORE WORK")
        print(f"   Additional issues need to be resolved.")
    
    return all_results['failed'] == 0

if __name__ == '__main__':
    try:
        success = main()
        sys.exit(0 if success else 1)
    except Exception as e:
        print(f"❌ Doctor edit fix test execution failed: {str(e)}")
        sys.exit(1)