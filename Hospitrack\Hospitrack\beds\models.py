from django.db import models
from django.contrib.auth import get_user_model
from patients.models import Patient

User = get_user_model()

class Ward(models.Model):
    WARD_TYPE_CHOICES = [
        ('general', 'General Ward'),
        ('private', 'Private Ward'),
        ('icu', 'ICU'),
        ('emergency', 'Emergency'),
        ('pediatric', 'Pediatric'),
        ('maternity', 'Maternity'),
        ('surgery', 'Surgery'),
    ]

    name = models.CharField(max_length=100)
    ward_type = models.CharField(max_length=20, choices=WARD_TYPE_CHOICES)
    floor = models.IntegerField()
    capacity = models.IntegerField()
    description = models.TextField(blank=True, null=True)
    is_active = models.BooleanField(default=True)

    def __str__(self):
        return f"{self.name} - {self.get_ward_type_display()}"

    @property
    def available_beds(self):
        return self.beds.filter(status='available').count()

    @property
    def occupied_beds(self):
        return self.beds.filter(status='occupied').count()

    @property
    def maintenance_beds(self):
        return self.beds.filter(status='maintenance').count()

    @property
    def emergency_beds(self):
        return self.beds.filter(status='emergency').count()

class Bed(models.Model):
    STATUS_CHOICES = [
        ('available', 'Available'),
        ('occupied', 'Occupied'),
        ('maintenance', 'Under Maintenance'),
        ('emergency', 'Emergency Reserved'),
    ]

    bed_number = models.CharField(max_length=10)
    ward = models.ForeignKey(Ward, on_delete=models.CASCADE, related_name='beds')
    status = models.CharField(max_length=15, choices=STATUS_CHOICES, default='available')
    bed_type = models.CharField(max_length=50, default='Standard')  # Standard, ICU, Emergency, etc.
    daily_rate = models.DecimalField(max_digits=10, decimal_places=2, default=0.00)
    is_active = models.BooleanField(default=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        unique_together = ['ward', 'bed_number']
        ordering = ['ward', 'bed_number']

    def __str__(self):
        return f"{self.ward.name} - Bed {self.bed_number}"

class BedAssignment(models.Model):
    bed = models.ForeignKey(Bed, on_delete=models.CASCADE, related_name='assignments')
    patient = models.ForeignKey(Patient, on_delete=models.CASCADE, related_name='bed_assignments')
    assigned_date = models.DateTimeField(auto_now_add=True)
    discharge_date = models.DateTimeField(blank=True, null=True)
    assigned_by = models.ForeignKey(User, on_delete=models.CASCADE, related_name='bed_assignments')
    notes = models.TextField(blank=True, null=True)
    is_active = models.BooleanField(default=True)

    class Meta:
        ordering = ['-assigned_date']

    def __str__(self):
        return f"{self.patient.full_name} in {self.bed}"

    def save(self, *args, **kwargs):
        if self.is_active:
            # When assigning a bed, mark it as occupied
            self.bed.status = 'occupied'
            self.bed.save()
        super().save(*args, **kwargs)

class BedStatusUpdate(models.Model):
    bed = models.ForeignKey(Bed, on_delete=models.CASCADE, related_name='status_updates')
    previous_status = models.CharField(max_length=15)
    new_status = models.CharField(max_length=15)
    updated_by = models.ForeignKey(User, on_delete=models.CASCADE)
    reason = models.TextField(blank=True, null=True)
    updated_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        ordering = ['-updated_at']

    def __str__(self):
        return f"{self.bed} status changed from {self.previous_status} to {self.new_status}"
