from django.core.management.base import BaseCommand
from patients.models import Patient, PatientDischarge

class Command(BaseCommand):
    help = 'Fix patient discharge status - update is_active field for discharged patients'

    def handle(self, *args, **options):
        self.stdout.write('Fixing patient discharge status...')
        
        # Get all patients with discharge records
        discharged_patients = PatientDischarge.objects.all()
        
        fixed_count = 0
        for discharge in discharged_patients:
            patient = discharge.patient
            if patient.is_active:  # If patient is still marked as active
                patient.is_active = False
                patient.save()
                fixed_count += 1
                self.stdout.write(f'✓ Fixed status for {patient.full_name} - marked as inactive')
        
        self.stdout.write(f'\nFixed {fixed_count} patient(s) status.')
        
        # Show updated summary
        total_patients = Patient.objects.count()
        active_patients = Patient.objects.filter(is_active=True).count()
        inactive_patients = Patient.objects.filter(is_active=False).count()
        
        self.stdout.write(f'\nUPDATED SUMMARY:')
        self.stdout.write(f'Total patients: {total_patients}')
        self.stdout.write(f'Active patients: {active_patients}')
        self.stdout.write(f'Discharged patients: {inactive_patients}')
