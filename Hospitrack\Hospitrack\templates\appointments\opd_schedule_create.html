{% extends 'base.html' %}

{% block title %}Create OPD Schedule - HospiTrack{% endblock %}

{% block content %}
<div class="space-y-6">
    <!-- Header -->
    <div class="bg-white shadow rounded-lg p-6">
        <div class="flex justify-between items-center">
            <div>
                <h1 class="text-3xl font-bold text-gray-900">Create OPD Schedule</h1>
                <p class="text-gray-600 mt-2">Register patient for OPD consultation with specialist doctors</p>
            </div>
            <div class="flex space-x-3">
                <a href="{% url 'appointments:opd_schedule' %}" class="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
                    <i class="fas fa-arrow-left mr-2"></i>
                    Back to OPD Schedule
                </a>
            </div>
        </div>
    </div>

    <!-- OPD Schedule Form -->
    <div class="bg-white shadow rounded-lg p-6">
        <form method="post" class="space-y-6">
            {% csrf_token %}
            
            <!-- Patient and Doctor Selection -->
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">
                        Patient <span class="text-red-500">*</span>
                    </label>
                    <select name="patient" required class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500 bg-white">
                        <option value="">Select Patient</option>
                        {% for patient in patients %}
                        <option value="{{ patient.id }}">{{ patient.full_name }} ({{ patient.patient_id }}) - {{ patient.age }} years</option>
                        {% endfor %}
                    </select>
                </div>
                
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">
                        Specialist Doctor <span class="text-red-500">*</span>
                    </label>
                    <select name="doctor" required class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500 bg-white">
                        <option value="">Select Specialist Doctor</option>
                        {% for doctor in doctors %}
                        <option value="{{ doctor.user.id }}">Dr. {{ doctor.user.get_full_name }} - {{ doctor.specialization }} (₹{{ doctor.consultation_fee }})</option>
                        {% endfor %}
                    </select>
                </div>
            </div>

            <!-- Date and Time -->
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">
                        Schedule Date <span class="text-red-500">*</span>
                    </label>
                    <input type="date" name="schedule_date" required class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500">
                </div>
                
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">
                        Time Slot <span class="text-red-500">*</span>
                    </label>
                    <select name="time_slot" required class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500 bg-white">
                        <option value="">Select Time Slot</option>
                        <!-- Morning Slots -->
                        <optgroup label="Morning OPD (9:00 AM - 12:00 PM)">
                            <option value="09:00">9:00 AM</option>
                            <option value="09:30">9:30 AM</option>
                            <option value="10:00">10:00 AM</option>
                            <option value="10:30">10:30 AM</option>
                            <option value="11:00">11:00 AM</option>
                            <option value="11:30">11:30 AM</option>
                        </optgroup>
                        <!-- Afternoon Slots -->
                        <optgroup label="Afternoon OPD (2:00 PM - 5:00 PM)">
                            <option value="14:00">2:00 PM</option>
                            <option value="14:30">2:30 PM</option>
                            <option value="15:00">3:00 PM</option>
                            <option value="15:30">3:30 PM</option>
                            <option value="16:00">4:00 PM</option>
                            <option value="16:30">4:30 PM</option>
                        </optgroup>
                        <!-- Evening Slots -->
                        <optgroup label="Evening OPD (6:00 PM - 8:00 PM)">
                            <option value="18:00">6:00 PM</option>
                            <option value="18:30">6:30 PM</option>
                            <option value="19:00">7:00 PM</option>
                            <option value="19:30">7:30 PM</option>
                        </optgroup>
                    </select>
                </div>
            </div>

            <!-- Reason for Visit -->
            <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">
                    Reason for OPD Visit <span class="text-red-500">*</span>
                </label>
                <textarea name="reason" rows="3" required class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500 resize-vertical" placeholder="Enter the medical reason for this OPD consultation..."></textarea>
            </div>

            <!-- Special Instructions -->
            <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">
                    Special Instructions
                </label>
                <textarea name="special_instructions" rows="2" class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500 resize-vertical" placeholder="Any special instructions for the doctor or patient..."></textarea>
            </div>

            <!-- Recurring Schedule -->
            <div class="border border-gray-200 rounded-lg p-4 bg-gray-50">
                <div class="flex items-center mb-3">
                    <input type="checkbox" name="is_recurring" id="is_recurring" class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
                    <label for="is_recurring" class="ml-2 block text-sm font-medium text-gray-700">
                        Recurring OPD Schedule
                    </label>
                </div>
                <div id="recurring_options" class="hidden">
                    <label class="block text-sm font-medium text-gray-700 mb-2">
                        Number of Weeks
                    </label>
                    <select name="recurring_weeks" class="w-full md:w-48 px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500 bg-white">
                        <option value="1">1 Week</option>
                        <option value="2">2 Weeks</option>
                        <option value="3">3 Weeks</option>
                        <option value="4">4 Weeks</option>
                        <option value="6">6 Weeks</option>
                        <option value="8">8 Weeks</option>
                        <option value="12">12 Weeks</option>
                    </select>
                    <p class="text-xs text-gray-500 mt-1">Schedule will be created for the same day and time for the specified number of weeks.</p>
                </div>
            </div>

            <!-- Form Actions -->
            <div class="flex justify-end space-x-3 pt-6 border-t border-gray-200">
                <a href="{% url 'appointments:opd_schedule' %}" class="px-6 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
                    Cancel
                </a>
                <button type="submit" class="px-6 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700">
                    <i class="fas fa-calendar-plus mr-2"></i>
                    Create OPD Schedule
                </button>
            </div>
        </form>
    </div>

    <!-- Specialist Information -->
    <div class="bg-blue-50 border border-blue-200 rounded-lg p-4">
        <div class="flex">
            <div class="flex-shrink-0">
                <i class="fas fa-info-circle text-blue-400"></i>
            </div>
            <div class="ml-3">
                <h3 class="text-sm font-medium text-blue-800">OPD Specialist Information</h3>
                <div class="mt-2 text-sm text-blue-700">
                    <ul class="list-disc list-inside space-y-1">
                        <li><strong>Cardiologist:</strong> Heart and cardiovascular system specialist</li>
                        <li><strong>Nephrologist:</strong> Kidney and urinary system specialist</li>
                        <li><strong>Pulmonologist:</strong> Lung and respiratory system specialist</li>
                        <li><strong>Hepatologist:</strong> Liver and digestive system specialist</li>
                        <li><strong>Orthopedist:</strong> Bone, joint, and musculoskeletal specialist</li>
                        <li><strong>Pediatrician:</strong> Child health and development specialist</li>
                        <li><strong>ENT Specialist:</strong> Ear, nose, and throat specialist</li>
                        <li><strong>Neurosurgeon:</strong> Brain and spine surgery specialist</li>
                        <li><strong>Neurologist:</strong> Brain and nervous system specialist</li>
                        <li><strong>Psychiatrist:</strong> Mental health and behavioral specialist</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// Set minimum date to today
document.addEventListener('DOMContentLoaded', function() {
    const dateInput = document.querySelector('input[name="schedule_date"]');
    const today = new Date().toISOString().split('T')[0];
    dateInput.min = today;
    
    // Set default date to today
    if (!dateInput.value) {
        dateInput.value = today;
    }
    
    // Handle recurring schedule checkbox
    const recurringCheckbox = document.getElementById('is_recurring');
    const recurringOptions = document.getElementById('recurring_options');
    
    recurringCheckbox.addEventListener('change', function() {
        if (this.checked) {
            recurringOptions.classList.remove('hidden');
        } else {
            recurringOptions.classList.add('hidden');
        }
    });
});
</script>
{% endblock %}
