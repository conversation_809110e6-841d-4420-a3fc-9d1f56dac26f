<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}HospiTrack - Hospital Management System{% endblock %}</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: '#3B82F6',
                        secondary: '#1E40AF',
                        accent: '#10B981',
                        danger: '#EF4444',
                        warning: '#F59E0B',
                    }
                }
            }
        }
    </script>
</head>
<body class="bg-gray-50 min-h-screen">
    {% if user.is_authenticated %}
    <!-- Navigation Bar -->
    <nav class="bg-white shadow-lg border-b border-gray-200">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between h-16">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <h1 class="text-2xl font-bold text-primary">HospiTrack</h1>
                    </div>
                    <div class="hidden md:ml-6 md:flex md:space-x-8">
                        {% if user.user_type == 'admin' %}
                            <a href="{% url 'admin_dashboard' %}" class="{% if request.resolver_match.url_name == 'admin_dashboard' %}text-primary bg-primary bg-opacity-10 border-b-2 border-primary{% else %}text-gray-500 hover:text-primary{% endif %} px-3 py-2 rounded-md text-sm font-medium transition-colors duration-200">Dashboard</a>
                            <a href="{% url 'patients:list' %}" class="{% if 'patients' in request.resolver_match.namespace %}text-primary bg-primary bg-opacity-10 border-b-2 border-primary{% else %}text-gray-500 hover:text-primary{% endif %} px-3 py-2 rounded-md text-sm font-medium transition-colors duration-200">Patients</a>
                            <a href="{% url 'appointments:opd_schedule' %}" class="{% if 'appointments' in request.resolver_match.namespace %}text-primary bg-primary bg-opacity-10 border-b-2 border-primary{% else %}text-gray-500 hover:text-primary{% endif %} px-3 py-2 rounded-md text-sm font-medium transition-colors duration-200">OPD Management</a>
                            <a href="{% url 'beds:management' %}" class="{% if 'beds' in request.resolver_match.namespace %}text-primary bg-primary bg-opacity-10 border-b-2 border-primary{% else %}text-gray-500 hover:text-primary{% endif %} px-3 py-2 rounded-md text-sm font-medium transition-colors duration-200">Bed Management</a>
                            <a href="{% url 'staff:list' %}" class="{% if 'staff' in request.resolver_match.namespace %}text-primary bg-primary bg-opacity-10 border-b-2 border-primary{% else %}text-gray-500 hover:text-primary{% endif %} px-3 py-2 rounded-md text-sm font-medium transition-colors duration-200">Nurses</a>
                        {% elif user.user_type == 'doctor' %}
                            <a href="{% url 'doctor_dashboard' %}" class="{% if request.resolver_match.url_name == 'doctor_dashboard' %}text-primary bg-primary bg-opacity-10 border-b-2 border-primary{% else %}text-gray-500 hover:text-primary{% endif %} px-3 py-2 rounded-md text-sm font-medium transition-colors duration-200">Dashboard</a>
                            <a href="{% url 'appointments:opd_queue' %}" class="{% if 'appointments' in request.resolver_match.namespace %}text-primary bg-primary bg-opacity-10 border-b-2 border-primary{% else %}text-gray-500 hover:text-primary{% endif %} px-3 py-2 rounded-md text-sm font-medium transition-colors duration-200">Today's OPD Queue</a>
                            <a href="{% url 'patients:list' %}" class="{% if 'patients' in request.resolver_match.namespace %}text-primary bg-primary bg-opacity-10 border-b-2 border-primary{% else %}text-gray-500 hover:text-primary{% endif %} px-3 py-2 rounded-md text-sm font-medium transition-colors duration-200">Patients</a>
                        {% elif user.user_type == 'nurse' %}
                            <a href="{% url 'nurse_dashboard' %}" class="{% if request.resolver_match.url_name == 'nurse_dashboard' %}text-primary bg-primary bg-opacity-10 border-b-2 border-primary{% else %}text-gray-500 hover:text-primary{% endif %} px-3 py-2 rounded-md text-sm font-medium transition-colors duration-200">Dashboard</a>
                            <a href="{% url 'beds:management' %}" class="{% if 'beds' in request.resolver_match.namespace %}text-primary bg-primary bg-opacity-10 border-b-2 border-primary{% else %}text-gray-500 hover:text-primary{% endif %} px-3 py-2 rounded-md text-sm font-medium transition-colors duration-200">Bed Management</a>
                            <a href="{% url 'patients:updates_list' %}" class="{% if 'patients' in request.resolver_match.namespace %}text-primary bg-primary bg-opacity-10 border-b-2 border-primary{% else %}text-gray-500 hover:text-primary{% endif %} px-3 py-2 rounded-md text-sm font-medium transition-colors duration-200">Patient Updates</a>
                        {% endif %}
                    </div>
                </div>
                <div class="flex items-center space-x-4">
                    <div class="text-sm text-gray-700">
                        Welcome, <span class="font-medium">{{ user.first_name }} {{ user.last_name }}</span>
                        <span class="text-xs text-gray-500">({{ user.get_user_type_display }})</span>
                    </div>
                    <a href="{% url 'logout' %}" class="bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-md text-sm font-medium">
                        <i class="fas fa-sign-out-alt mr-1"></i> Logout
                    </a>
                </div>
            </div>
        </div>
    </nav>
    {% endif %}

    <!-- Messages -->
    {% if messages %}
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 mt-4">
        {% for message in messages %}
        <div class="alert alert-{{ message.tags }} mb-4 p-4 rounded-md {% if message.tags == 'error' %}bg-red-100 border border-red-400 text-red-700{% elif message.tags == 'success' %}bg-green-100 border border-green-400 text-green-700{% elif message.tags == 'warning' %}bg-yellow-100 border border-yellow-400 text-yellow-700{% else %}bg-blue-100 border border-blue-400 text-blue-700{% endif %}">
            {{ message }}
        </div>
        {% endfor %}
    </div>
    {% endif %}

    <!-- Main Content -->
    <main class="{% if user.is_authenticated %}max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8{% endif %}">
        {% block content %}
        {% endblock %}
    </main>

    <!-- Footer -->
    <footer class="bg-white border-t border-gray-200 mt-auto">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4">
            <div class="text-center text-sm text-gray-500">
                © 2025 HospiTrack. All rights reserved.
            </div>
        </div>
    </footer>

    <script>
        // Auto-hide messages after 5 seconds
        setTimeout(function() {
            const alerts = document.querySelectorAll('.alert');
            alerts.forEach(function(alert) {
                alert.style.transition = 'opacity 0.5s';
                alert.style.opacity = '0';
                setTimeout(function() {
                    alert.remove();
                }, 500);
            });
        }, 5000);
    </script>
</body>
</html>
