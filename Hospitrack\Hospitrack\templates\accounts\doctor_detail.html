{% extends 'base.html' %}

{% block title %}Dr. {{ doctor.user.get_full_name }} - Doctor Details{% endblock %}

{% block content %}
<div class="space-y-6">
    <!-- Header -->
    <div class="bg-white shadow rounded-lg p-6">
        <div class="flex justify-between items-start">
            <div>
                <h1 class="text-3xl font-bold text-gray-900">Dr. {{ doctor.user.get_full_name }}</h1>
                <p class="text-gray-600 mt-2">{{ doctor.specialization }}</p>
                <div class="mt-2 flex items-center space-x-4 text-sm text-gray-500">
                    <span>{{ doctor.department }}</span>
                    <span>License: {{ doctor.license_number }}</span>
                    <span>{{ doctor.experience_years }} years experience</span>
                </div>
            </div>
            <div class="flex space-x-3">
                <a href="{% url 'doctors_list' %}" class="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
                    <i class="fas fa-arrow-left mr-2"></i>
                    Back to Doctors
                </a>
                {% if user.user_type == 'admin' %}
                <a href="{% url 'doctor_edit' doctor.user.id %}" class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700">
                    <i class="fas fa-edit mr-2"></i>
                    Edit Doctor
                </a>
                {% endif %}
            </div>
        </div>
    </div>

    <!-- Doctor Information Grid -->
    <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <!-- Personal Information -->
        <div class="bg-white shadow rounded-lg p-6">
            <h2 class="text-lg font-medium text-gray-900 mb-4">Personal Information</h2>
            <dl class="space-y-3">
                <div>
                    <dt class="text-sm font-medium text-gray-500">Full Name</dt>
                    <dd class="text-sm text-gray-900">Dr. {{ doctor.user.get_full_name }}</dd>
                </div>
                <div>
                    <dt class="text-sm font-medium text-gray-500">Email</dt>
                    <dd class="text-sm text-gray-900">{{ doctor.user.email }}</dd>
                </div>
                <div>
                    <dt class="text-sm font-medium text-gray-500">Phone</dt>
                    <dd class="text-sm text-gray-900">{{ doctor.user.phone|default:"Not provided" }}</dd>
                </div>
                <div>
                    <dt class="text-sm font-medium text-gray-500">Username</dt>
                    <dd class="text-sm text-gray-900">{{ doctor.user.username }}</dd>
                </div>
            </dl>
        </div>

        <!-- Professional Information -->
        <div class="bg-white shadow rounded-lg p-6">
            <h2 class="text-lg font-medium text-gray-900 mb-4">Professional Information</h2>
            <dl class="space-y-3">
                <div>
                    <dt class="text-sm font-medium text-gray-500">Specialization</dt>
                    <dd class="text-sm text-gray-900">
                        <span class="px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800">
                            {{ doctor.specialization }}
                        </span>
                    </dd>
                </div>
                <div>
                    <dt class="text-sm font-medium text-gray-500">Department</dt>
                    <dd class="text-sm text-gray-900">{{ doctor.department }}</dd>
                </div>
                <div>
                    <dt class="text-sm font-medium text-gray-500">License Number</dt>
                    <dd class="text-sm text-gray-900">{{ doctor.license_number }}</dd>
                </div>
                <div>
                    <dt class="text-sm font-medium text-gray-500">Experience</dt>
                    <dd class="text-sm text-gray-900">{{ doctor.experience_years }} years</dd>
                </div>
                <div>
                    <dt class="text-sm font-medium text-gray-500">Consultation Fee</dt>
                    <dd class="text-sm text-gray-900">${{ doctor.consultation_fee|floatformat:2 }}</dd>
                </div>
            </dl>
        </div>

        <!-- Statistics -->
        <div class="bg-white shadow rounded-lg p-6">
            <h2 class="text-lg font-medium text-gray-900 mb-4">Statistics</h2>
            <dl class="space-y-3">
                <div>
                    <dt class="text-sm font-medium text-gray-500">Total Patients</dt>
                    <dd class="text-2xl font-bold text-blue-600">{{ doctor.total_patients|default:0 }}</dd>
                </div>
                <div>
                    <dt class="text-sm font-medium text-gray-500">Today's Appointments</dt>
                    <dd class="text-2xl font-bold text-green-600">{{ doctor.today_appointments|default:0 }}</dd>
                </div>
                <div>
                    <dt class="text-sm font-medium text-gray-500">This Month</dt>
                    <dd class="text-2xl font-bold text-purple-600">{{ doctor.month_appointments|default:0 }}</dd>
                </div>
                <div>
                    <dt class="text-sm font-medium text-gray-500">Status</dt>
                    <dd class="text-sm text-gray-900">
                        <span class="px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800">
                            Active
                        </span>
                    </dd>
                </div>
            </dl>
        </div>
    </div>

    <!-- Qualifications -->
    {% if doctor.qualifications %}
    <div class="bg-white shadow rounded-lg p-6">
        <h2 class="text-lg font-medium text-gray-900 mb-4">Qualifications</h2>
        <div class="bg-gray-50 rounded-lg p-4">
            <p class="text-sm text-gray-900">{{ doctor.qualifications }}</p>
        </div>
    </div>
    {% endif %}

    <!-- Quick Actions -->
    <div class="bg-white shadow rounded-lg p-6">
        <h2 class="text-lg font-medium text-gray-900 mb-4">Quick Actions</h2>
        <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
            {% if user.user_type == 'admin' %}
            <a href="{% url 'doctor_edit' doctor.user.id %}" class="inline-flex items-center justify-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700">
                <i class="fas fa-edit mr-2"></i>
                Edit Details
            </a>
            
            <a href="#" class="inline-flex items-center justify-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-green-600 hover:bg-green-700">
                <i class="fas fa-calendar mr-2"></i>
                View Schedule
            </a>
            
            <a href="#" class="inline-flex items-center justify-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-purple-600 hover:bg-purple-700">
                <i class="fas fa-users mr-2"></i>
                View Patients
            </a>
            
            <a href="{% url 'doctors_list' %}" class="inline-flex items-center justify-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
                <i class="fas fa-list mr-2"></i>
                Back to List
            </a>
            {% endif %}
        </div>
    </div>

    <!-- Recent Activity -->
    <div class="bg-white shadow rounded-lg p-6">
        <h2 class="text-lg font-medium text-gray-900 mb-6">Recent Activity</h2>
        <div class="flow-root">
            <ul class="-mb-8">
                <li>
                    <div class="relative pb-8">
                        <div class="relative flex space-x-3">
                            <div>
                                <span class="h-8 w-8 rounded-full bg-green-500 flex items-center justify-center ring-8 ring-white">
                                    <i class="fas fa-user-plus text-white text-xs"></i>
                                </span>
                            </div>
                            <div class="min-w-0 flex-1 pt-1.5 flex justify-between space-x-4">
                                <div>
                                    <p class="text-sm text-gray-500">Doctor profile created</p>
                                </div>
                                <div class="text-right text-sm whitespace-nowrap text-gray-500">
                                    <time>{{ doctor.user.date_joined|date:"M d, Y" }}</time>
                                </div>
                            </div>
                        </div>
                    </div>
                </li>
                <li>
                    <div class="relative pb-8">
                        <div class="relative flex space-x-3">
                            <div>
                                <span class="h-8 w-8 rounded-full bg-blue-500 flex items-center justify-center ring-8 ring-white">
                                    <i class="fas fa-stethoscope text-white text-xs"></i>
                                </span>
                            </div>
                            <div class="min-w-0 flex-1 pt-1.5 flex justify-between space-x-4">
                                <div>
                                    <p class="text-sm text-gray-500">Assigned to {{ doctor.department }} department</p>
                                </div>
                                <div class="text-right text-sm whitespace-nowrap text-gray-500">
                                    <time>{{ doctor.user.date_joined|date:"M d, Y" }}</time>
                                </div>
                            </div>
                        </div>
                    </div>
                </li>
                <li>
                    <div class="relative">
                        <div class="relative flex space-x-3">
                            <div>
                                <span class="h-8 w-8 rounded-full bg-purple-500 flex items-center justify-center ring-8 ring-white">
                                    <i class="fas fa-check text-white text-xs"></i>
                                </span>
                            </div>
                            <div class="min-w-0 flex-1 pt-1.5 flex justify-between space-x-4">
                                <div>
                                    <p class="text-sm text-gray-500">Account activated and ready</p>
                                </div>
                                <div class="text-right text-sm whitespace-nowrap text-gray-500">
                                    <time>{{ doctor.user.date_joined|date:"M d, Y" }}</time>
                                </div>
                            </div>
                        </div>
                    </div>
                </li>
            </ul>
        </div>
    </div>
</div>
{% endblock %}
