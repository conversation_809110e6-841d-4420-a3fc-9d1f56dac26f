from django.urls import path
from . import views

app_name = 'appointments'

urlpatterns = [
    path('', views.appointment_list, name='list'),
    path('create/', views.appointment_create, name='create'),
    path('schedule/', views.appointment_schedule, name='schedule'),
    path('<int:appointment_id>/', views.appointment_detail, name='detail'),
    path('<int:appointment_id>/edit/', views.appointment_edit, name='edit'),
    path('opd-schedule/', views.opd_schedule, name='opd_schedule'),
    path('opd-schedule/create/', views.opd_schedule_create, name='opd_schedule_create'),
    path('opd-registration/', views.opd_registration, name='opd_registration'),
    path('opd-queue/', views.opd_queue, name='opd_queue'),
    path('opd-management/', views.opd_management, name='opd_management'),
    path('opd/<int:registration_id>/', views.opd_detail, name='opd_detail'),
    path('opd/<int:registration_id>/edit/', views.opd_edit, name='opd_edit'),
    path('opd/<int:registration_id>/cancel/', views.opd_cancel, name='opd_cancel'),
    path('opd/<int:registration_id>/remove/', views.opd_remove, name='opd_remove'),
    path('today/', views.today_appointments, name='today'),
]
