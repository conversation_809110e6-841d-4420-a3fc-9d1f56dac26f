{% extends 'base.html' %}

{% block title %}Manage Leave - {{ nurse.user.get_full_name }} - HospiTrack{% endblock %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="max-w-2xl mx-auto">
        <div class="bg-white rounded-lg shadow-md p-6">
            <div class="flex items-center mb-6">
                <div class="bg-blue-100 p-3 rounded-full mr-4">
                    <svg class="w-6 h-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                    </svg>
                </div>
                <div>
                    <h1 class="text-2xl font-bold text-gray-900">Manage Leave Status</h1>
                    <p class="text-gray-600">{{ nurse.user.get_full_name }} - {{ nurse.specialty.name if nurse.specialty else nurse.department }}</p>
                </div>
            </div>

            {% if messages %}
                {% for message in messages %}
                    <div class="mb-4 p-4 rounded-md {% if message.tags == 'error' %}bg-red-50 text-red-700 border border-red-200{% else %}bg-green-50 text-green-700 border border-green-200{% endif %}">
                        {{ message }}
                    </div>
                {% endfor %}
            {% endif %}

            <!-- Current Status -->
            <div class="mb-6 p-4 bg-gray-50 rounded-lg">
                <h3 class="text-lg font-medium text-gray-900 mb-2">Current Status</h3>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                        <span class="text-sm text-gray-500">Status:</span>
                        <span class="ml-2 inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium 
                            {% if nurse.leave_status == 'available' %}bg-green-100 text-green-800
                            {% else %}bg-red-100 text-red-800{% endif %}">
                            {{ nurse.leave_status_display }}
                        </span>
                    </div>
                    <div>
                        <span class="text-sm text-gray-500">Shift:</span>
                        <span class="ml-2 text-sm font-medium text-gray-900">
                            {% if nurse.shift == 'morning' %}Morning (6 AM - 2 PM)
                            {% elif nurse.shift == 'evening' %}Evening (2 PM - 10 PM)
                            {% else %}Night (10 PM - 6 AM){% endif %}
                        </span>
                    </div>
                    {% if nurse.current_leave_info %}
                    <div class="md:col-span-2">
                        <span class="text-sm text-gray-500">Leave Period:</span>
                        <span class="ml-2 text-sm text-gray-900">
                            {{ nurse.leave_start_date|date:"M d, Y" }} to {{ nurse.leave_end_date|date:"M d, Y" }}
                            {% if nurse.current_leave_info.days_remaining > 0 %}
                                ({{ nurse.current_leave_info.days_remaining }} days remaining)
                            {% endif %}
                        </span>
                    </div>
                    {% endif %}
                </div>
            </div>

            <form method="post" class="space-y-6">
                {% csrf_token %}
                
                <!-- Leave Status -->
                <div>
                    <label for="leave_status" class="block text-sm font-medium text-gray-700 mb-1">Leave Status *</label>
                    <select id="leave_status" name="leave_status" required 
                            class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500 bg-white">
                        {% for value, label in leave_choices %}
                            <option value="{{ value }}" {% if nurse.leave_status == value %}selected{% endif %}>{{ label }}</option>
                        {% endfor %}
                    </select>
                </div>

                <!-- Leave Dates (shown when not available) -->
                <div id="leave-dates" class="space-y-4" {% if nurse.leave_status == 'available' %}style="display: none;"{% endif %}>
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                            <label for="leave_start_date" class="block text-sm font-medium text-gray-700 mb-1">Start Date</label>
                            <input type="date" id="leave_start_date" name="leave_start_date" 
                                   value="{{ nurse.leave_start_date|date:'Y-m-d' }}"
                                   class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500">
                        </div>
                        <div>
                            <label for="leave_end_date" class="block text-sm font-medium text-gray-700 mb-1">End Date</label>
                            <input type="date" id="leave_end_date" name="leave_end_date" 
                                   value="{{ nurse.leave_end_date|date:'Y-m-d' }}"
                                   class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500">
                        </div>
                    </div>
                    
                    <div>
                        <label for="leave_reason" class="block text-sm font-medium text-gray-700 mb-1">Reason</label>
                        <textarea id="leave_reason" name="leave_reason" rows="3"
                                  class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500 resize-vertical"
                                  placeholder="Reason for leave...">{{ nurse.leave_reason }}</textarea>
                    </div>
                </div>

                <!-- Action Buttons -->
                <div class="flex justify-end space-x-3 pt-6 border-t border-gray-200">
                    <a href="{% url 'nurses_list' %}" 
                       class="px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                        Cancel
                    </a>
                    <button type="submit" 
                            class="px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                        Update Leave Status
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
document.getElementById('leave_status').addEventListener('change', function() {
    const leaveDates = document.getElementById('leave-dates');
    if (this.value === 'available') {
        leaveDates.style.display = 'none';
        // Clear the date fields when setting to available
        document.getElementById('leave_start_date').value = '';
        document.getElementById('leave_end_date').value = '';
        document.getElementById('leave_reason').value = '';
    } else {
        leaveDates.style.display = 'block';
    }
});
</script>
{% endblock %}