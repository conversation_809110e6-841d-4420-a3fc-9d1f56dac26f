from django.core.management.base import BaseCommand
from patients.models import Patient, PatientDischarge

class Command(BaseCommand):
    help = 'Check patient discharge status'

    def handle(self, *args, **options):
        self.stdout.write('Checking patient discharge status...')
        
        patients = Patient.objects.all()
        
        for patient in patients:
            discharge_info = None
            try:
                discharge_info = PatientDischarge.objects.get(patient=patient)
            except PatientDischarge.DoesNotExist:
                pass
            
            status = "DISCHARGED" if discharge_info else "ACTIVE"
            is_active = patient.is_active
            is_discharged_prop = patient.is_discharged
            
            self.stdout.write(f'Patient: {patient.full_name} ({patient.patient_id})')
            self.stdout.write(f'  - is_active field: {is_active}')
            self.stdout.write(f'  - is_discharged property: {is_discharged_prop}')
            self.stdout.write(f'  - has discharge record: {status}')
            if discharge_info:
                self.stdout.write(f'  - discharged on: {discharge_info.discharge_date}')
            self.stdout.write('---')
        
        total_patients = patients.count()
        active_patients = patients.filter(is_active=True).count()
        discharged_patients = PatientDischarge.objects.count()
        
        self.stdout.write(f'\nSUMMARY:')
        self.stdout.write(f'Total patients: {total_patients}')
        self.stdout.write(f'Active patients (is_active=True): {active_patients}')
        self.stdout.write(f'Patients with discharge records: {discharged_patients}')
