{% extends 'base.html' %}

{% block title %}Login - HospiTrack{% endblock %}

{% block content %}
<div class="min-h-screen flex items-center justify-center bg-gradient-to-br from-blue-50 to-indigo-100 py-12 px-4 sm:px-6 lg:px-8">
    <div class="max-w-lg w-full space-y-8">
        <div class="text-center">
            <div class="mx-auto h-16 w-16 flex items-center justify-center rounded-full bg-primary shadow-lg">
                <i class="fas fa-hospital text-white text-2xl"></i>
            </div>
            <h1 class="mt-6 text-center text-4xl font-extrabold text-gray-900">
                HospiTrack
            </h1>
            <h2 class="mt-2 text-center text-xl font-semibold text-gray-700">
                Hospital Management System
            </h2>
            <p class="mt-4 text-center text-sm text-gray-600">
                Please sign in to access your dashboard
            </p>
        </div>
        
        <!-- Display Messages -->
        {% if messages %}
            {% for message in messages %}
            <div class="rounded-md p-4 {% if message.tags == 'error' %}bg-red-50 border border-red-200{% elif message.tags == 'success' %}bg-green-50 border border-green-200{% else %}bg-blue-50 border border-blue-200{% endif %}">
                <div class="flex">
                    <div class="flex-shrink-0">
                        {% if message.tags == 'error' %}
                            <i class="fas fa-exclamation-circle text-red-400"></i>
                        {% elif message.tags == 'success' %}
                            <i class="fas fa-check-circle text-green-400"></i>
                        {% else %}
                            <i class="fas fa-info-circle text-blue-400"></i>
                        {% endif %}
                    </div>
                    <div class="ml-3">
                        <p class="text-sm {% if message.tags == 'error' %}text-red-800{% elif message.tags == 'success' %}text-green-800{% else %}text-blue-800{% endif %}">
                            {{ message }}
                        </p>
                    </div>
                </div>
            </div>
            {% endfor %}
        {% endif %}

        <div class="mt-8 bg-white py-8 px-6 shadow-xl rounded-lg">
            <form class="space-y-6" method="post">
                {% csrf_token %}
                <div class="space-y-4">
                    <div>
                        <label for="username" class="block text-sm font-medium text-gray-700 mb-2">Username</label>
                        <div class="relative">
                            <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                <i class="fas fa-user text-gray-400"></i>
                            </div>
                            <input id="username" name="username" type="text" required
                                   class="block w-full pl-10 pr-3 py-3 border border-gray-300 rounded-md leading-5 bg-white placeholder-gray-500 focus:outline-none focus:placeholder-gray-400 focus:ring-2 focus:ring-primary focus:border-primary text-base"
                                   placeholder="Enter your username">
                        </div>
                    </div>
                    <div>
                        <label for="password" class="block text-sm font-medium text-gray-700 mb-2">Password</label>
                        <div class="relative">
                            <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                <i class="fas fa-lock text-gray-400"></i>
                            </div>
                            <input id="password" name="password" type="password" required
                                   class="block w-full pl-10 pr-3 py-3 border border-gray-300 rounded-md leading-5 bg-white placeholder-gray-500 focus:outline-none focus:placeholder-gray-400 focus:ring-2 focus:ring-primary focus:border-primary text-base"
                                   placeholder="Enter your password">
                        </div>
                    </div>
                </div>

                <div class="mt-6">
                    <button type="submit"
                            class="group relative w-full flex justify-center py-3 px-4 border border-transparent text-base font-medium rounded-md text-white bg-primary hover:bg-secondary focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary transition duration-150 ease-in-out">
                        <span class="absolute left-0 inset-y-0 flex items-center pl-3">
                            <i class="fas fa-sign-in-alt text-white"></i>
                        </span>
                        Sign In to HospiTrack
                    </button>
                </div>
            </form>

            <div class="mt-6 text-center">
                <a href="{% url 'landing' %}" class="text-primary hover:text-secondary text-sm font-medium">
                    ← Back to Home
                </a>
            </div>

            <!-- Role Information -->
            <div class="mt-6 pt-6 border-t border-gray-200">
                <h3 class="text-sm font-medium text-gray-900 text-center mb-3">Access Levels</h3>
                <div class="grid grid-cols-3 gap-3 text-center">
                    <div class="p-2 bg-blue-50 rounded-md">
                        <i class="fas fa-user-cog text-blue-600 mb-1"></i>
                        <div class="text-xs font-medium text-blue-800">Admin</div>
                        <div class="text-xs text-blue-600">Full Access</div>
                    </div>
                    <div class="p-2 bg-green-50 rounded-md">
                        <i class="fas fa-user-md text-green-600 mb-1"></i>
                        <div class="text-xs font-medium text-green-800">Doctor</div>
                        <div class="text-xs text-green-600">Medical Care</div>
                    </div>
                    <div class="p-2 bg-purple-50 rounded-md">
                        <i class="fas fa-user-nurse text-purple-600 mb-1"></i>
                        <div class="text-xs font-medium text-purple-800">Nurse</div>
                        <div class="text-xs text-purple-600">Patient Care</div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Security Notice -->
        <div class="mt-6 text-center">
            <p class="text-xs text-gray-500">
                <i class="fas fa-shield-alt mr-1"></i>
                Secure hospital management system
            </p>
        </div>
    </div>
</div>
{% endblock %}
