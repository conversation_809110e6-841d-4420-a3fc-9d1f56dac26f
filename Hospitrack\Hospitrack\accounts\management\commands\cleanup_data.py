from django.core.management.base import BaseCommand
from django.contrib.auth import get_user_model
from accounts.models import Nurse
from staff.models import StaffMember
from patients.models import Patient
from appointments.models import Appointment

User = get_user_model()

class Command(BaseCommand):
    help = 'Remove 40 nurses and clear fake data'

    def handle(self, *args, **options):
        self.stdout.write('Starting data cleanup...')
        
        try:
            # Remove 40 nurses (keep 10)
            self.stdout.write('Removing 40 nurses...')
            
            # Get all nurse users except the first 10
            nurse_users = User.objects.filter(user_type='nurse').order_by('id')[10:]
            
            removed_count = 0
            for user in nurse_users:
                try:
                    # Remove associated staff member
                    if hasattr(user, 'staffmember'):
                        user.staffmember.delete()
                    
                    # Remove nurse profile
                    if hasattr(user, 'nurse'):
                        user.nurse.delete()
                    
                    # Remove user
                    username = user.username
                    user.delete()
                    removed_count += 1
                    
                    if removed_count >= 40:
                        break
                        
                except Exception as e:
                    self.stdout.write(f'Error removing nurse {user.username}: {str(e)}')
                    continue
            
            self.stdout.write(f'✓ Removed {removed_count} nurses')
            
            # Clear fake patient data (keep real patients)
            self.stdout.write('Clearing fake patient data...')
            
            # Remove patients with obviously fake data
            fake_patients = Patient.objects.filter(
                first_name__in=['Test', 'Fake', 'Demo', 'Sample']
            )
            fake_count = fake_patients.count()
            fake_patients.delete()
            
            self.stdout.write(f'✓ Removed {fake_count} fake patients')
            
            # Clear fake appointments
            self.stdout.write('Clearing fake appointments...')
            
            # Remove appointments for deleted patients
            orphaned_appointments = Appointment.objects.filter(patient__isnull=True)
            orphaned_count = orphaned_appointments.count()
            orphaned_appointments.delete()
            
            self.stdout.write(f'✓ Removed {orphaned_count} orphaned appointments')
            
            # Summary
            remaining_nurses = User.objects.filter(user_type='nurse').count()
            remaining_patients = Patient.objects.filter(is_active=True).count()
            remaining_appointments = Appointment.objects.count()
            
            self.stdout.write(self.style.SUCCESS('\n=== CLEANUP SUMMARY ==='))
            self.stdout.write(f'Remaining nurses: {remaining_nurses}')
            self.stdout.write(f'Remaining patients: {remaining_patients}')
            self.stdout.write(f'Remaining appointments: {remaining_appointments}')
            self.stdout.write(self.style.SUCCESS('Data cleanup completed successfully!'))
            
        except Exception as e:
            self.stdout.write(self.style.ERROR(f'Error during cleanup: {str(e)}'))
