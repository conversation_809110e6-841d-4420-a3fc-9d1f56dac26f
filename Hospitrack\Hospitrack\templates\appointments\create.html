{% extends 'base.html' %}

{% block title %}Schedule New Appointment - HospiTrack{% endblock %}

{% block content %}
<div class="space-y-6">
    <!-- Header -->
    <div class="bg-white shadow rounded-lg p-6">
        <div class="flex justify-between items-center">
            <div>
                <h1 class="text-3xl font-bold text-gray-900">Schedule New Appointment</h1>
                <p class="text-gray-600 mt-2">Create a new appointment for a patient with a doctor</p>
            </div>
            <div class="flex space-x-3">
                <a href="{% url 'appointments:list' %}" class="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
                    <i class="fas fa-arrow-left mr-2"></i>
                    Back to Appointments
                </a>
            </div>
        </div>
    </div>

    <!-- Appointment Form -->
    <div class="bg-white shadow rounded-lg p-6">
        <form method="post" class="space-y-6">
            {% csrf_token %}
            
            <!-- Patient and Doctor Selection -->
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">
                        Patient <span class="text-red-500">*</span>
                    </label>
                    <select name="patient" required class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500 bg-white">
                        <option value="">Select Patient</option>
                        {% for patient in patients %}
                        <option value="{{ patient.id }}">{{ patient.full_name }} ({{ patient.patient_id }})</option>
                        {% endfor %}
                    </select>
                </div>
                
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">
                        Doctor <span class="text-red-500">*</span>
                    </label>
                    <select name="doctor" required class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500 bg-white">
                        <option value="">Select Doctor</option>
                        {% for doctor in doctors %}
                        <option value="{{ doctor.id }}">Dr. {{ doctor.user.get_full_name }} - {{ doctor.specialization }}</option>
                        {% endfor %}
                    </select>
                </div>
            </div>

            <!-- Date and Time -->
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">
                        Appointment Date <span class="text-red-500">*</span>
                    </label>
                    <input type="date" name="appointment_date" required class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500">
                </div>
                
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">
                        Appointment Time <span class="text-red-500">*</span>
                    </label>
                    <select name="appointment_time" required class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500 bg-white">
                        <option value="">Select Time</option>
                        <!-- Morning Slots -->
                        <optgroup label="Morning (9:00 AM - 12:00 PM)">
                            <option value="09:00">9:00 AM</option>
                            <option value="09:30">9:30 AM</option>
                            <option value="10:00">10:00 AM</option>
                            <option value="10:30">10:30 AM</option>
                            <option value="11:00">11:00 AM</option>
                            <option value="11:30">11:30 AM</option>
                        </optgroup>
                        <!-- Afternoon Slots -->
                        <optgroup label="Afternoon (1:00 PM - 5:00 PM)">
                            <option value="13:00">1:00 PM</option>
                            <option value="13:30">1:30 PM</option>
                            <option value="14:00">2:00 PM</option>
                            <option value="14:30">2:30 PM</option>
                            <option value="15:00">3:00 PM</option>
                            <option value="15:30">3:30 PM</option>
                            <option value="16:00">4:00 PM</option>
                            <option value="16:30">4:30 PM</option>
                        </optgroup>
                        <!-- Evening Slots -->
                        <optgroup label="Evening (6:00 PM - 9:00 PM)">
                            <option value="18:00">6:00 PM</option>
                            <option value="18:30">6:30 PM</option>
                            <option value="19:00">7:00 PM</option>
                            <option value="19:30">7:30 PM</option>
                            <option value="20:00">8:00 PM</option>
                            <option value="20:30">8:30 PM</option>
                        </optgroup>
                    </select>
                </div>
            </div>

            <!-- Appointment Type -->
            <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">
                    Appointment Type
                </label>
                <select name="appointment_type" class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500 bg-white">
                    {% for value, label in appointment_types %}
                    <option value="{{ value }}">{{ label }}</option>
                    {% endfor %}
                </select>
            </div>

            <!-- Reason for Visit -->
            <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">
                    Reason for Visit
                </label>
                <textarea name="reason" rows="3" class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500 resize-vertical" placeholder="Enter the reason for this appointment..."></textarea>
            </div>

            <!-- Additional Notes -->
            <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">
                    Additional Notes
                </label>
                <textarea name="notes" rows="2" class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500 resize-vertical" placeholder="Any additional notes or special instructions..."></textarea>
            </div>

            <!-- Form Actions -->
            <div class="flex justify-end space-x-3 pt-6 border-t border-gray-200">
                <a href="{% url 'appointments:list' %}" class="px-6 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
                    Cancel
                </a>
                <button type="submit" class="px-6 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700">
                    <i class="fas fa-calendar-plus mr-2"></i>
                    Schedule Appointment
                </button>
            </div>
        </form>
    </div>

    <!-- Quick Info -->
    <div class="bg-blue-50 border border-blue-200 rounded-lg p-4">
        <div class="flex">
            <div class="flex-shrink-0">
                <i class="fas fa-info-circle text-blue-400"></i>
            </div>
            <div class="ml-3">
                <h3 class="text-sm font-medium text-blue-800">Appointment Scheduling Guidelines</h3>
                <div class="mt-2 text-sm text-blue-700">
                    <ul class="list-disc list-inside space-y-1">
                        <li>Appointments are scheduled in 30-minute slots</li>
                        <li>Morning slots: 9:00 AM - 12:00 PM</li>
                        <li>Afternoon slots: 1:00 PM - 5:00 PM</li>
                        <li>Evening slots: 6:00 PM - 9:00 PM</li>
                        <li>Emergency appointments can be scheduled outside regular hours</li>
                        <li>The system will check for conflicts automatically</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// Set minimum date to today
document.addEventListener('DOMContentLoaded', function() {
    const dateInput = document.querySelector('input[name="appointment_date"]');
    const today = new Date().toISOString().split('T')[0];
    dateInput.min = today;
    
    // Set default date to today
    if (!dateInput.value) {
        dateInput.value = today;
    }
});
</script>
{% endblock %}
