{% extends 'base.html' %}
{% load static %}

{% block title %}Patient Flow Analytics{% endblock %}

{% block extra_css %}
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<style>
    .metric-card {
        background: white;
        border-radius: 12px;
        padding: 1.5rem;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        border-left: 4px solid #3b82f6;
    }
    .metric-value {
        font-size: 2rem;
        font-weight: bold;
        color: #1f2937;
    }
    .metric-label {
        color: #6b7280;
        font-size: 0.9rem;
    }
    .chart-container {
        background: white;
        border-radius: 12px;
        padding: 1.5rem;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        margin-bottom: 2rem;
    }
    .nav-analytics {
        background: #1f2937;
        padding: 1rem;
        border-radius: 8px;
        margin-bottom: 2rem;
    }
    .nav-analytics a {
        color: #d1d5db;
        text-decoration: none;
        padding: 0.5rem 1rem;
        border-radius: 6px;
        margin-right: 0.5rem;
        transition: all 0.3s;
    }
    .nav-analytics a:hover {
        background: #374151;
        color: white;
    }
    .nav-analytics a.active {
        background: #3b82f6;
        color: white;
    }
</style>
{% endblock %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <!-- Header -->
    <div class="mb-8">
        <h1 class="text-4xl font-bold text-gray-900 mb-2">🚶 Patient Flow Analytics</h1>
        <p class="text-gray-600">Track admission patterns, discharge rates, and bed occupancy trends</p>
    </div>

    <!-- Analytics Navigation -->
    <div class="nav-analytics">
        <a href="{% url 'analytics:dashboard' %}">📊 Overview</a>
        <a href="{% url 'analytics:patient_flow' %}" class="active">🚶 Patient Flow</a>
        <a href="{% url 'analytics:financial' %}">💰 Financial</a>
        <a href="{% url 'analytics:bed_utilization' %}">🛏️ Bed Utilization</a>
        <a href="{% url 'analytics:staff_efficiency' %}">👥 Staff Efficiency</a>
        <a href="{% url 'analytics:quality' %}">⭐ Quality Metrics</a>
    </div>

    <!-- Filters -->
    <div class="bg-white p-4 rounded-lg shadow mb-6">
        <form method="get" class="flex items-center space-x-4">
            <div>
                <label class="block text-sm font-medium text-gray-700">Start Date</label>
                <input type="date" name="start_date" value="{{ start_date|date:'Y-m-d' }}" 
                       class="mt-1 block w-full rounded-md border-gray-300 shadow-sm">
            </div>
            <div>
                <label class="block text-sm font-medium text-gray-700">End Date</label>
                <input type="date" name="end_date" value="{{ end_date|date:'Y-m-d' }}" 
                       class="mt-1 block w-full rounded-md border-gray-300 shadow-sm">
            </div>
            <div>
                <label class="block text-sm font-medium text-gray-700">Specialty</label>
                <select name="specialty" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm">
                    <option value="">All Specialties</option>
                    {% for spec in specialties %}
                        <option value="{{ spec.id }}" {% if specialty and specialty.id == spec.id %}selected{% endif %}>
                            {{ spec.name }}
                        </option>
                    {% endfor %}
                </select>
            </div>
            <div class="pt-6">
                <button type="submit" class="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700">
                    Apply Filters
                </button>
            </div>
        </form>
    </div>

    <!-- Summary Metrics -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        <div class="metric-card">
            <div class="metric-value">{{ total_admissions }}</div>
            <div class="metric-label">Total Admissions</div>
        </div>
        <div class="metric-card" style="border-left-color: #10b981;">
            <div class="metric-value">{{ total_discharges }}</div>
            <div class="metric-label">Total Discharges</div>
        </div>
        <div class="metric-card" style="border-left-color: #f59e0b;">
            <div class="metric-value">{{ avg_occupancy }}%</div>
            <div class="metric-label">Average Occupancy</div>
        </div>
        <div class="metric-card" style="border-left-color: #8b5cf6;">
            <div class="metric-value">{{ avg_los }}</div>
            <div class="metric-label">Avg Length of Stay (days)</div>
        </div>
    </div>

    <!-- Charts -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
        <!-- Admissions vs Discharges -->
        <div class="chart-container">
            <h3 class="text-xl font-semibold mb-4">📈 Admissions vs Discharges</h3>
            <canvas id="admissionDischargeChart" height="300"></canvas>
        </div>

        <!-- Admission Types -->
        <div class="chart-container">
            <h3 class="text-xl font-semibold mb-4">🚨 Emergency vs Planned Admissions</h3>
            <canvas id="admissionTypesChart" height="300"></canvas>
        </div>
    </div>

    <!-- Occupancy Trend -->
    <div class="chart-container">
        <h3 class="text-xl font-semibold mb-4">🛏️ Bed Occupancy Rate Trend</h3>
        <canvas id="occupancyChart" height="200"></canvas>
    </div>

    <!-- Data Table -->
    <div class="bg-white rounded-lg shadow overflow-hidden">
        <div class="px-6 py-4 border-b border-gray-200">
            <h3 class="text-lg font-semibold">📋 Daily Patient Flow Data</h3>
        </div>
        <div class="overflow-x-auto">
            <table class="min-w-full divide-y divide-gray-200">
                <thead class="bg-gray-50">
                    <tr>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Date</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Admissions</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Emergency</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Planned</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Discharges</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Occupancy %</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Avg LOS</th>
                    </tr>
                </thead>
                <tbody class="bg-white divide-y divide-gray-200">
                    {% for data in flow_data %}
                    <tr>
                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                            {{ data.date|date:"M d, Y" }}
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{{ data.total_admissions }}</td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-red-600">{{ data.emergency_admissions }}</td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-green-600">{{ data.planned_admissions }}</td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{{ data.total_discharges }}</td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{{ data.occupancy_rate|floatformat:1 }}%</td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{{ data.average_length_of_stay|floatformat:1 }}</td>
                    </tr>
                    {% empty %}
                    <tr>
                        <td colspan="7" class="px-6 py-4 text-center text-gray-500">No data available for the selected period</td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
    </div>
</div>

<script>
// Chart data from Django
const chartData = {{ chart_data|safe }};

// Admissions vs Discharges Chart
const admissionDischargeCtx = document.getElementById('admissionDischargeChart').getContext('2d');
new Chart(admissionDischargeCtx, {
    type: 'line',
    data: {
        labels: chartData.dates,
        datasets: [
            {
                label: 'Admissions',
                data: chartData.admissions,
                borderColor: '#3b82f6',
                backgroundColor: 'rgba(59, 130, 246, 0.1)',
                tension: 0.4,
                fill: true
            },
            {
                label: 'Discharges',
                data: chartData.discharges,
                borderColor: '#10b981',
                backgroundColor: 'rgba(16, 185, 129, 0.1)',
                tension: 0.4,
                fill: true
            }
        ]
    },
    options: {
        responsive: true,
        maintainAspectRatio: false,
        scales: {
            y: {
                beginAtZero: true,
                title: {
                    display: true,
                    text: 'Count'
                }
            }
        },
        plugins: {
            legend: {
                display: true,
                position: 'top'
            }
        }
    }
});

// Admission Types Chart
const admissionTypesCtx = document.getElementById('admissionTypesChart').getContext('2d');
new Chart(admissionTypesCtx, {
    type: 'bar',
    data: {
        labels: chartData.dates,
        datasets: [
            {
                label: 'Emergency',
                data: chartData.emergency_admissions,
                backgroundColor: 'rgba(239, 68, 68, 0.8)',
                borderColor: '#ef4444',
                borderWidth: 1
            },
            {
                label: 'Planned',
                data: chartData.planned_admissions,
                backgroundColor: 'rgba(34, 197, 94, 0.8)',
                borderColor: '#22c55e',
                borderWidth: 1
            }
        ]
    },
    options: {
        responsive: true,
        maintainAspectRatio: false,
        scales: {
            x: {
                stacked: true
            },
            y: {
                stacked: true,
                beginAtZero: true,
                title: {
                    display: true,
                    text: 'Admissions'
                }
            }
        },
        plugins: {
            legend: {
                display: true,
                position: 'top'
            }
        }
    }
});

// Occupancy Chart
const occupancyCtx = document.getElementById('occupancyChart').getContext('2d');
new Chart(occupancyCtx, {
    type: 'line',
    data: {
        labels: chartData.dates,
        datasets: [
            {
                label: 'Occupancy Rate (%)',
                data: chartData.occupancy_rate,
                borderColor: '#f59e0b',
                backgroundColor: 'rgba(245, 158, 11, 0.1)',
                tension: 0.4,
                fill: true
            }
        ]
    },
    options: {
        responsive: true,
        maintainAspectRatio: false,
        scales: {
            y: {
                beginAtZero: true,
                max: 100,
                title: {
                    display: true,
                    text: 'Occupancy Rate (%)'
                }
            }
        },
        plugins: {
            legend: {
                display: false
            }
        }
    }
});
</script>
{% endblock %}
