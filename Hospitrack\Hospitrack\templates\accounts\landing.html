<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>HospiTrack - Hospital Management System</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: '#3B82F6',
                        secondary: '#1E40AF',
                        accent: '#10B981',
                    }
                }
            }
        }
    </script>
</head>
<body class="bg-gradient-to-br from-blue-50 to-indigo-100 min-h-screen">
    <!-- Header -->
    <header class="bg-white shadow-sm">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4">
            <div class="flex justify-between items-center">
                <div class="flex items-center">
                    <i class="fas fa-hospital text-3xl text-primary mr-3"></i>
                    <h1 class="text-3xl font-bold text-gray-900">HospiTrack</h1>
                </div>
                <div class="text-sm text-gray-600">
                    Hospital Management System
                </div>
            </div>
        </div>
    </header>

    <!-- Main Content -->
    <main class="flex-1 flex items-center justify-center px-4 sm:px-6 lg:px-8">
        <div class="max-w-6xl w-full">
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
                <!-- Left Side - Welcome Content -->
                <div class="text-center lg:text-left">
                    <h2 class="text-4xl font-bold text-gray-900 mb-6">
                        Welcome to <span class="text-primary">HospiTrack</span>
                    </h2>
                    <p class="text-xl text-gray-600 mb-8">
                        A comprehensive hospital management system designed to streamline healthcare operations, 
                        manage patient records, appointments, and staff efficiently.
                    </p>
                    
                    <div class="grid grid-cols-1 sm:grid-cols-3 gap-6 mb-8">
                        <div class="text-center p-4 bg-white rounded-lg shadow-sm">
                            <i class="fas fa-user-md text-3xl text-primary mb-2"></i>
                            <h3 class="font-semibold text-gray-900">For Doctors</h3>
                            <p class="text-sm text-gray-600">Manage appointments and patient care</p>
                        </div>
                        <div class="text-center p-4 bg-white rounded-lg shadow-sm">
                            <i class="fas fa-user-nurse text-3xl text-accent mb-2"></i>
                            <h3 class="font-semibold text-gray-900">For Nurses</h3>
                            <p class="text-sm text-gray-600">Update patient status and bed management</p>
                        </div>
                        <div class="text-center p-4 bg-white rounded-lg shadow-sm">
                            <i class="fas fa-user-cog text-3xl text-secondary mb-2"></i>
                            <h3 class="font-semibold text-gray-900">For Admins</h3>
                            <p class="text-sm text-gray-600">Complete hospital management control</p>
                        </div>
                    </div>
                </div>

                <!-- Right Side - Login Form -->
                <div class="bg-white rounded-lg shadow-xl p-8 lg:p-10 mx-4 sm:mx-6 lg:mx-0 my-6 lg:my-8">
                    <div class="text-center mb-8 lg:mb-10">
                        <h3 class="text-2xl lg:text-3xl font-bold text-gray-900 mb-3">Sign In</h3>
                        <p class="text-gray-600 text-base">Access your dashboard</p>
                    </div>

                    <form method="post" action="{% url 'login' %}" class="space-y-6">
                        {% csrf_token %}
                        <div>
                            <label for="username" class="block text-sm font-medium text-gray-700 mb-2">
                                Username
                            </label>
                            <div class="relative">
                                <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                    <i class="fas fa-user text-gray-400"></i>
                                </div>
                                <input type="text" id="username" name="username" required
                                       value=""
                                       autocomplete="off"
                                       class="block w-full pl-10 pr-3 py-3 border border-gray-300 rounded-md leading-5 bg-white placeholder-gray-500 focus:outline-none focus:placeholder-gray-400 focus:ring-1 focus:ring-primary focus:border-primary"
                                       placeholder="Enter your username">
                            </div>
                        </div>

                        <div>
                            <label for="password" class="block text-sm font-medium text-gray-700 mb-2">
                                Password
                            </label>
                            <div class="relative">
                                <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                    <i class="fas fa-lock text-gray-400"></i>
                                </div>
                                <input type="password" id="password" name="password" required
                                       value=""
                                       autocomplete="off"
                                       class="block w-full pl-10 pr-3 py-3 border border-gray-300 rounded-md leading-5 bg-white placeholder-gray-500 focus:outline-none focus:placeholder-gray-400 focus:ring-1 focus:ring-primary focus:border-primary"
                                       placeholder="Enter your password">
                            </div>
                        </div>

                        <div class="pt-4">
                            <button type="submit"
                                    class="w-full flex justify-center py-3 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary hover:bg-secondary focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary transition duration-150 ease-in-out">
                                <i class="fas fa-sign-in-alt mr-2"></i>
                                Sign In
                            </button>
                        </div>
                    </form>

                </div>
            </div>
        </div>
    </main>



    <!-- Footer -->
    <footer class="bg-gray-900 text-white py-12">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="grid grid-cols-1 md:grid-cols-4 gap-8">
                <div class="col-span-1 md:col-span-2">
                    <div class="flex items-center mb-4">
                        <i class="fas fa-hospital text-primary text-2xl mr-3"></i>
                        <h3 class="text-2xl font-bold">HospiTrack</h3>
                    </div>
                    <p class="text-gray-300 mb-4">
                        Advanced hospital management system designed to streamline healthcare operations
                        and improve patient care quality.
                    </p>
                </div>

                <div>
                    <h4 class="text-lg font-semibold mb-4">Features</h4>
                    <ul class="space-y-2 text-gray-300">
                        <li>Patient Management</li>
                        <li>Appointment Scheduling</li>
                        <li>Bed Management</li>
                        <li>Staff Management</li>
                        <li>Medical Records</li>
                    </ul>
                </div>

                <div>
                    <h4 class="text-lg font-semibold mb-4">Support</h4>
                    <ul class="space-y-2 text-gray-300">
                        <li>Documentation</li>
                        <li>Help Center</li>
                        <li>Contact Support</li>
                        <li>Training</li>
                        <li>System Status</li>
                    </ul>
                </div>
            </div>

            <div class="border-t border-gray-700 mt-8 pt-8 text-center">
                <p class="text-gray-400">
                    © 2025 HospiTrack. All rights reserved. | Hospital Management System
                </p>
            </div>
        </div>
    </footer>

<script>
// Clear login form on page load to ensure empty fields after logout
document.addEventListener('DOMContentLoaded', function() {
    // Clear username and password fields
    const usernameField = document.getElementById('username');
    const passwordField = document.getElementById('password');

    if (usernameField) {
        usernameField.value = '';
    }
    if (passwordField) {
        passwordField.value = '';
    }

    // Prevent browser auto-fill with a slight delay
    setTimeout(function() {
        if (usernameField) usernameField.value = '';
        if (passwordField) passwordField.value = '';
    }, 100);
});

// Clear form when user navigates back to this page
window.addEventListener('pageshow', function(event) {
    const usernameField = document.getElementById('username');
    const passwordField = document.getElementById('password');

    if (usernameField) usernameField.value = '';
    if (passwordField) passwordField.value = '';
});
</script>
</body>
</html>
