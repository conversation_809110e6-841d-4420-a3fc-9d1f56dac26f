{% extends 'base.html' %}

{% block title %}{{ staff_member.user.get_full_name }} - Work Schedule{% endblock %}

{% block content %}
<div class="space-y-6">
    <!-- Header -->
    <div class="bg-white shadow rounded-lg p-6">
        <div class="flex justify-between items-start">
            <div>
                <h1 class="text-3xl font-bold text-gray-900">Work Schedule</h1>
                <p class="text-gray-600 mt-2">{{ staff_member.user.get_full_name }} - {{ staff_member.employee_id }}</p>
                <div class="mt-2 flex items-center space-x-4 text-sm text-gray-500">
                    <span>{{ staff_member.department.name }}</span>
                    {% if staff_member.user.nurse %}
                    <span>{{ staff_member.user.nurse.get_shift_display }} Shift</span>
                    {% endif %}
                </div>
            </div>
            <div class="flex space-x-3">
                <a href="{% url 'staff:detail' staff_member.id %}" class="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
                    <i class="fas fa-arrow-left mr-2"></i>
                    Back to Details
                </a>
                <a href="{% url 'staff:list' %}" class="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
                    <i class="fas fa-list mr-2"></i>
                    Back to Nurses
                </a>
            </div>
        </div>
    </div>

    <!-- Schedule Overview -->
    <div class="bg-white shadow rounded-lg p-6">
        <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-6">
            <div class="text-center">
                <div class="text-2xl font-bold text-blue-600">
                    {% if staff_member.user.nurse %}{{ staff_member.user.nurse.get_shift_display }}{% else %}N/A{% endif %}
                </div>
                <div class="text-sm text-gray-500">Primary Shift</div>
            </div>
            <div class="text-center">
                <div class="text-2xl font-bold text-green-600">5</div>
                <div class="text-sm text-gray-500">Days/Week</div>
            </div>
            <div class="text-center">
                <div class="text-2xl font-bold text-purple-600">40</div>
                <div class="text-sm text-gray-500">Hours/Week</div>
            </div>
            <div class="text-center">
                <div class="text-2xl font-bold text-orange-600">{{ staff_member.department.name }}</div>
                <div class="text-sm text-gray-500">Department</div>
            </div>
        </div>
    </div>

    <!-- Weekly Schedule -->
    {% for week in weeks %}
    <div class="bg-white shadow rounded-lg p-6">
        <div class="flex justify-between items-center mb-4">
            <h2 class="text-lg font-medium text-gray-900">
                Week of {{ week.week_start|date:"F d" }} - {{ week.week_end|date:"F d, Y" }}
            </h2>
            {% if forloop.first %}
            <span class="px-2 py-1 text-xs font-semibold rounded-full bg-blue-100 text-blue-800">Current Week</span>
            {% endif %}
        </div>
        
        <div class="grid grid-cols-1 md:grid-cols-7 gap-4">
            {% for day in week.days %}
            <div class="border rounded-lg p-4 {% if day.is_today %}bg-blue-50 border-blue-200{% elif day.is_working %}bg-white{% else %}bg-gray-50{% endif %}">
                <div class="text-center">
                    <div class="text-sm font-medium text-gray-900 mb-1">
                        {{ day.day_name }}
                    </div>
                    <div class="text-lg font-bold {% if day.is_today %}text-blue-600{% else %}text-gray-700{% endif %} mb-2">
                        {{ day.date|date:"d" }}
                    </div>
                    
                    {% if day.is_working %}
                    <div class="text-xs text-center">
                        <div class="px-2 py-1 bg-green-100 text-green-800 rounded-full mb-2">
                            <i class="fas fa-clock mr-1"></i>
                            Working
                        </div>
                        <div class="text-gray-600 text-xs">
                            {{ day.shift_time }}
                        </div>
                    </div>
                    {% else %}
                    <div class="text-xs text-center">
                        <div class="px-2 py-1 bg-gray-100 text-gray-600 rounded-full">
                            <i class="fas fa-calendar-times mr-1"></i>
                            {{ day.shift_time }}
                        </div>
                    </div>
                    {% endif %}
                    
                    {% if day.is_today %}
                    <div class="mt-2">
                        <span class="px-1 py-0.5 text-xs font-semibold rounded bg-blue-100 text-blue-800">Today</span>
                    </div>
                    {% endif %}
                </div>
            </div>
            {% endfor %}
        </div>
    </div>
    {% endfor %}

    <!-- Schedule Legend -->
    <div class="bg-white shadow rounded-lg p-6">
        <h2 class="text-lg font-medium text-gray-900 mb-4">Schedule Legend</h2>
        <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div class="flex items-center">
                <div class="w-4 h-4 bg-green-100 border border-green-200 rounded mr-3"></div>
                <span class="text-sm text-gray-700">Working Day</span>
            </div>
            <div class="flex items-center">
                <div class="w-4 h-4 bg-gray-100 border border-gray-200 rounded mr-3"></div>
                <span class="text-sm text-gray-700">Day Off</span>
            </div>
            <div class="flex items-center">
                <div class="w-4 h-4 bg-blue-100 border border-blue-200 rounded mr-3"></div>
                <span class="text-sm text-gray-700">Today</span>
            </div>
        </div>
    </div>

    <!-- Shift Information -->
    {% if staff_member.user.nurse %}
    <div class="bg-white shadow rounded-lg p-6">
        <h2 class="text-lg font-medium text-gray-900 mb-4">Shift Information</h2>
        <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div class="text-center p-4 bg-yellow-50 rounded-lg">
                <i class="fas fa-sun text-2xl text-yellow-600 mb-2"></i>
                <h3 class="font-medium text-gray-900">Morning Shift</h3>
                <p class="text-sm text-gray-600">6:00 AM - 2:00 PM</p>
                {% if staff_member.user.nurse.shift == 'morning' %}
                <span class="mt-2 inline-block px-2 py-1 text-xs font-semibold rounded-full bg-yellow-100 text-yellow-800">Current</span>
                {% endif %}
            </div>
            
            <div class="text-center p-4 bg-orange-50 rounded-lg">
                <i class="fas fa-sun text-2xl text-orange-600 mb-2"></i>
                <h3 class="font-medium text-gray-900">Evening Shift</h3>
                <p class="text-sm text-gray-600">2:00 PM - 10:00 PM</p>
                {% if staff_member.user.nurse.shift == 'evening' %}
                <span class="mt-2 inline-block px-2 py-1 text-xs font-semibold rounded-full bg-orange-100 text-orange-800">Current</span>
                {% endif %}
            </div>
            
            <div class="text-center p-4 bg-blue-50 rounded-lg">
                <i class="fas fa-moon text-2xl text-blue-600 mb-2"></i>
                <h3 class="font-medium text-gray-900">Night Shift</h3>
                <p class="text-sm text-gray-600">10:00 PM - 6:00 AM</p>
                {% if staff_member.user.nurse.shift == 'night' %}
                <span class="mt-2 inline-block px-2 py-1 text-xs font-semibold rounded-full bg-blue-100 text-blue-800">Current</span>
                {% endif %}
            </div>
        </div>
    </div>
    {% endif %}

    <!-- Quick Actions -->
    <div class="bg-white shadow rounded-lg p-6">
        <h2 class="text-lg font-medium text-gray-900 mb-4">Schedule Actions</h2>
        <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
            {% if user.user_type == 'admin' %}
            <button class="inline-flex items-center justify-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700">
                <i class="fas fa-edit mr-2"></i>
                Edit Schedule
            </button>
            
            <button class="inline-flex items-center justify-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-green-600 hover:bg-green-700">
                <i class="fas fa-plus mr-2"></i>
                Add Overtime
            </button>
            
            <button class="inline-flex items-center justify-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-purple-600 hover:bg-purple-700">
                <i class="fas fa-calendar-alt mr-2"></i>
                Request Time Off
            </button>
            
            <button class="inline-flex items-center justify-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-orange-600 hover:bg-orange-700">
                <i class="fas fa-print mr-2"></i>
                Print Schedule
            </button>
            {% endif %}
        </div>
    </div>
</div>
{% endblock %}
