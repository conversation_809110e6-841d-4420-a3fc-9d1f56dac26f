# Generated by Django 5.2.4 on 2025-07-27 04:23

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('accounts', '0001_initial'),
    ]

    operations = [
        migrations.CreateModel(
            name='MedicalSpecialty',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100, unique=True)),
                ('description', models.TextField(blank=True, null=True)),
                ('is_active', models.BooleanField(default=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
            ],
            options={
                'verbose_name_plural': 'Medical Specialties',
                'ordering': ['name'],
            },
        ),
        migrations.AlterField(
            model_name='doctor',
            name='available_from',
            field=models.TimeField(blank=True, null=True),
        ),
        migrations.Alter<PERSON><PERSON>(
            model_name='doctor',
            name='available_to',
            field=models.TimeField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='doctor',
            name='specialty',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='doctors', to='accounts.medicalspecialty'),
        ),
        migrations.AddField(
            model_name='nurse',
            name='specialty',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='nurses', to='accounts.medicalspecialty'),
        ),
    ]
