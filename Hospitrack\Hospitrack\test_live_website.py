#!/usr/bin/env python
"""
Live website testing - simulates real browser requests
"""
import os
import sys
import django
import requests
import time
from urllib.parse import urljoin

# Setup Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'hospitrack.settings')
django.setup()

from django.test import Client
from accounts.models import User

BASE_URL = 'http://127.0.0.1:8000'

def test_server_connectivity():
    """Test if server is running and accessible"""
    print("🌐 Testing Server Connectivity")
    print("=" * 50)
    
    try:
        response = requests.get(BASE_URL, timeout=10)
        if response.status_code == 200:
            print(f"   ✅ Server is running and accessible")
            print(f"   📊 Response time: {response.elapsed.total_seconds():.2f}s")
            return True
        else:
            print(f"   ❌ Server returned status: {response.status_code}")
            return False
    except requests.exceptions.ConnectionError:
        print(f"   ❌ Cannot connect to server at {BASE_URL}")
        print(f"   💡 Make sure Django server is running: python manage.py runserver")
        return False
    except Exception as e:
        print(f"   ❌ Connection error: {str(e)}")
        return False

def test_page_loads():
    """Test all major pages load without errors"""
    print(f"\n📄 Testing Page Loads")
    print("=" * 50)
    
    client = Client()
    results = {'passed': 0, 'failed': 0, 'errors': []}
    
    # Test public pages
    public_pages = [
        ('/', 'Landing Page'),
        ('/login/', 'Login Page'),
    ]
    
    for url, name in public_pages:
        try:
            response = client.get(url)
            if response.status_code == 200:
                print(f"   ✅ {name} loads successfully")
                results['passed'] += 1
                
                # Check for common errors in content
                content = response.content.decode()
                if 'Error' in content or 'Exception' in content or 'Traceback' in content:
                    print(f"   ⚠️  {name} contains error messages")
                    results['errors'].append(f"{name} contains errors")
            else:
                print(f"   ❌ {name} failed: {response.status_code}")
                results['failed'] += 1
                results['errors'].append(f"{name} failed: {response.status_code}")
        except Exception as e:
            print(f"   ❌ {name} exception: {str(e)}")
            results['failed'] += 1
            results['errors'].append(f"{name} exception: {str(e)}")
    
    # Test authenticated pages
    print(f"\n   🔐 Testing Authenticated Pages")
    
    # Admin pages
    client.login(username='admin', password='admin123')
    admin_pages = [
        ('/admin-dashboard/', 'Admin Dashboard'),
        ('/doctors/', 'Doctors List'),
        ('/doctors/create/', 'Doctor Creation'),
        ('/nurses/', 'Nurses List'),
        ('/nurses/create/', 'Nurse Creation'),
        ('/nurses/leave/', 'Nurse Leave Management'),
        ('/patients/', 'Patients List'),
        ('/patients/register/', 'Patient Registration'),
        ('/appointments/', 'Appointments'),
        ('/beds/', 'Bed Management'),
        ('/staff/', 'Staff Management'),
    ]
    
    for url, name in admin_pages:
        try:
            response = client.get(url)
            if response.status_code == 200:
                print(f"   ✅ {name} loads successfully")
                results['passed'] += 1
                
                # Check for template errors
                content = response.content.decode()
                if 'TemplateDoesNotExist' in content or 'TemplateSyntaxError' in content:
                    print(f"   ❌ {name} has template errors")
                    results['failed'] += 1
                    results['errors'].append(f"{name} template errors")
            else:
                print(f"   ❌ {name} failed: {response.status_code}")
                results['failed'] += 1
                results['errors'].append(f"{name} failed: {response.status_code}")
        except Exception as e:
            print(f"   ❌ {name} exception: {str(e)}")
            results['failed'] += 1
            results['errors'].append(f"{name} exception: {str(e)}")
    
    client.logout()
    
    # Doctor pages
    client.login(username='dr.emergency', password='doctor123')
    doctor_pages = [
        ('/doctor-dashboard/', 'Doctor Dashboard'),
        ('/specialty-team/', 'Specialty Team'),
    ]
    
    for url, name in doctor_pages:
        try:
            response = client.get(url)
            if response.status_code == 200:
                print(f"   ✅ {name} loads successfully")
                results['passed'] += 1
            else:
                print(f"   ❌ {name} failed: {response.status_code}")
                results['failed'] += 1
                results['errors'].append(f"{name} failed: {response.status_code}")
        except Exception as e:
            print(f"   ❌ {name} exception: {str(e)}")
            results['failed'] += 1
            results['errors'].append(f"{name} exception: {str(e)}")
    
    client.logout()
    
    # Nurse pages
    client.login(username='emma.johnson1', password='nurse123')
    nurse_pages = [
        ('/nurse-dashboard/', 'Nurse Dashboard'),
    ]
    
    for url, name in nurse_pages:
        try:
            response = client.get(url)
            if response.status_code == 200:
                print(f"   ✅ {name} loads successfully")
                results['passed'] += 1
            else:
                print(f"   ❌ {name} failed: {response.status_code}")
                results['failed'] += 1
                results['errors'].append(f"{name} failed: {response.status_code}")
        except Exception as e:
            print(f"   ❌ {name} exception: {str(e)}")
            results['failed'] += 1
            results['errors'].append(f"{name} exception: {str(e)}")
    
    return results

def test_form_submissions():
    """Test form submissions work correctly"""
    print(f"\n📝 Testing Form Submissions")
    print("=" * 50)
    
    client = Client()
    results = {'passed': 0, 'failed': 0, 'errors': []}
    
    # Test login form
    print(f"   🔐 Testing Login Form")
    try:
        # Test valid login
        response = client.post('/login/', {
            'username': 'admin',
            'password': 'admin123'
        })
        if response.status_code == 302:  # Redirect on success
            print(f"   ✅ Valid login works")
            results['passed'] += 1
        else:
            print(f"   ❌ Valid login failed: {response.status_code}")
            results['failed'] += 1
            results['errors'].append("Valid login failed")
        
        client.logout()
        
        # Test invalid login
        response = client.post('/login/', {
            'username': 'invalid',
            'password': 'invalid'
        })
        if response.status_code == 200:  # Stay on page with error
            content = response.content.decode()
            if 'Invalid' in content or 'error' in content.lower():
                print(f"   ✅ Invalid login properly rejected")
                results['passed'] += 1
            else:
                print(f"   ❌ Invalid login error message missing")
                results['failed'] += 1
                results['errors'].append("Invalid login error message missing")
        else:
            print(f"   ❌ Invalid login handling failed: {response.status_code}")
            results['failed'] += 1
            results['errors'].append("Invalid login handling failed")
            
    except Exception as e:
        print(f"   ❌ Login form test exception: {str(e)}")
        results['failed'] += 1
        results['errors'].append(f"Login form exception: {str(e)}")
    
    # Test patient registration form
    print(f"   🏥 Testing Patient Registration Form")
    try:
        client.login(username='admin', password='admin123')
        
        # Test valid patient registration
        response = client.post('/patients/register/', {
            'patient_id': f'TEST{int(time.time())}',  # Unique ID
            'first_name': 'Test',
            'last_name': 'Patient',
            'date_of_birth': '1990-01-01',
            'gender': 'M',
            'blood_group': 'O+',
            'phone': '**********',
            'email': '<EMAIL>',
            'address': 'Test Address',
            'emergency_contact_name': 'Emergency Contact',
            'emergency_contact_phone': '**********',
            'emergency_contact_relation': 'Father'
        })
        
        if response.status_code in [200, 302]:
            print(f"   ✅ Patient registration form works")
            results['passed'] += 1
        else:
            print(f"   ❌ Patient registration failed: {response.status_code}")
            results['failed'] += 1
            results['errors'].append(f"Patient registration failed: {response.status_code}")
            
    except Exception as e:
        print(f"   ❌ Patient registration exception: {str(e)}")
        results['failed'] += 1
        results['errors'].append(f"Patient registration exception: {str(e)}")
    
    return results

def test_static_files():
    """Test static files are loading"""
    print(f"\n🎨 Testing Static Files")
    print("=" * 50)
    
    client = Client()
    results = {'passed': 0, 'failed': 0, 'errors': []}
    
    try:
        # Get a page and check for CSS/JS references
        response = client.get('/')
        if response.status_code == 200:
            content = response.content.decode()
            
            # Check for CSS
            if 'css' in content.lower() or 'stylesheet' in content.lower():
                print(f"   ✅ CSS references found")
                results['passed'] += 1
            else:
                print(f"   ⚠️  No CSS references found")
                results['errors'].append("No CSS references")
            
            # Check for responsive design classes
            if 'md:' in content or 'lg:' in content or 'grid' in content:
                print(f"   ✅ Responsive design classes found")
                results['passed'] += 1
            else:
                print(f"   ❌ No responsive design classes found")
                results['failed'] += 1
                results['errors'].append("No responsive design classes")
                
        else:
            print(f"   ❌ Cannot load page to check static files")
            results['failed'] += 1
            results['errors'].append("Cannot load page for static file check")
            
    except Exception as e:
        print(f"   ❌ Static files test exception: {str(e)}")
        results['failed'] += 1
        results['errors'].append(f"Static files exception: {str(e)}")
    
    return results

def test_database_operations():
    """Test database operations work correctly"""
    print(f"\n🗄️ Testing Database Operations")
    print("=" * 50)
    
    results = {'passed': 0, 'failed': 0, 'errors': []}
    
    try:
        # Test user queries
        total_users = User.objects.count()
        if total_users > 0:
            print(f"   ✅ Database contains {total_users} users")
            results['passed'] += 1
        else:
            print(f"   ❌ No users found in database")
            results['failed'] += 1
            results['errors'].append("No users in database")
        
        # Test user authentication
        admin_user = User.objects.filter(username='admin').first()
        if admin_user and admin_user.check_password('admin123'):
            print(f"   ✅ Password authentication works")
            results['passed'] += 1
        else:
            print(f"   ❌ Password authentication failed")
            results['failed'] += 1
            results['errors'].append("Password authentication failed")
        
        # Test relationships
        from accounts.models import Doctor, Nurse
        doctors_with_specialty = Doctor.objects.filter(specialty__isnull=False).count()
        total_doctors = Doctor.objects.count()
        
        if doctors_with_specialty > 0:
            print(f"   ✅ Doctor-specialty relationships work ({doctors_with_specialty}/{total_doctors})")
            results['passed'] += 1
        else:
            print(f"   ❌ No doctor-specialty relationships found")
            results['failed'] += 1
            results['errors'].append("No doctor-specialty relationships")
            
    except Exception as e:
        print(f"   ❌ Database operations exception: {str(e)}")
        results['failed'] += 1
        results['errors'].append(f"Database operations exception: {str(e)}")
    
    return results

def test_error_handling():
    """Test error handling and 404 pages"""
    print(f"\n🚨 Testing Error Handling")
    print("=" * 50)
    
    client = Client()
    results = {'passed': 0, 'failed': 0, 'errors': []}
    
    try:
        # Test 404 page
        response = client.get('/nonexistent-page/')
        if response.status_code == 404:
            print(f"   ✅ 404 errors handled correctly")
            results['passed'] += 1
        else:
            print(f"   ❌ 404 handling failed: {response.status_code}")
            results['failed'] += 1
            results['errors'].append("404 handling failed")
        
        # Test accessing protected page without login
        response = client.get('/admin-dashboard/')
        if response.status_code == 302:  # Should redirect to login
            print(f"   ✅ Protected pages redirect correctly")
            results['passed'] += 1
        else:
            print(f"   ❌ Protected page access not handled: {response.status_code}")
            results['failed'] += 1
            results['errors'].append("Protected page access not handled")
            
    except Exception as e:
        print(f"   ❌ Error handling test exception: {str(e)}")
        results['failed'] += 1
        results['errors'].append(f"Error handling exception: {str(e)}")
    
    return results

def main():
    """Run all live website tests"""
    print("🌐 Live Website Testing")
    print("=" * 80)
    
    # First check if server is running
    if not test_server_connectivity():
        print("\n❌ Cannot proceed with testing - server not accessible")
        return False
    
    all_results = {'passed': 0, 'failed': 0, 'errors': []}
    
    # Run all test suites
    test_suites = [
        ("Page Loads", test_page_loads),
        ("Form Submissions", test_form_submissions),
        ("Static Files", test_static_files),
        ("Database Operations", test_database_operations),
        ("Error Handling", test_error_handling)
    ]
    
    for suite_name, test_function in test_suites:
        print(f"\n🧪 Running {suite_name} Tests...")
        results = test_function()
        all_results['passed'] += results['passed']
        all_results['failed'] += results['failed']
        all_results['errors'].extend(results['errors'])
        
        print(f"   📊 {suite_name} Results: {results['passed']} passed, {results['failed']} failed")
    
    # Final summary
    print(f"\n" + "=" * 80)
    print(f"🎯 LIVE WEBSITE TEST RESULTS")
    print(f"=" * 80)
    print(f"✅ Total Passed: {all_results['passed']}")
    print(f"❌ Total Failed: {all_results['failed']}")
    
    if all_results['passed'] + all_results['failed'] > 0:
        success_rate = (all_results['passed']/(all_results['passed']+all_results['failed']))*100
        print(f"📊 Success Rate: {success_rate:.1f}%")
    
    if all_results['errors']:
        print(f"\n🔍 Issues Found:")
        for i, error in enumerate(all_results['errors'], 1):
            print(f"   {i}. {error}")
    
    # Website status
    if all_results['failed'] == 0:
        print(f"\n🎉 WEBSITE STATUS: ✅ FULLY FUNCTIONAL")
        print(f"   All pages load correctly and functionality works as expected.")
    elif all_results['failed'] <= 2:
        print(f"\n⚠️  WEBSITE STATUS: 🟡 MOSTLY FUNCTIONAL")
        print(f"   Minor issues detected but website is usable.")
    else:
        print(f"\n❌ WEBSITE STATUS: 🔴 NEEDS ATTENTION")
        print(f"   Multiple issues detected that affect functionality.")
    
    return all_results['failed'] <= 2

if __name__ == '__main__':
    try:
        success = main()
        sys.exit(0 if success else 1)
    except Exception as e:
        print(f"❌ Live website test execution failed: {str(e)}")
        sys.exit(1)