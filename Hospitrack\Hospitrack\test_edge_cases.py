#!/usr/bin/env python
"""
Edge cases and potential runtime issues testing
"""
import os
import sys
import django
from django.test import Client
from django.db import transaction

# Setup Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'hospitrack.settings')
django.setup()

from accounts.models import <PERSON><PERSON>, Doctor, Nurse, MedicalSpecialty
from patients.models import Patient

def test_edge_cases():
    """Test edge cases and potential runtime issues"""
    print("🔍 Testing Edge Cases and Runtime Issues")
    print("=" * 80)
    
    client = Client()
    results = {'passed': 0, 'failed': 0, 'errors': []}
    
    # Test 1: Long session handling
    print(f"\n⏱️ Testing Session Handling")
    try:
        client.login(username='admin', password='admin123')
        
        # Make multiple requests to test session persistence
        for i in range(5):
            response = client.get('/admin-dashboard/')
            if response.status_code != 200:
                print(f"   ❌ Session failed on request {i+1}")
                results['failed'] += 1
                results['errors'].append(f"Session failed on request {i+1}")
                break
        else:
            print(f"   ✅ Session handling works correctly")
            results['passed'] += 1
            
    except Exception as e:
        print(f"   ❌ Session handling exception: {str(e)}")
        results['failed'] += 1
        results['errors'].append(f"Session handling exception: {str(e)}")
    
    # Test 2: Large data handling
    print(f"\n📊 Testing Large Data Handling")
    try:
        # Test with large query results
        all_users = User.objects.all()
        if len(all_users) > 50:  # We have 79 users
            print(f"   ✅ Large user dataset handled ({len(all_users)} users)")
            results['passed'] += 1
        else:
            print(f"   ⚠️  Small dataset, cannot test large data handling")
            results['errors'].append("Cannot test large data handling")
        
        # Test nurses list page with many nurses
        response = client.get('/nurses/')
        if response.status_code == 200:
            print(f"   ✅ Nurses list page handles large dataset")
            results['passed'] += 1
        else:
            print(f"   ❌ Nurses list page failed with large dataset")
            results['failed'] += 1
            results['errors'].append("Nurses list failed with large dataset")
            
    except Exception as e:
        print(f"   ❌ Large data handling exception: {str(e)}")
        results['failed'] += 1
        results['errors'].append(f"Large data handling exception: {str(e)}")
    
    # Test 3: Invalid form data
    print(f"\n📝 Testing Invalid Form Data Handling")
    try:
        # Test patient registration with invalid data
        response = client.post('/patients/register/', {
            'patient_id': '',  # Empty required field
            'first_name': 'A' * 200,  # Very long name
            'date_of_birth': 'invalid-date',  # Invalid date
            'email': 'invalid-email',  # Invalid email
            'phone': 'abc123',  # Invalid phone
        })
        
        # Should handle gracefully (either show errors or redirect with message)
        if response.status_code in [200, 302]:
            print(f"   ✅ Invalid form data handled gracefully")
            results['passed'] += 1
        else:
            print(f"   ❌ Invalid form data caused server error: {response.status_code}")
            results['failed'] += 1
            results['errors'].append(f"Invalid form data server error: {response.status_code}")
            
    except Exception as e:
        print(f"   ❌ Invalid form data exception: {str(e)}")
        results['failed'] += 1
        results['errors'].append(f"Invalid form data exception: {str(e)}")
    
    # Test 4: Concurrent user access simulation
    print(f"\n👥 Testing Concurrent Access Simulation")
    try:
        # Create multiple client instances to simulate concurrent users
        clients = [Client() for _ in range(3)]
        users = [
            ('admin', 'admin123'),
            ('dr.emergency', 'doctor123'),
            ('emma.johnson1', 'nurse123')
        ]
        
        # Login all clients simultaneously
        for i, (username, password) in enumerate(users):
            if clients[i].login(username=username, password=password):
                print(f"   ✅ Concurrent user {i+1} ({username}) logged in")
                results['passed'] += 1
            else:
                print(f"   ❌ Concurrent user {i+1} ({username}) login failed")
                results['failed'] += 1
                results['errors'].append(f"Concurrent user {username} login failed")
        
        # Test simultaneous page access
        responses = []
        for i, client_instance in enumerate(clients):
            if i == 0:  # Admin
                response = client_instance.get('/admin-dashboard/')
            elif i == 1:  # Doctor
                response = client_instance.get('/doctor-dashboard/')
            else:  # Nurse
                response = client_instance.get('/nurse-dashboard/')
            responses.append(response)
        
        if all(r.status_code == 200 for r in responses):
            print(f"   ✅ Concurrent page access works")
            results['passed'] += 1
        else:
            print(f"   ❌ Concurrent page access failed")
            results['failed'] += 1
            results['errors'].append("Concurrent page access failed")
            
    except Exception as e:
        print(f"   ❌ Concurrent access exception: {str(e)}")
        results['failed'] += 1
        results['errors'].append(f"Concurrent access exception: {str(e)}")
    
    # Test 5: Database constraint violations
    print(f"\n🗄️ Testing Database Constraint Handling")
    try:
        # Try to create duplicate user (should be handled)
        existing_user = User.objects.first()
        if existing_user:
            try:
                with transaction.atomic():
                    User.objects.create_user(
                        username=existing_user.username,  # Duplicate username
                        password='test123',
                        user_type='nurse'
                    )
                print(f"   ❌ Duplicate user creation not prevented")
                results['failed'] += 1
                results['errors'].append("Duplicate user creation not prevented")
            except Exception:
                print(f"   ✅ Database constraints properly enforced")
                results['passed'] += 1
        else:
            print(f"   ⚠️  No existing users to test constraints")
            results['errors'].append("Cannot test database constraints")
            
    except Exception as e:
        print(f"   ❌ Database constraint test exception: {str(e)}")
        results['failed'] += 1
        results['errors'].append(f"Database constraint exception: {str(e)}")
    
    # Test 6: Memory usage with complex queries
    print(f"\n🧠 Testing Memory Usage with Complex Queries")
    try:
        # Complex query with joins
        doctors_with_nurses = Doctor.objects.select_related('user', 'specialty').prefetch_related(
            'specialty__nurses__user'
        ).all()
        
        if len(doctors_with_nurses) > 0:
            print(f"   ✅ Complex queries execute successfully ({len(doctors_with_nurses)} doctors)")
            results['passed'] += 1
        else:
            print(f"   ❌ Complex queries returned no results")
            results['failed'] += 1
            results['errors'].append("Complex queries returned no results")
            
    except Exception as e:
        print(f"   ❌ Complex query exception: {str(e)}")
        results['failed'] += 1
        results['errors'].append(f"Complex query exception: {str(e)}")
    
    # Test 7: Special characters and Unicode handling
    print(f"\n🌐 Testing Unicode and Special Characters")
    try:
        # Test with special characters in forms
        response = client.post('/patients/register/', {
            'patient_id': 'TEST_UNICODE_001',
            'first_name': 'José',  # Unicode character
            'last_name': 'García-López',  # Hyphen and accent
            'date_of_birth': '1990-01-01',
            'gender': 'M',
            'blood_group': 'O+',
            'phone': '+91-**********',  # Special characters
            'email': '<EMAIL>',
            'address': '123 Main St, Apt #4B',  # Special characters
            'emergency_contact_name': 'María García',
            'emergency_contact_phone': '**********',
            'emergency_contact_relation': 'Mother'
        })
        
        if response.status_code in [200, 302]:
            print(f"   ✅ Unicode and special characters handled correctly")
            results['passed'] += 1
        else:
            print(f"   ❌ Unicode handling failed: {response.status_code}")
            results['failed'] += 1
            results['errors'].append(f"Unicode handling failed: {response.status_code}")
            
    except Exception as e:
        print(f"   ❌ Unicode handling exception: {str(e)}")
        results['failed'] += 1
        results['errors'].append(f"Unicode handling exception: {str(e)}")
    
    return results

def test_performance_issues():
    """Test for potential performance issues"""
    print(f"\n⚡ Testing Performance Issues")
    print("=" * 50)
    
    client = Client()
    results = {'passed': 0, 'failed': 0, 'errors': []}
    
    try:
        import time
        
        client.login(username='admin', password='admin123')
        
        # Test page load times
        pages_to_test = [
            ('/admin-dashboard/', 'Admin Dashboard'),
            ('/doctors/', 'Doctors List'),
            ('/nurses/', 'Nurses List'),
            ('/patients/', 'Patients List')
        ]
        
        for url, name in pages_to_test:
            start_time = time.time()
            response = client.get(url)
            end_time = time.time()
            
            load_time = end_time - start_time
            
            if response.status_code == 200:
                if load_time < 2.0:  # Should load within 2 seconds
                    print(f"   ✅ {name} loads quickly ({load_time:.2f}s)")
                    results['passed'] += 1
                else:
                    print(f"   ⚠️  {name} loads slowly ({load_time:.2f}s)")
                    results['errors'].append(f"{name} slow load time: {load_time:.2f}s")
            else:
                print(f"   ❌ {name} failed to load")
                results['failed'] += 1
                results['errors'].append(f"{name} failed to load")
                
    except Exception as e:
        print(f"   ❌ Performance test exception: {str(e)}")
        results['failed'] += 1
        results['errors'].append(f"Performance test exception: {str(e)}")
    
    return results

def main():
    """Run all edge case tests"""
    print("🔍 Edge Cases and Runtime Issues Testing")
    print("=" * 80)
    
    all_results = {'passed': 0, 'failed': 0, 'errors': []}
    
    # Run test suites
    test_suites = [
        ("Edge Cases", test_edge_cases),
        ("Performance Issues", test_performance_issues)
    ]
    
    for suite_name, test_function in test_suites:
        print(f"\n🧪 Running {suite_name} Tests...")
        results = test_function()
        all_results['passed'] += results['passed']
        all_results['failed'] += results['failed']
        all_results['errors'].extend(results['errors'])
        
        print(f"   📊 {suite_name} Results: {results['passed']} passed, {results['failed']} failed")
    
    # Final summary
    print(f"\n" + "=" * 80)
    print(f"🎯 EDGE CASES TEST RESULTS")
    print(f"=" * 80)
    print(f"✅ Total Passed: {all_results['passed']}")
    print(f"❌ Total Failed: {all_results['failed']}")
    
    if all_results['passed'] + all_results['failed'] > 0:
        success_rate = (all_results['passed']/(all_results['passed']+all_results['failed']))*100
        print(f"📊 Success Rate: {success_rate:.1f}%")
    
    if all_results['errors']:
        print(f"\n🔍 Issues and Warnings:")
        for i, error in enumerate(all_results['errors'], 1):
            print(f"   {i}. {error}")
    
    # System robustness status
    if all_results['failed'] == 0:
        print(f"\n🛡️ SYSTEM ROBUSTNESS: ✅ EXCELLENT")
        print(f"   System handles edge cases and stress conditions well.")
    elif all_results['failed'] <= 2:
        print(f"\n🛡️ SYSTEM ROBUSTNESS: 🟡 GOOD")
        print(f"   System is robust with minor edge case issues.")
    else:
        print(f"\n🛡️ SYSTEM ROBUSTNESS: 🔴 NEEDS IMPROVEMENT")
        print(f"   System has issues handling edge cases.")
    
    return all_results['failed'] <= 2

if __name__ == '__main__':
    try:
        success = main()
        sys.exit(0 if success else 1)
    except Exception as e:
        print(f"❌ Edge cases test execution failed: {str(e)}")
        sys.exit(1)