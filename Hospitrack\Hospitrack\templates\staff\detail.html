{% extends 'base.html' %}

{% block title %}{{ staff_member.user.get_full_name }} - Nurse Details{% endblock %}

{% block content %}
<div class="space-y-6">
    <!-- Header -->
    <div class="bg-white shadow rounded-lg p-6">
        <div class="flex justify-between items-start">
            <div>
                <h1 class="text-3xl font-bold text-gray-900">{{ staff_member.user.get_full_name }}</h1>
                <p class="text-gray-600 mt-2">Employee ID: {{ staff_member.employee_id }}</p>
                <div class="mt-2 flex items-center space-x-4 text-sm text-gray-500">
                    <span>{{ staff_member.get_designation_display }}</span>
                    <span>{{ staff_member.department.name }}</span>
                    <span>{{ staff_member.get_employment_type_display }}</span>
                </div>
            </div>
            <div class="flex space-x-3">
                <a href="{% url 'staff:list' %}" class="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
                    <i class="fas fa-arrow-left mr-2"></i>
                    Back to Nurses
                </a>
            </div>
        </div>
    </div>

    <!-- Nurse Information Grid -->
    <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <!-- Personal Information -->
        <div class="bg-white shadow rounded-lg p-6">
            <h2 class="text-lg font-medium text-gray-900 mb-4">Personal Information</h2>
            <dl class="space-y-3">
                <div>
                    <dt class="text-sm font-medium text-gray-500">Full Name</dt>
                    <dd class="text-sm text-gray-900">{{ staff_member.user.get_full_name }}</dd>
                </div>
                <div>
                    <dt class="text-sm font-medium text-gray-500">Email</dt>
                    <dd class="text-sm text-gray-900">{{ staff_member.user.email }}</dd>
                </div>
                <div>
                    <dt class="text-sm font-medium text-gray-500">Employee ID</dt>
                    <dd class="text-sm text-gray-900">{{ staff_member.employee_id }}</dd>
                </div>
                <div>
                    <dt class="text-sm font-medium text-gray-500">Status</dt>
                    <dd class="text-sm text-gray-900">
                        <span class="px-2 py-1 text-xs font-semibold rounded-full {% if staff_member.is_active %}bg-green-100 text-green-800{% else %}bg-red-100 text-red-800{% endif %}">
                            {% if staff_member.is_active %}Active{% else %}Inactive{% endif %}
                        </span>
                    </dd>
                </div>
            </dl>
        </div>

        <!-- Employment Information -->
        <div class="bg-white shadow rounded-lg p-6">
            <h2 class="text-lg font-medium text-gray-900 mb-4">Employment Information</h2>
            <dl class="space-y-3">
                <div>
                    <dt class="text-sm font-medium text-gray-500">Department</dt>
                    <dd class="text-sm text-gray-900">{{ staff_member.department.name }}</dd>
                </div>
                <div>
                    <dt class="text-sm font-medium text-gray-500">Designation</dt>
                    <dd class="text-sm text-gray-900">
                        <span class="px-2 py-1 text-xs font-semibold rounded-full bg-purple-100 text-purple-800">
                            {{ staff_member.get_designation_display }}
                        </span>
                    </dd>
                </div>
                <div>
                    <dt class="text-sm font-medium text-gray-500">Employment Type</dt>
                    <dd class="text-sm text-gray-900">{{ staff_member.get_employment_type_display }}</dd>
                </div>
                <div>
                    <dt class="text-sm font-medium text-gray-500">Hire Date</dt>
                    <dd class="text-sm text-gray-900">{{ staff_member.hire_date|date:"F d, Y" }}</dd>
                </div>
                {% if staff_member.experience_years %}
                <div>
                    <dt class="text-sm font-medium text-gray-500">Experience</dt>
                    <dd class="text-sm text-gray-900">{{ staff_member.experience_years }} years</dd>
                </div>
                {% endif %}
            </dl>
        </div>

        <!-- Nursing Information -->
        <div class="bg-white shadow rounded-lg p-6">
            <h2 class="text-lg font-medium text-gray-900 mb-4">Nursing Information</h2>
            <dl class="space-y-3">
                {% if staff_member.user.nurse %}
                <div>
                    <dt class="text-sm font-medium text-gray-500">Shift</dt>
                    <dd class="text-sm text-gray-900">
                        <span class="px-2 py-1 text-xs font-semibold rounded-full 
                            {% if staff_member.user.nurse.shift == 'morning' %}bg-yellow-100 text-yellow-800
                            {% elif staff_member.user.nurse.shift == 'evening' %}bg-orange-100 text-orange-800
                            {% else %}bg-blue-100 text-blue-800{% endif %}">
                            {{ staff_member.user.nurse.get_shift_display }}
                        </span>
                    </dd>
                </div>
                <div>
                    <dt class="text-sm font-medium text-gray-500">License Number</dt>
                    <dd class="text-sm text-gray-900">{{ staff_member.user.nurse.license_number }}</dd>
                </div>
                {% endif %}
                {% if staff_member.salary %}
                <div>
                    <dt class="text-sm font-medium text-gray-500">Salary</dt>
                    <dd class="text-sm text-gray-900">${{ staff_member.salary|floatformat:0 }}</dd>
                </div>
                {% endif %}
            </dl>
        </div>
    </div>

    <!-- Emergency Contact -->
    <div class="bg-white shadow rounded-lg p-6">
        <h2 class="text-lg font-medium text-gray-900 mb-4">Emergency Contact</h2>
        <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div>
                <dt class="text-sm font-medium text-gray-500 mb-2">Contact Name</dt>
                <dd class="text-sm text-gray-900">{{ staff_member.emergency_contact_name|default:"Not provided" }}</dd>
            </div>
            <div>
                <dt class="text-sm font-medium text-gray-500 mb-2">Contact Phone</dt>
                <dd class="text-sm text-gray-900">{{ staff_member.emergency_contact_phone|default:"Not provided" }}</dd>
            </div>
            <div>
                <dt class="text-sm font-medium text-gray-500 mb-2">Relationship</dt>
                <dd class="text-sm text-gray-900">{{ staff_member.emergency_contact_relation|default:"Not provided" }}</dd>
            </div>
        </div>
    </div>

    <!-- Qualifications -->
    {% if staff_member.qualifications %}
    <div class="bg-white shadow rounded-lg p-6">
        <h2 class="text-lg font-medium text-gray-900 mb-4">Qualifications</h2>
        <div class="bg-gray-50 rounded-lg p-4">
            <p class="text-sm text-gray-900">{{ staff_member.qualifications }}</p>
        </div>
    </div>
    {% endif %}

    <!-- Quick Actions -->
    <div class="bg-white shadow rounded-lg p-6">
        <h2 class="text-lg font-medium text-gray-900 mb-4">Quick Actions</h2>
        <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
            {% if user.user_type == 'admin' %}
            <a href="{% url 'staff:edit' staff_member.id %}" class="inline-flex items-center justify-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700">
                <i class="fas fa-edit mr-2"></i>
                Edit Details
            </a>

            <a href="{% url 'staff:schedule' staff_member.id %}" class="inline-flex items-center justify-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-green-600 hover:bg-green-700">
                <i class="fas fa-calendar mr-2"></i>
                View Schedule
            </a>

            <a href="{% url 'staff:list' %}" class="inline-flex items-center justify-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
                <i class="fas fa-list mr-2"></i>
                Back to Nurses List
            </a>
            {% endif %}
        </div>
    </div>

    <!-- Employment History -->
    <div class="bg-white shadow rounded-lg p-6">
        <h2 class="text-lg font-medium text-gray-900 mb-4">Employment Summary</h2>
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
                <h3 class="font-medium text-gray-900 mb-2">Current Position</h3>
                <dl class="space-y-1 text-sm">
                    <div class="flex justify-between">
                        <dt class="text-gray-500">Position:</dt>
                        <dd class="text-gray-900">{{ staff_member.get_designation_display }}</dd>
                    </div>
                    <div class="flex justify-between">
                        <dt class="text-gray-500">Department:</dt>
                        <dd class="text-gray-900">{{ staff_member.department.name }}</dd>
                    </div>
                    <div class="flex justify-between">
                        <dt class="text-gray-500">Start Date:</dt>
                        <dd class="text-gray-900">{{ staff_member.hire_date|date:"M d, Y" }}</dd>
                    </div>
                    <div class="flex justify-between">
                        <dt class="text-gray-500">Employment Type:</dt>
                        <dd class="text-gray-900">{{ staff_member.get_employment_type_display }}</dd>
                    </div>
                </dl>
            </div>
            
            <div>
                <h3 class="font-medium text-gray-900 mb-2">Account Information</h3>
                <dl class="space-y-1 text-sm">
                    <div class="flex justify-between">
                        <dt class="text-gray-500">Username:</dt>
                        <dd class="text-gray-900">{{ staff_member.user.username }}</dd>
                    </div>
                    <div class="flex justify-between">
                        <dt class="text-gray-500">User Type:</dt>
                        <dd class="text-gray-900">{{ staff_member.user.get_user_type_display }}</dd>
                    </div>
                    <div class="flex justify-between">
                        <dt class="text-gray-500">Last Login:</dt>
                        <dd class="text-gray-900">{{ staff_member.user.last_login|date:"M d, Y H:i"|default:"Never" }}</dd>
                    </div>
                    <div class="flex justify-between">
                        <dt class="text-gray-500">Account Status:</dt>
                        <dd class="text-gray-900">
                            {% if staff_member.user.is_active %}
                                <span class="text-green-600">Active</span>
                            {% else %}
                                <span class="text-red-600">Inactive</span>
                            {% endif %}
                        </dd>
                    </div>
                </dl>
            </div>
        </div>
    </div>
</div>
{% endblock %}
