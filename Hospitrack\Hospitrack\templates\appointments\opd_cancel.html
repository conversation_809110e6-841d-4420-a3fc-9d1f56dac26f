{% extends 'base.html' %}

{% block title %}Cancel OPD Registration - HospiTrack{% endblock %}

{% block content %}
<div class="space-y-6">
    <!-- Header -->
    <div class="bg-white shadow rounded-lg p-6">
        <div class="flex justify-between items-center">
            <div>
                <h1 class="text-3xl font-bold text-gray-900">Cancel OPD Registration</h1>
                <p class="text-gray-600 mt-2">{{ registration.opd_number }} - {{ registration.patient_name }}</p>
            </div>
            <div class="flex space-x-3">
                <a href="{% url 'appointments:opd_schedule' %}" class="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
                    <i class="fas fa-arrow-left mr-2"></i>
                    Back to OPD Management
                </a>
            </div>
        </div>
    </div>

    <!-- Confirmation Card -->
    <div class="bg-white shadow rounded-lg p-6">
        <div class="flex items-center mb-6">
            <div class="flex-shrink-0">
                <i class="fas fa-exclamation-triangle text-4xl text-red-500"></i>
            </div>
            <div class="ml-4">
                <h2 class="text-xl font-bold text-gray-900">Confirm Cancellation</h2>
                <p class="text-gray-600">Are you sure you want to cancel this OPD registration?</p>
            </div>
        </div>

        <!-- Patient Information -->
        <div class="bg-gray-50 rounded-lg p-4 mb-6">
            <h3 class="text-lg font-medium text-gray-900 mb-3">Registration Details</h3>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                    <span class="text-sm font-medium text-gray-500">OPD Number:</span>
                    <p class="text-sm text-gray-900 font-mono">{{ registration.opd_number }}</p>
                </div>
                <div>
                    <span class="text-sm font-medium text-gray-500">Patient Name:</span>
                    <p class="text-sm text-gray-900">{{ registration.patient_name }}</p>
                </div>
                <div>
                    <span class="text-sm font-medium text-gray-500">Doctor:</span>
                    <p class="text-sm text-gray-900">Dr. {{ registration.doctor.user.get_full_name }}</p>
                </div>
                <div>
                    <span class="text-sm font-medium text-gray-500">Specialization:</span>
                    <p class="text-sm text-gray-900">{{ registration.doctor.specialization }}</p>
                </div>
                <div>
                    <span class="text-sm font-medium text-gray-500">Appointment Time:</span>
                    <p class="text-sm text-gray-900">{{ registration.get_preferred_time_display }}</p>
                </div>
                <div>
                    <span class="text-sm font-medium text-gray-500">Queue Number:</span>
                    <p class="text-sm text-gray-900">#{{ registration.queue_number }}</p>
                </div>
                <div>
                    <span class="text-sm font-medium text-gray-500">Chief Complaint:</span>
                    <p class="text-sm text-gray-900">{{ registration.chief_complaint }}</p>
                </div>
                <div>
                    <span class="text-sm font-medium text-gray-500">Current Status:</span>
                    <p class="text-sm">
                        <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full 
                            {% if registration.status == 'registered' %}bg-blue-100 text-blue-800
                            {% elif registration.status == 'waiting' %}bg-yellow-100 text-yellow-800
                            {% elif registration.status == 'consulting' %}bg-green-100 text-green-800
                            {% elif registration.status == 'completed' %}bg-purple-100 text-purple-800
                            {% else %}bg-gray-100 text-gray-800{% endif %}">
                            {{ registration.get_status_display }}
                        </span>
                    </p>
                </div>
            </div>
        </div>

        <!-- Warning Message -->
        <div class="bg-red-50 border border-red-200 rounded-lg p-4 mb-6">
            <div class="flex">
                <div class="flex-shrink-0">
                    <i class="fas fa-exclamation-circle text-red-400"></i>
                </div>
                <div class="ml-3">
                    <h3 class="text-sm font-medium text-red-800">Important Notice</h3>
                    <div class="mt-2 text-sm text-red-700">
                        <ul class="list-disc list-inside space-y-1">
                            <li>This will mark the OPD registration as <strong>cancelled</strong></li>
                            <li>The patient will be removed from today's appointment queue</li>
                            <li>The registration record will be kept for history (not deleted)</li>
                            <li><strong>Patient's personal information will NOT be deleted</strong></li>
                            <li>You may need to inform the patient about the cancellation</li>
                            {% if registration.is_paid %}
                            <li class="font-medium">Payment refund may need to be processed separately</li>
                            {% endif %}
                        </ul>
                    </div>
                </div>
            </div>
        </div>

        <!-- Action Buttons -->
        <div class="flex justify-end space-x-3">
            <a href="{% url 'appointments:opd_schedule' %}" class="px-6 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
                <i class="fas fa-times mr-2"></i>
                No, Keep Registration
            </a>
            <form method="post" class="inline">
                {% csrf_token %}
                <button type="submit" class="px-6 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-red-600 hover:bg-red-700">
                    <i class="fas fa-ban mr-2"></i>
                    Yes, Cancel Registration
                </button>
            </form>
        </div>
    </div>

    <!-- Additional Information -->
    <div class="bg-blue-50 border border-blue-200 rounded-lg p-4">
        <div class="flex">
            <div class="flex-shrink-0">
                <i class="fas fa-info-circle text-blue-400"></i>
            </div>
            <div class="ml-3">
                <h3 class="text-sm font-medium text-blue-800">Alternative Actions</h3>
                <div class="mt-2 text-sm text-blue-700">
                    <p>Instead of cancelling, you can also:</p>
                    <ul class="list-disc list-inside space-y-1 mt-1">
                        <li><a href="{% url 'appointments:opd_edit' registration.id %}" class="underline hover:text-blue-900">Edit the registration details</a></li>
                        <li><a href="{% url 'appointments:opd_detail' registration.id %}" class="underline hover:text-blue-900">View complete patient information</a></li>
                        <li>Reschedule the appointment for another day</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
