from django.shortcuts import render, redirect, get_object_or_404
from django.contrib.auth.decorators import login_required
from django.contrib import messages
from .models import Bed, Ward, BedAssignment, BedStatusUpdate

@login_required
def bed_management(request):
    """Bed management overview"""
    wards = Ward.objects.filter(is_active=True)
    beds = Bed.objects.filter(is_active=True)

    # Bed statistics
    total_beds = beds.count()
    available_beds = beds.filter(status='available').count()
    occupied_beds = beds.filter(status='occupied').count()
    maintenance_beds = beds.filter(status='maintenance').count()
    emergency_beds = beds.filter(status='emergency').count()

    context = {
        'wards': wards,
        'beds': beds,
        'total_beds': total_beds,
        'available_beds': available_beds,
        'occupied_beds': occupied_beds,
        'maintenance_beds': maintenance_beds,
        'emergency_beds': emergency_beds,
    }
    return render(request, 'beds/management.html', context)

@login_required
def bed_assign(request):
    """Assign bed to patient"""
    return render(request, 'beds/assign.html')

@login_required
def bed_status_update(request, bed_id):
    """Update bed status"""
    bed = get_object_or_404(Bed, id=bed_id)
    if request.method == 'POST':
        new_status = request.POST.get('status')
        reason = request.POST.get('reason', '')

        if new_status and new_status != bed.status:
            # Create status update record
            BedStatusUpdate.objects.create(
                bed=bed,
                previous_status=bed.status,
                new_status=new_status,
                updated_by=request.user,
                reason=reason
            )

            bed.status = new_status
            bed.save()

            messages.success(request, f'Bed {bed.bed_number} status updated to {new_status}')

        return redirect('beds:management')

    return render(request, 'beds/status_update.html', {'bed': bed})

@login_required
def ward_list(request):
    """List all wards"""
    wards = Ward.objects.filter(is_active=True)
    return render(request, 'beds/ward_list.html', {'wards': wards})

@login_required
def ward_create(request):
    """Create new ward"""
    return render(request, 'beds/ward_create.html')
