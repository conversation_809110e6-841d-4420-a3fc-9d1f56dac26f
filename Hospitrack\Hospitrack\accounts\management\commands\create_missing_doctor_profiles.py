from django.core.management.base import BaseCommand
from django.contrib.auth import get_user_model
from accounts.models import Doctor

User = get_user_model()

class Command(BaseCommand):
    help = 'Create missing doctor profiles for existing doctor users'

    def handle(self, *args, **options):
        self.stdout.write('Creating missing doctor profiles...')
        
        # Doctor data mapping
        doctor_data = {
            'dr.cardiologist': {
                'specialization': 'Cardiologist',
                'department': 'Cardiology',
                'license_number': 'CARD001',
                'experience_years': 12,
                'consultation_fee': 800.00,
                'qualifications': 'MBBS, MD Cardiology, Fellowship in Interventional Cardiology',
                'phone': '**********'
            },
            'dr.nephrologist': {
                'specialization': 'Nephrologist',
                'department': 'Nephrology',
                'license_number': 'NEPH001',
                'experience_years': 10,
                'consultation_fee': 750.00,
                'qualifications': 'MBBS, MD Internal Medicine, DM Nephrology',
                'phone': '**********'
            },
            'dr.pulmonologist': {
                'specialization': 'Pulmonologist',
                'department': 'Pulmonology',
                'license_number': 'PULM001',
                'experience_years': 8,
                'consultation_fee': 700.00,
                'qualifications': 'MBBS, MD Pulmonary Medicine, Fellowship in Critical Care',
                'phone': '**********'
            },
            'dr.hepatologist': {
                'specialization': 'Hepatologist',
                'department': 'Gastroenterology',
                'license_number': 'HEPT001',
                'experience_years': 15,
                'consultation_fee': 850.00,
                'qualifications': 'MBBS, MD Gastroenterology, DM Hepatology',
                'phone': '**********'
            },
            'dr.orthopedist': {
                'specialization': 'Orthopedist',
                'department': 'Orthopedics',
                'license_number': 'ORTH001',
                'experience_years': 14,
                'consultation_fee': 900.00,
                'qualifications': 'MBBS, MS Orthopedics, Fellowship in Joint Replacement',
                'phone': '**********'
            },
            'dr.pediatrician': {
                'specialization': 'Pediatrician',
                'department': 'Pediatrics',
                'license_number': 'PEDI001',
                'experience_years': 9,
                'consultation_fee': 600.00,
                'qualifications': 'MBBS, MD Pediatrics, Fellowship in Pediatric Cardiology',
                'phone': '**********'
            },
            'dr.ent': {
                'specialization': 'ENT Specialist',
                'department': 'ENT',
                'license_number': 'ENT001',
                'experience_years': 11,
                'consultation_fee': 650.00,
                'qualifications': 'MBBS, MS ENT, Fellowship in Head and Neck Surgery',
                'phone': '**********'
            },
            'dr.neurosurgeon': {
                'specialization': 'Neurosurgeon',
                'department': 'Neurosurgery',
                'license_number': 'NEUR001',
                'experience_years': 16,
                'consultation_fee': 1200.00,
                'qualifications': 'MBBS, MS Neurosurgery, Fellowship in Spine Surgery',
                'phone': '**********'
            },
            'dr.neurologist': {
                'specialization': 'Neurologist',
                'department': 'Neurology',
                'license_number': 'NEURO001',
                'experience_years': 13,
                'consultation_fee': 800.00,
                'qualifications': 'MBBS, MD Neurology, DM Neurology',
                'phone': '**********'
            },
            'dr.psychiatrist': {
                'specialization': 'Psychiatrist',
                'department': 'Psychiatry',
                'license_number': 'PSYC001',
                'experience_years': 7,
                'consultation_fee': 700.00,
                'qualifications': 'MBBS, MD Psychiatry, Fellowship in Child Psychiatry',
                'phone': '**********'
            }
        }
        
        created_count = 0
        for username, data in doctor_data.items():
            try:
                user = User.objects.get(username=username)
                
                # Check if doctor profile already exists
                if hasattr(user, 'doctor'):
                    self.stdout.write(f'- Profile already exists for {username}')
                    continue
                
                # Create doctor profile
                doctor = Doctor.objects.create(
                    user=user,
                    specialization=data['specialization'],
                    license_number=data['license_number'],
                    department=data['department'],
                    consultation_fee=data['consultation_fee'],
                    available_from='09:00',
                    available_to='17:00',
                    is_available=True
                )
                
                created_count += 1
                self.stdout.write(f'✓ Created profile for Dr. {user.get_full_name()} - {doctor.specialization}')
                
            except User.DoesNotExist:
                self.stdout.write(f'✗ User {username} not found')
            except Exception as e:
                self.stdout.write(f'✗ Error creating profile for {username}: {str(e)}')
        
        self.stdout.write(f'\n✅ Created {created_count} doctor profiles.')
        self.stdout.write(f'📊 Total doctors: {Doctor.objects.count()}')
