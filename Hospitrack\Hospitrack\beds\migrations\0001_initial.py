# Generated by Django 5.2.4 on 2025-07-20 15:24

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('patients', '0001_initial'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='Bed',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('bed_number', models.CharField(max_length=10)),
                ('status', models.CharField(choices=[('available', 'Available'), ('occupied', 'Occupied'), ('maintenance', 'Under Maintenance'), ('emergency', 'Emergency Reserved')], default='available', max_length=15)),
                ('bed_type', models.CharField(default='Standard', max_length=50)),
                ('daily_rate', models.DecimalField(decimal_places=2, default=0.0, max_digits=10)),
                ('is_active', models.BooleanField(default=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
            options={
                'ordering': ['ward', 'bed_number'],
            },
        ),
        migrations.CreateModel(
            name='Ward',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100)),
                ('ward_type', models.CharField(choices=[('general', 'General Ward'), ('private', 'Private Ward'), ('icu', 'ICU'), ('emergency', 'Emergency'), ('pediatric', 'Pediatric'), ('maternity', 'Maternity'), ('surgery', 'Surgery')], max_length=20)),
                ('floor', models.IntegerField()),
                ('capacity', models.IntegerField()),
                ('description', models.TextField(blank=True, null=True)),
                ('is_active', models.BooleanField(default=True)),
            ],
        ),
        migrations.CreateModel(
            name='BedAssignment',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('assigned_date', models.DateTimeField(auto_now_add=True)),
                ('discharge_date', models.DateTimeField(blank=True, null=True)),
                ('notes', models.TextField(blank=True, null=True)),
                ('is_active', models.BooleanField(default=True)),
                ('assigned_by', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='bed_assignments', to=settings.AUTH_USER_MODEL)),
                ('bed', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='assignments', to='beds.bed')),
                ('patient', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='bed_assignments', to='patients.patient')),
            ],
            options={
                'ordering': ['-assigned_date'],
            },
        ),
        migrations.CreateModel(
            name='BedStatusUpdate',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('previous_status', models.CharField(max_length=15)),
                ('new_status', models.CharField(max_length=15)),
                ('reason', models.TextField(blank=True, null=True)),
                ('updated_at', models.DateTimeField(auto_now_add=True)),
                ('bed', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='status_updates', to='beds.bed')),
                ('updated_by', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'ordering': ['-updated_at'],
            },
        ),
        migrations.AddField(
            model_name='bed',
            name='ward',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='beds', to='beds.ward'),
        ),
        migrations.AlterUniqueTogether(
            name='bed',
            unique_together={('ward', 'bed_number')},
        ),
    ]
