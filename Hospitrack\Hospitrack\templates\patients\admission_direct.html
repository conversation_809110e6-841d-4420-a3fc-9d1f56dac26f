{% extends 'base.html' %}

{% block title %}Admit {{ patient.full_name }} - HospiTrack{% endblock %}

{% block content %}
<div class="space-y-6">
    <!-- Header -->
    <div class="bg-white shadow rounded-lg p-6">
        <div class="flex justify-between items-center">
            <div>
                <h1 class="text-3xl font-bold text-gray-900">Admit Patient to Hospital</h1>
                <p class="text-gray-600 mt-2">Admitting: <span class="font-semibold text-gray-900">{{ patient.full_name }}</span> ({{ patient.patient_id }})</p>
            </div>
            <div class="flex space-x-3">
                <a href="{% url 'patients:detail' patient.id %}" class="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
                    <i class="fas fa-arrow-left mr-2"></i>
                    Back to Patient
                </a>
                <a href="{% url 'patients:admission_management' %}" class="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
                    <i class="fas fa-list mr-2"></i>
                    Admission Management
                </a>
            </div>
        </div>
    </div>

    <!-- Patient Summary -->
    <div class="bg-blue-50 border border-blue-200 rounded-lg p-6">
        <h2 class="text-lg font-semibold text-blue-900 mb-4">Patient Summary</h2>
        <div class="grid grid-cols-1 md:grid-cols-4 gap-4 text-sm">
            <div>
                <span class="font-medium text-blue-700">Name:</span>
                <span class="text-blue-900">{{ patient.full_name }}</span>
            </div>
            <div>
                <span class="font-medium text-blue-700">Age:</span>
                <span class="text-blue-900">{{ patient.age }} years</span>
            </div>
            <div>
                <span class="font-medium text-blue-700">Gender:</span>
                <span class="text-blue-900">{{ patient.get_gender_display }}</span>
            </div>
            <div>
                <span class="font-medium text-blue-700">Blood Group:</span>
                <span class="text-blue-900">{{ patient.blood_group }}</span>
            </div>
            <div>
                <span class="font-medium text-blue-700">Phone:</span>
                <span class="text-blue-900">{{ patient.phone }}</span>
            </div>
            <div>
                <span class="font-medium text-blue-700">Emergency Contact:</span>
                <span class="text-blue-900">{{ patient.emergency_contact_name }} ({{ patient.emergency_contact_phone }})</span>
            </div>
            {% if patient.insurance_number %}
            <div>
                <span class="font-medium text-blue-700">Insurance:</span>
                <span class="text-blue-900">{{ patient.insurance_number }}</span>
            </div>
            {% endif %}
        </div>
    </div>

    <!-- Admission Form -->
    <div class="bg-white shadow rounded-lg p-6">
        <h2 class="text-xl font-bold text-gray-900 mb-6">Hospital Admission Details</h2>
        
        <form method="post" class="space-y-6">
            {% csrf_token %}
            
            <!-- Doctor and Admission Type -->
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">
                        Attending Doctor <span class="text-red-500">*</span>
                    </label>
                    <select name="doctor" required class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500 bg-white">
                        <option value="">Choose Doctor</option>
                        {% for doctor in doctors %}
                        <option value="{{ doctor.id }}">
                            Dr. {{ doctor.user.get_full_name }} - {{ doctor.specialization }}
                        </option>
                        {% endfor %}
                    </select>
                </div>
                
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">
                        Admission Type <span class="text-red-500">*</span>
                    </label>
                    <select name="admission_type" required class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500 bg-white">
                        <option value="">Select Type</option>
                        <option value="emergency">Emergency</option>
                        <option value="planned">Planned</option>
                        <option value="transfer">Transfer</option>
                        <option value="observation">Observation</option>
                    </select>
                </div>
            </div>

            <!-- Expected Discharge Date -->
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">
                        Expected Discharge Date
                    </label>
                    <input type="date" name="expected_discharge_date" class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500">
                </div>
            </div>

            <!-- Medical Information -->
            <div class="space-y-6">
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">
                        Reason for Admission <span class="text-red-500">*</span>
                    </label>
                    <textarea name="reason_for_admission" required rows="3" class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500 resize-vertical" placeholder="Describe the reason for hospital admission"></textarea>
                </div>
                
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">
                        Primary Diagnosis <span class="text-red-500">*</span>
                    </label>
                    <textarea name="diagnosis" required rows="3" class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500 resize-vertical" placeholder="Primary diagnosis or suspected condition"></textarea>
                </div>
                
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">
                        Current Symptoms
                    </label>
                    <textarea name="symptoms" rows="3" class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500 resize-vertical" placeholder="Current symptoms and complaints"></textarea>
                </div>
                
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">
                            Medical History
                        </label>
                        <textarea name="medical_history" rows="4" class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500 resize-vertical" placeholder="Relevant medical history">{{ patient.medical_history }}</textarea>
                        <p class="text-xs text-gray-500 mt-1">Pre-filled from patient record</p>
                    </div>
                    
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">
                            Known Allergies
                        </label>
                        <textarea name="allergies" rows="4" class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500 resize-vertical" placeholder="Known allergies and reactions">{{ patient.allergies }}</textarea>
                        <p class="text-xs text-gray-500 mt-1">Pre-filled from patient record</p>
                    </div>
                </div>
                
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">
                        Current Medications
                    </label>
                    <textarea name="current_medications" rows="3" class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500 resize-vertical" placeholder="Current medications and dosages">{{ patient.current_medications }}</textarea>
                    <p class="text-xs text-gray-500 mt-1">Pre-filled from patient record</p>
                </div>
            </div>

            <!-- Form Actions -->
            <div class="flex justify-end space-x-3 pt-6 border-t border-gray-200">
                <a href="{% url 'patients:detail' patient.id %}" class="px-6 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
                    Cancel
                </a>
                <button type="submit" class="px-6 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-orange-600 hover:bg-orange-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-orange-500">
                    <i class="fas fa-bed mr-2"></i>
                    Admit to Hospital
                </button>
            </div>
        </form>
    </div>
</div>

<script>
// Auto-populate doctor details when selected
document.querySelector('select[name="doctor"]').addEventListener('change', function() {
    const selectedOption = this.options[this.selectedIndex];
    if (selectedOption.value) {
        console.log('Doctor selected:', selectedOption.text);
    }
});

// Set minimum date to today for expected discharge
document.addEventListener('DOMContentLoaded', function() {
    const today = new Date().toISOString().split('T')[0];
    document.querySelector('input[name="expected_discharge_date"]').setAttribute('min', today);
});
</script>
{% endblock %}
