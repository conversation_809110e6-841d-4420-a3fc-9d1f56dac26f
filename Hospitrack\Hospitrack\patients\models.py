from django.db import models
from django.contrib.auth import get_user_model

User = get_user_model()

class Patient(models.Model):
    GENDER_CHOICES = [
        ('M', 'Male'),
        ('F', 'Female'),
        ('O', 'Other'),
    ]

    BLOOD_GROUP_CHOICES = [
        ('A+', 'A+'), ('A-', 'A-'),
        ('B+', 'B+'), ('B-', 'B-'),
        ('AB+', 'AB+'), ('AB-', 'AB-'),
        ('O+', 'O+'), ('O-', 'O-'),
    ]

    patient_id = models.Char<PERSON><PERSON>(max_length=20, unique=True)
    first_name = models.CharField(max_length=50)
    last_name = models.CharField(max_length=50)
    date_of_birth = models.DateField()
    gender = models.Char<PERSON>ield(max_length=1, choices=GENDER_CHOICES)
    blood_group = models.Char<PERSON>ield(max_length=3, choices=BLOOD_GROUP_CHOICES)
    phone = models.CharField(max_length=15)
    email = models.EmailField(blank=True, null=True)
    address = models.TextField()
    emergency_contact_name = models.CharField(max_length=100)
    emergency_contact_phone = models.CharField(max_length=15)
    emergency_contact_relation = models.CharField(max_length=50)
    medical_history = models.TextField(blank=True, null=True)
    allergies = models.TextField(blank=True, null=True)
    current_medications = models.TextField(blank=True, null=True)
    insurance_number = models.CharField(max_length=50, blank=True, null=True)
    # Specialty-based access control
    primary_specialty = models.ForeignKey('accounts.MedicalSpecialty', on_delete=models.SET_NULL, null=True, blank=True, 
                                        related_name='patients', help_text="Primary medical specialty handling this patient")
    is_active = models.BooleanField(default=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    created_by = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, blank=True)

    def __str__(self):
        return f"{self.patient_id} - {self.first_name} {self.last_name}"

    @property
    def full_name(self):
        return f"{self.first_name} {self.last_name}"

    @property
    def age(self):
        from datetime import date
        today = date.today()
        return today.year - self.date_of_birth.year - ((today.month, today.day) < (self.date_of_birth.month, self.date_of_birth.day))

    @property
    def is_discharged(self):
        """Check if patient has been discharged"""
        try:
            return hasattr(self, 'patientdischarge') and self.patientdischarge is not None
        except:
            return False

    def get_accessible_doctors(self):
        """Get doctors who can access this patient based on specialty"""
        if self.primary_specialty:
            from accounts.models import Doctor
            return Doctor.objects.filter(specialty=self.primary_specialty, is_available=True)
        return Doctor.objects.none()

    def get_accessible_nurses(self):
        """Get nurses who can access this patient based on specialty"""
        if self.primary_specialty:
            from accounts.models import Nurse
            return Nurse.objects.filter(specialty=self.primary_specialty)
        return Nurse.objects.none()

    def can_user_access(self, user):
        """Check if a user can access this patient based on specialty"""
        if user.user_type == 'admin':
            return True
        
        if user.user_type == 'doctor':
            try:
                from accounts.models import Doctor
                doctor = Doctor.objects.get(user=user)
                return self.primary_specialty == doctor.specialty
            except Doctor.DoesNotExist:
                return False
        
        if user.user_type == 'nurse':
            try:
                from accounts.models import Nurse
                nurse = Nurse.objects.get(user=user)
                return self.primary_specialty == nurse.specialty
            except Nurse.DoesNotExist:
                return False
        
        return False

class PatientUpdate(models.Model):
    patient = models.ForeignKey(Patient, on_delete=models.CASCADE, related_name='updates')
    update_text = models.TextField()
    vital_signs = models.JSONField(blank=True, null=True)  # Store BP, temperature, pulse, etc.
    medications_given = models.TextField(blank=True, null=True)
    notes = models.TextField(blank=True, null=True)
    updated_by = models.ForeignKey(User, on_delete=models.CASCADE)
    created_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        ordering = ['-created_at']

    def __str__(self):
        return f"Update for {self.patient.full_name} on {self.created_at.strftime('%Y-%m-%d %H:%M')}"

class PatientDischarge(models.Model):
    patient = models.OneToOneField(Patient, on_delete=models.CASCADE)
    discharge_date = models.DateTimeField()
    discharge_summary = models.TextField()
    final_diagnosis = models.TextField()
    treatment_given = models.TextField()
    medications_prescribed = models.TextField(blank=True, null=True)
    follow_up_instructions = models.TextField(blank=True, null=True)
    follow_up_date = models.DateField(blank=True, null=True)
    discharged_by = models.ForeignKey(User, on_delete=models.CASCADE)
    created_at = models.DateTimeField(auto_now_add=True)

    def __str__(self):
        return f"Discharge - {self.patient.full_name} on {self.discharge_date.strftime('%Y-%m-%d')}"

class PatientAdmission(models.Model):
    """Patient admission for hospital stay"""
    ADMISSION_TYPE_CHOICES = [
        ('emergency', 'Emergency'),
        ('planned', 'Planned'),
        ('transfer', 'Transfer'),
        ('observation', 'Observation'),
    ]

    STATUS_CHOICES = [
        ('admitted', 'Admitted'),
        ('discharged', 'Discharged'),
        ('transferred', 'Transferred'),
    ]

    # Basic Information
    admission_id = models.CharField(max_length=20, unique=True)
    patient = models.ForeignKey(Patient, on_delete=models.CASCADE, related_name='admissions')
    doctor = models.ForeignKey('accounts.Doctor', on_delete=models.CASCADE, related_name='patient_admissions')

    # Admission Details
    admission_date = models.DateTimeField()
    admission_type = models.CharField(max_length=15, choices=ADMISSION_TYPE_CHOICES, default='planned')
    reason_for_admission = models.TextField()
    diagnosis = models.TextField()

    # Medical Information
    symptoms = models.TextField(blank=True, null=True)
    medical_history = models.TextField(blank=True, null=True)
    allergies = models.TextField(blank=True, null=True)
    current_medications = models.TextField(blank=True, null=True)

    # Status and Dates
    status = models.CharField(max_length=15, choices=STATUS_CHOICES, default='admitted')
    expected_discharge_date = models.DateField(blank=True, null=True)
    actual_discharge_date = models.DateTimeField(blank=True, null=True)

    # System fields
    admitted_by = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, blank=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        ordering = ['-admission_date']

    def save(self, *args, **kwargs):
        if not self.admission_id:
            from datetime import date
            today = date.today()
            # Generate unique admission ID
            existing_count = PatientAdmission.objects.filter(admission_date__date=today).count()
            self.admission_id = f"ADM{today.strftime('%Y%m%d')}{existing_count + 1:03d}"
        super().save(*args, **kwargs)

    def __str__(self):
        return f"{self.admission_id} - {self.patient.full_name} with Dr. {self.doctor.user.get_full_name()}"
