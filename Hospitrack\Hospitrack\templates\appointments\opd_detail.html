{% extends 'base.html' %}

{% block title %}OPD Registration Details - HospiTrack{% endblock %}

{% block content %}
<div class="space-y-6">
    <!-- Header -->
    <div class="bg-white shadow rounded-lg p-6">
        <div class="flex justify-between items-center">
            <div>
                <h1 class="text-3xl font-bold text-gray-900">OPD Registration Details</h1>
                <p class="text-gray-600 mt-2">{{ registration.opd_number }} - {{ registration.patient_name }}</p>
            </div>
            <div class="flex space-x-3">
                <a href="{% url 'appointments:opd_queue' %}" class="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
                    <i class="fas fa-arrow-left mr-2"></i>
                    Back to Queue
                </a>
                <a href="{% url 'appointments:opd_schedule' %}" class="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
                    <i class="fas fa-calendar mr-2"></i>
                    OPD Management
                </a>
            </div>
        </div>
    </div>

    <!-- Registration Information -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <!-- Patient Information -->
        <div class="bg-white shadow rounded-lg p-6">
            <h2 class="text-lg font-medium text-gray-900 mb-4">Patient Information</h2>
            <dl class="space-y-3">
                <div>
                    <dt class="text-sm font-medium text-gray-500">OPD Number</dt>
                    <dd class="text-sm text-gray-900 font-mono">{{ registration.opd_number }}</dd>
                </div>
                <div>
                    <dt class="text-sm font-medium text-gray-500">Patient Name</dt>
                    <dd class="text-sm text-gray-900">{{ registration.patient_name }}</dd>
                </div>
                <div class="grid grid-cols-2 gap-4">
                    <div>
                        <dt class="text-sm font-medium text-gray-500">Age</dt>
                        <dd class="text-sm text-gray-900">{{ registration.age }} years</dd>
                    </div>
                    <div>
                        <dt class="text-sm font-medium text-gray-500">Gender</dt>
                        <dd class="text-sm text-gray-900">{{ registration.get_gender_display }}</dd>
                    </div>
                </div>
                <div>
                    <dt class="text-sm font-medium text-gray-500">Phone Number</dt>
                    <dd class="text-sm text-gray-900">{{ registration.phone }}</dd>
                </div>
                <div>
                    <dt class="text-sm font-medium text-gray-500">Address</dt>
                    <dd class="text-sm text-gray-900">{{ registration.address }}</dd>
                </div>
            </dl>
        </div>

        <!-- Appointment Information -->
        <div class="bg-white shadow rounded-lg p-6">
            <h2 class="text-lg font-medium text-gray-900 mb-4">Appointment Information</h2>
            <dl class="space-y-3">
                <div>
                    <dt class="text-sm font-medium text-gray-500">Doctor</dt>
                    <dd class="text-sm text-gray-900">Dr. {{ registration.doctor.user.get_full_name }}</dd>
                </div>
                <div>
                    <dt class="text-sm font-medium text-gray-500">Specialization</dt>
                    <dd class="text-sm text-gray-900">{{ registration.doctor.specialization }}</dd>
                </div>
                <div class="grid grid-cols-2 gap-4">
                    <div>
                        <dt class="text-sm font-medium text-gray-500">Registration Date</dt>
                        <dd class="text-sm text-gray-900">{{ registration.registration_date|date:"F d, Y" }}</dd>
                    </div>
                    <div>
                        <dt class="text-sm font-medium text-gray-500">Registration Time</dt>
                        <dd class="text-sm text-gray-900">{{ registration.registration_time|time:"g:i A" }}</dd>
                    </div>
                </div>
                <div class="grid grid-cols-2 gap-4">
                    <div>
                        <dt class="text-sm font-medium text-gray-500">Preferred Time</dt>
                        <dd class="text-sm text-gray-900">{{ registration.get_preferred_time_display }}</dd>
                    </div>
                    <div>
                        <dt class="text-sm font-medium text-gray-500">Queue Number</dt>
                        <dd class="text-sm text-gray-900">#{{ registration.queue_number }}</dd>
                    </div>
                </div>
                <div>
                    <dt class="text-sm font-medium text-gray-500">Visit Type</dt>
                    <dd class="text-sm text-gray-900">{{ registration.get_visit_type_display }}</dd>
                </div>
                <div>
                    <dt class="text-sm font-medium text-gray-500">Status</dt>
                    <dd class="text-sm">
                        <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full 
                            {% if registration.status == 'registered' %}bg-blue-100 text-blue-800
                            {% elif registration.status == 'waiting' %}bg-yellow-100 text-yellow-800
                            {% elif registration.status == 'consulting' %}bg-green-100 text-green-800
                            {% elif registration.status == 'completed' %}bg-purple-100 text-purple-800
                            {% elif registration.status == 'cancelled' %}bg-red-100 text-red-800
                            {% else %}bg-gray-100 text-gray-800{% endif %}">
                            {{ registration.get_status_display }}
                        </span>
                    </dd>
                </div>
            </dl>
        </div>
    </div>

    <!-- Medical Information -->
    <div class="bg-white shadow rounded-lg p-6">
        <h2 class="text-lg font-medium text-gray-900 mb-4">Medical Information</h2>
        <dl class="space-y-3">
            <div>
                <dt class="text-sm font-medium text-gray-500">Chief Complaint</dt>
                <dd class="text-sm text-gray-900">{{ registration.chief_complaint }}</dd>
            </div>
            {% if registration.symptoms %}
            <div>
                <dt class="text-sm font-medium text-gray-500">Symptoms</dt>
                <dd class="text-sm text-gray-900">{{ registration.symptoms }}</dd>
            </div>
            {% endif %}
            {% if registration.special_instructions %}
            <div>
                <dt class="text-sm font-medium text-gray-500">Special Instructions</dt>
                <dd class="text-sm text-gray-900">{{ registration.special_instructions }}</dd>
            </div>
            {% endif %}
            <div class="grid grid-cols-2 gap-4">
                <div>
                    <dt class="text-sm font-medium text-gray-500">Emergency Case</dt>
                    <dd class="text-sm text-gray-900">
                        {% if registration.is_emergency %}
                        <span class="text-red-600 font-medium">Yes ⚡</span>
                        {% else %}
                        <span class="text-gray-600">No</span>
                        {% endif %}
                    </dd>
                </div>
                <div>
                    <dt class="text-sm font-medium text-gray-500">Consultation Fee</dt>
                    <dd class="text-sm text-gray-900">₹{{ registration.consultation_fee }}</dd>
                </div>
            </div>
        </dl>
    </div>

    <!-- Payment Information -->
    <div class="bg-white shadow rounded-lg p-6">
        <h2 class="text-lg font-medium text-gray-900 mb-4">Payment Information</h2>
        <dl class="space-y-3">
            <div class="grid grid-cols-2 gap-4">
                <div>
                    <dt class="text-sm font-medium text-gray-500">Payment Method</dt>
                    <dd class="text-sm text-gray-900">{{ registration.get_payment_method_display }}</dd>
                </div>
                <div>
                    <dt class="text-sm font-medium text-gray-500">Payment Status</dt>
                    <dd class="text-sm">
                        {% if registration.is_paid %}
                        <span class="text-green-600 font-medium">Paid ✓</span>
                        {% else %}
                        <span class="text-red-600 font-medium">Pending</span>
                        {% endif %}
                    </dd>
                </div>
            </div>
        </dl>
    </div>

    <!-- System Information -->
    <div class="bg-gray-50 shadow rounded-lg p-6">
        <h2 class="text-lg font-medium text-gray-900 mb-4">System Information</h2>
        <dl class="space-y-3">
            <div class="grid grid-cols-2 gap-4">
                <div>
                    <dt class="text-sm font-medium text-gray-500">Created At</dt>
                    <dd class="text-sm text-gray-900">{{ registration.created_at|date:"F d, Y g:i A" }}</dd>
                </div>
                <div>
                    <dt class="text-sm font-medium text-gray-500">Last Updated</dt>
                    <dd class="text-sm text-gray-900">{{ registration.updated_at|date:"F d, Y g:i A" }}</dd>
                </div>
            </div>
            {% if registration.created_by %}
            <div>
                <dt class="text-sm font-medium text-gray-500">Registered By</dt>
                <dd class="text-sm text-gray-900">{{ registration.created_by.get_full_name }} ({{ registration.created_by.username }})</dd>
            </div>
            {% endif %}
        </dl>
    </div>

    <!-- Action Buttons -->
    <div class="bg-white shadow rounded-lg p-6">
        <div class="flex justify-between items-center">
            <div>
                <h2 class="text-lg font-medium text-gray-900">Actions</h2>
                <p class="text-sm text-gray-600">Manage this OPD registration</p>
            </div>
            <div class="flex space-x-3">
                <a href="{% url 'appointments:opd_queue' %}" class="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
                    <i class="fas fa-list mr-2"></i>
                    Back to Queue
                </a>
                {% if registration.status != 'cancelled' and registration.status != 'completed' and user.user_type == 'admin' %}
                <a href="{% url 'appointments:opd_edit' registration.id %}" class="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
                    <i class="fas fa-edit mr-2"></i>
                    Edit Registration
                </a>
                <a href="{% url 'appointments:opd_cancel' registration.id %}" class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-red-600 hover:bg-red-700">
                    <i class="fas fa-ban mr-2"></i>
                    Cancel Registration
                </a>
                {% endif %}
                <a href="{% url 'appointments:opd_registration' %}" class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-green-600 hover:bg-green-700">
                    <i class="fas fa-user-plus mr-2"></i>
                    Register New Patient
                </a>
            </div>
        </div>
    </div>
</div>
{% endblock %}
