# Specialty-Based Access Control System

## Overview
The HospiTrack system now implements a comprehensive specialty-based access control system that separates doctors and nurses by their medical specializations, ensuring that healthcare professionals can only access patients within their area of expertise.

## Key Features

### 1. Medical Specialty Management
- **Centralized Specialty Model**: All medical specialties are managed through the `MedicalSpecialty` model
- **13 Pre-configured Specialties**: Cardiology, Nephrology, Pulmonology, Hepatology, Orthopedics, Pediatrics, ENT, Neurosurgery, Neurology, Psychiatry, General Medicine, Emergency Medicine, Surgery
- **Admin Interface**: Full CRUD operations for managing specialties

### 2. Doctor Specialty Assignment
- **Automatic Assignment**: When creating a new doctor, they are automatically assigned to their qualification specialty
- **Specialty-Based Access**: Doctors can only view and manage patients in their specialty
- **Backward Compatibility**: Existing specialization field maintained for compatibility

### 3. Nurse Specialty Assignment
- **Qualification-Based Assignment**: Nurses are assigned to specialties based on their qualifications
- **Department Synchronization**: Department field automatically syncs with specialty
- **Shift Management**: Nurses maintain their shift schedules within their specialty

### 4. Patient Specialty Assignment
- **Primary Specialty**: Each patient is assigned to a primary medical specialty
- **Auto-Assignment**: When registering patients, they are automatically assigned to the specialty of the staff member creating them
- **Access Control**: Only staff in the same specialty can access the patient data

## Access Control Rules

### For Doctors:
- ✅ Can view patients in their specialty only
- ✅ Can update patients in their specialty only
- ❌ Cannot access patients from other specialties
- ✅ Can see other doctors in their specialty

### For Nurses:
- ✅ Can view patients in their specialty only
- ✅ Can update patients in their specialty only
- ❌ Cannot access patients from other specialties
- ✅ Can see other nurses in their specialty

### For Admins:
- ✅ Can view all patients regardless of specialty
- ✅ Can manage all doctors and nurses
- ✅ Can create new specialties
- ✅ Full system access

## Implementation Details

### Models Updated:
1. **MedicalSpecialty**: New model for managing specialties
2. **Doctor**: Added `specialty` foreign key field
3. **Nurse**: Added `specialty` foreign key field
4. **Patient**: Added `primary_specialty` foreign key field

### Views Updated:
1. **patient_list**: Filters patients by user's specialty
2. **patient_detail**: Checks specialty-based access
3. **patient_register**: Auto-assigns specialty based on creator
4. **doctor_create**: Auto-assigns specialty based on qualification
5. **nurse_create**: Auto-assigns specialty based on qualification

### New Features:
1. **Nurse Management**: Complete CRUD for nurses with specialty assignment
2. **Specialty Dashboard**: Admin can view staff and patients by specialty
3. **Access Control Methods**: Patient model includes `can_user_access()` method

## Database Migrations
- **accounts.0002**: Adds MedicalSpecialty model and specialty fields
- **patients.0003**: Adds primary_specialty field to Patient model

## Management Commands

### 1. Setup Specialty System
```bash
python manage.py setup_specialty_system
```
- Creates all medical specialties
- Updates existing doctors with specialties
- Updates existing nurses with specialties

### 2. Demo Specialty Access
```bash
python manage.py demo_specialty_access
```
- Shows complete system overview
- Demonstrates access control rules
- Lists staff and patients by specialty

### 3. Assign Patient Specialties
```bash
python manage.py assign_patient_specialties
```
- Assigns existing patients to specialties
- Shows patient distribution

## URL Structure
- `/nurses/` - List all nurses (admin only)
- `/nurses/create/` - Create new nurse (admin only)
- `/doctors/create/` - Create new doctor (admin only)
- `/patients/` - List patients (filtered by specialty)
- `/patients/<id>/` - Patient detail (specialty-based access)

## Templates Added
- `accounts/nurse_create.html` - Nurse creation form
- `accounts/nurses_list.html` - Nurses listing page
- Updated admin dashboard with specialty management links

## Example Workflow

### 1. Creating a Cardiologist:
1. Admin creates doctor with "Cardiology" specialty
2. Doctor is automatically assigned to Cardiology specialty
3. Doctor can only see Cardiology patients

### 2. Creating a Cardiology Nurse:
1. Admin creates nurse with "Cardiology" specialty
2. Nurse is automatically assigned to Cardiology specialty
3. Nurse can only see Cardiology patients

### 3. Patient Registration:
1. Cardiology nurse registers a new patient
2. Patient is automatically assigned to Cardiology specialty
3. Only Cardiology staff can access this patient

### 4. Access Control in Action:
- Cardiology doctor logs in → sees only Cardiology patients
- Nephrology nurse logs in → sees only Nephrology patients
- Admin logs in → sees all patients

## Benefits

1. **Data Security**: Ensures medical data is only accessible to relevant specialists
2. **Workflow Efficiency**: Staff see only relevant patients for their specialty
3. **Compliance**: Helps meet healthcare data privacy requirements
4. **Scalability**: Easy to add new specialties and staff
5. **Automatic Assignment**: Reduces manual work and errors

## Current System Status

### Specialties with Staff:
- **Cardiology**: 1 doctor, 2 nurses
- **Emergency Medicine**: 2 nurses
- **General Medicine**: 3 nurses
- **Hepatology**: 1 doctor
- **Nephrology**: 1 doctor
- **Neurology**: 1 doctor
- **Neurosurgery**: 1 doctor
- **Orthopedics**: 1 doctor, 1 nurse
- **Pediatrics**: 1 doctor, 1 nurse
- **Psychiatry**: 1 doctor
- **Pulmonology**: 1 doctor
- **ENT**: 1 doctor
- **Surgery**: 1 nurse

### Patient Distribution:
- **Hepatology**: 4 patients
- **ENT**: 1 patient
- **Emergency Medicine**: 1 patient
- **Nephrology**: 1 patient
- **Pediatrics**: 1 patient
- **Psychiatry**: 1 patient
- **Pulmonology**: 1 patient

## Future Enhancements

1. **Multi-Specialty Doctors**: Support for doctors with multiple specialties
2. **Specialty Transfers**: Allow transferring patients between specialties
3. **Specialty Reports**: Generate reports by specialty
4. **Specialty Scheduling**: Schedule appointments within specialties
5. **Specialty Notifications**: Notify relevant staff about specialty-specific events

---

The specialty-based access control system is now fully implemented and operational, providing secure, efficient, and organized healthcare data management based on medical specializations.