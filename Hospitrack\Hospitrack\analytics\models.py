from django.db import models
from django.contrib.auth import get_user_model
from django.utils import timezone
from datetime import datetime, timedelta
import json

User = get_user_model()

class AnalyticsMetric(models.Model):
    """Base model for storing various analytics metrics"""
    METRIC_TYPE_CHOICES = [
        ('patient_flow', 'Patient Flow'),
        ('bed_utilization', 'Bed Utilization'),
        ('staff_efficiency', 'Staff Efficiency'),
        ('financial', 'Financial'),
        ('quality', 'Quality Metrics'),
        ('operational', 'Operational'),
        ('predictive', 'Predictive'),
    ]
    
    FREQUENCY_CHOICES = [
        ('hourly', 'Hourly'),
        ('daily', 'Daily'),
        ('weekly', 'Weekly'),
        ('monthly', 'Monthly'),
        ('quarterly', 'Quarterly'),
        ('yearly', 'Yearly'),
    ]
    
    name = models.CharField(max_length=100)
    metric_type = models.CharField(max_length=20, choices=METRIC_TYPE_CHOICES)
    value = models.DecimalField(max_digits=15, decimal_places=4)
    unit = models.CharField(max_length=50, blank=True, null=True)
    frequency = models.CharField(max_length=20, choices=FREQUENCY_CHOICES)
    date_recorded = models.DateTimeField(default=timezone.now)
    metadata = models.JSONField(default=dict, blank=True)  # Store additional context
    department = models.CharField(max_length=100, blank=True, null=True)
    specialty = models.ForeignKey('accounts.MedicalSpecialty', on_delete=models.SET_NULL, null=True, blank=True)
    
    class Meta:
        ordering = ['-date_recorded']
        indexes = [
            models.Index(fields=['metric_type', 'date_recorded']),
            models.Index(fields=['name', 'date_recorded']),
        ]
    
    def __str__(self):
        return f"{self.name}: {self.value} {self.unit or ''} ({self.date_recorded.date()})"

class PatientFlowMetrics(models.Model):
    """Track patient flow and admission patterns"""
    date = models.DateField()
    hour = models.IntegerField(null=True, blank=True)  # For hourly tracking
    
    # Admission metrics
    total_admissions = models.IntegerField(default=0)
    emergency_admissions = models.IntegerField(default=0)
    planned_admissions = models.IntegerField(default=0)
    
    # Discharge metrics
    total_discharges = models.IntegerField(default=0)
    average_length_of_stay = models.DecimalField(max_digits=5, decimal_places=2, default=0)
    
    # Occupancy metrics
    total_occupied_beds = models.IntegerField(default=0)
    occupancy_rate = models.DecimalField(max_digits=5, decimal_places=2, default=0)  # Percentage
    
    # Wait times
    average_wait_time_minutes = models.IntegerField(default=0)
    
    # Department-specific data
    department = models.CharField(max_length=100, blank=True, null=True)
    specialty = models.ForeignKey('accounts.MedicalSpecialty', on_delete=models.SET_NULL, null=True, blank=True)
    
    created_at = models.DateTimeField(auto_now_add=True)
    
    class Meta:
        unique_together = ['date', 'hour', 'department']
        ordering = ['-date', '-hour']
    
    def __str__(self):
        time_str = f"{self.hour}:00" if self.hour is not None else "Daily"
        return f"Patient Flow - {self.date} {time_str} ({self.department or 'All'})"

class StaffEfficiencyMetrics(models.Model):
    """Track staff performance and efficiency metrics"""
    date = models.DateField()
    staff_member = models.ForeignKey(User, on_delete=models.CASCADE, related_name='efficiency_metrics')
    
    # Productivity metrics
    patients_seen = models.IntegerField(default=0)
    appointments_completed = models.IntegerField(default=0)
    average_consultation_time = models.IntegerField(default=0)  # in minutes
    
    # Quality metrics
    patient_satisfaction_score = models.DecimalField(max_digits=3, decimal_places=2, null=True, blank=True)
    no_show_rate = models.DecimalField(max_digits=5, decimal_places=2, default=0)
    
    # Workload metrics
    scheduled_hours = models.DecimalField(max_digits=4, decimal_places=2, default=8)
    actual_hours = models.DecimalField(max_digits=4, decimal_places=2, default=0)
    overtime_hours = models.DecimalField(max_digits=4, decimal_places=2, default=0)
    
    # Financial metrics
    revenue_generated = models.DecimalField(max_digits=10, decimal_places=2, default=0)
    
    created_at = models.DateTimeField(auto_now_add=True)
    
    class Meta:
        unique_together = ['date', 'staff_member']
        ordering = ['-date']
    
    def __str__(self):
        return f"{self.staff_member.get_full_name()} - {self.date}"

class BedUtilizationMetrics(models.Model):
    """Track bed utilization and turnover metrics"""
    date = models.DateField()
    ward = models.ForeignKey('beds.Ward', on_delete=models.CASCADE, related_name='utilization_metrics')
    
    # Utilization metrics
    total_beds = models.IntegerField()
    occupied_beds = models.IntegerField(default=0)
    available_beds = models.IntegerField(default=0)
    maintenance_beds = models.IntegerField(default=0)
    
    # Efficiency metrics
    utilization_rate = models.DecimalField(max_digits=5, decimal_places=2, default=0)  # Percentage
    turnover_rate = models.DecimalField(max_digits=5, decimal_places=2, default=0)
    average_length_of_stay = models.DecimalField(max_digits=5, decimal_places=2, default=0)
    
    # Revenue metrics
    revenue_per_bed = models.DecimalField(max_digits=10, decimal_places=2, default=0)
    total_revenue = models.DecimalField(max_digits=12, decimal_places=2, default=0)
    
    created_at = models.DateTimeField(auto_now_add=True)
    
    class Meta:
        unique_together = ['date', 'ward']
        ordering = ['-date']
    
    def __str__(self):
        return f"{self.ward.name} - {self.date} ({self.utilization_rate}% utilized)"

class FinancialMetrics(models.Model):
    """Track financial performance and revenue metrics"""
    date = models.DateField()
    
    # Revenue streams
    consultation_revenue = models.DecimalField(max_digits=12, decimal_places=2, default=0)
    bed_revenue = models.DecimalField(max_digits=12, decimal_places=2, default=0)
    procedure_revenue = models.DecimalField(max_digits=12, decimal_places=2, default=0)
    pharmacy_revenue = models.DecimalField(max_digits=12, decimal_places=2, default=0)
    other_revenue = models.DecimalField(max_digits=12, decimal_places=2, default=0)
    
    # Costs
    staff_costs = models.DecimalField(max_digits=12, decimal_places=2, default=0)
    operational_costs = models.DecimalField(max_digits=12, decimal_places=2, default=0)
    equipment_costs = models.DecimalField(max_digits=12, decimal_places=2, default=0)
    
    # Calculated fields
    total_revenue = models.DecimalField(max_digits=12, decimal_places=2, default=0)
    total_costs = models.DecimalField(max_digits=12, decimal_places=2, default=0)
    net_profit = models.DecimalField(max_digits=12, decimal_places=2, default=0)
    profit_margin = models.DecimalField(max_digits=5, decimal_places=2, default=0)
    
    # Department/Specialty breakdown
    department = models.CharField(max_length=100, blank=True, null=True)
    specialty = models.ForeignKey('accounts.MedicalSpecialty', on_delete=models.SET_NULL, null=True, blank=True)
    
    created_at = models.DateTimeField(auto_now_add=True)
    
    def save(self, *args, **kwargs):
        # Auto-calculate totals
        self.total_revenue = (self.consultation_revenue + self.bed_revenue + 
                            self.procedure_revenue + self.pharmacy_revenue + self.other_revenue)
        self.total_costs = self.staff_costs + self.operational_costs + self.equipment_costs
        self.net_profit = self.total_revenue - self.total_costs
        if self.total_revenue > 0:
            self.profit_margin = (self.net_profit / self.total_revenue) * 100
        super().save(*args, **kwargs)
    
    class Meta:
        unique_together = ['date', 'department', 'specialty']
        ordering = ['-date']
    
    def __str__(self):
        return f"Financial Metrics - {self.date} ({self.department or 'All'})"

class QualityMetrics(models.Model):
    """Track quality of care and patient satisfaction metrics"""
    date = models.DateField()
    
    # Patient satisfaction
    average_satisfaction_score = models.DecimalField(max_digits=3, decimal_places=2, default=0)
    satisfaction_responses = models.IntegerField(default=0)
    
    # Clinical quality
    readmission_rate = models.DecimalField(max_digits=5, decimal_places=2, default=0)
    infection_rate = models.DecimalField(max_digits=5, decimal_places=2, default=0)
    mortality_rate = models.DecimalField(max_digits=5, decimal_places=2, default=0)
    
    # Process quality
    average_wait_time = models.IntegerField(default=0)  # minutes
    appointment_punctuality = models.DecimalField(max_digits=5, decimal_places=2, default=0)  # percentage
    
    # Safety metrics
    incident_count = models.IntegerField(default=0)
    near_miss_count = models.IntegerField(default=0)
    
    department = models.CharField(max_length=100, blank=True, null=True)
    specialty = models.ForeignKey('accounts.MedicalSpecialty', on_delete=models.SET_NULL, null=True, blank=True)
    
    created_at = models.DateTimeField(auto_now_add=True)
    
    class Meta:
        unique_together = ['date', 'department', 'specialty']
        ordering = ['-date']
    
    def __str__(self):
        return f"Quality Metrics - {self.date} ({self.department or 'All'})"

class PredictiveModel(models.Model):
    """Store predictive model results and forecasts"""
    MODEL_TYPE_CHOICES = [
        ('patient_flow', 'Patient Flow Prediction'),
        ('bed_demand', 'Bed Demand Forecast'),
        ('staff_scheduling', 'Staff Scheduling Optimization'),
        ('resource_planning', 'Resource Planning'),
        ('risk_assessment', 'Risk Assessment'),
    ]
    
    name = models.CharField(max_length=100)
    model_type = models.CharField(max_length=20, choices=MODEL_TYPE_CHOICES)
    prediction_date = models.DateField()
    target_date = models.DateField()  # Date being predicted for
    
    # Prediction results
    predicted_value = models.DecimalField(max_digits=15, decimal_places=4)
    confidence_score = models.DecimalField(max_digits=5, decimal_places=4, default=0)
    
    # Model metadata
    model_version = models.CharField(max_length=50)
    input_features = models.JSONField(default=dict)
    model_parameters = models.JSONField(default=dict)
    
    # Validation
    actual_value = models.DecimalField(max_digits=15, decimal_places=4, null=True, blank=True)
    accuracy_score = models.DecimalField(max_digits=5, decimal_places=4, null=True, blank=True)
    
    created_at = models.DateTimeField(auto_now_add=True)
    
    class Meta:
        ordering = ['-prediction_date', '-target_date']
    
    def __str__(self):
        return f"{self.name} - Predicting {self.target_date} (Confidence: {self.confidence_score})"

class AlertRule(models.Model):
    """Define rules for automated alerts and notifications"""
    ALERT_TYPE_CHOICES = [
        ('threshold', 'Threshold Alert'),
        ('trend', 'Trend Alert'),
        ('anomaly', 'Anomaly Detection'),
        ('prediction', 'Prediction Alert'),
    ]
    
    SEVERITY_CHOICES = [
        ('low', 'Low'),
        ('medium', 'Medium'),
        ('high', 'High'),
        ('critical', 'Critical'),
    ]
    
    name = models.CharField(max_length=100)
    description = models.TextField()
    alert_type = models.CharField(max_length=20, choices=ALERT_TYPE_CHOICES)
    severity = models.CharField(max_length=10, choices=SEVERITY_CHOICES)
    
    # Rule conditions
    metric_name = models.CharField(max_length=100)
    threshold_value = models.DecimalField(max_digits=15, decimal_places=4, null=True, blank=True)
    condition = models.CharField(max_length=10, choices=[
        ('>', 'Greater than'),
        ('<', 'Less than'),
        ('>=', 'Greater than or equal'),
        ('<=', 'Less than or equal'),
        ('==', 'Equal to'),
        ('!=', 'Not equal to'),
    ])
    
    # Notification settings
    notify_users = models.ManyToManyField(User, blank=True)
    email_enabled = models.BooleanField(default=True)
    sms_enabled = models.BooleanField(default=False)
    
    is_active = models.BooleanField(default=True)
    created_at = models.DateTimeField(auto_now_add=True)
    
    def __str__(self):
        return f"{self.name} ({self.severity})"

class Alert(models.Model):
    """Store triggered alerts"""
    rule = models.ForeignKey(AlertRule, on_delete=models.CASCADE, related_name='alerts')
    triggered_at = models.DateTimeField(auto_now_add=True)
    metric_value = models.DecimalField(max_digits=15, decimal_places=4)
    message = models.TextField()
    
    # Status tracking
    is_acknowledged = models.BooleanField(default=False)
    acknowledged_by = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, blank=True)
    acknowledged_at = models.DateTimeField(null=True, blank=True)
    
    # Resolution tracking
    is_resolved = models.BooleanField(default=False)
    resolved_by = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, blank=True, related_name='resolved_alerts')
    resolved_at = models.DateTimeField(null=True, blank=True)
    resolution_notes = models.TextField(blank=True, null=True)
    
    class Meta:
        ordering = ['-triggered_at']
    
    def __str__(self):
        return f"{self.rule.name} - {self.triggered_at.strftime('%Y-%m-%d %H:%M')}"
