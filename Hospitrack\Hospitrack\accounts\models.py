from django.db import models
from django.contrib.auth.models import AbstractUser

class User(AbstractUser):
    USER_TYPE_CHOICES = (
        ('admin', 'Admin'),
        ('doctor', 'Doctor'),
        ('nurse', 'Nurse'),
    )

    user_type = models.CharField(max_length=10, choices=USER_TYPE_CHOICES)
    phone = models.CharField(max_length=15, blank=True, null=True)
    address = models.TextField(blank=True, null=True)
    date_of_birth = models.DateField(blank=True, null=True)
    profile_picture = models.ImageField(upload_to='profile_pics/', blank=True, null=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return f"{self.username} ({self.user_type})"

# Medical Specialties - Centralized list for consistency
class MedicalSpecialty(models.Model):
    """Centralized medical specialties for doctors and nurses"""
    name = models.Char<PERSON>ield(max_length=100, unique=True)
    description = models.TextField(blank=True, null=True)
    is_active = models.Bo<PERSON>anField(default=True)
    created_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        verbose_name_plural = "Medical Specialties"
        ordering = ['name']

    def __str__(self):
        return self.name

class Doctor(models.Model):
    user = models.OneToOneField(User, on_delete=models.CASCADE, primary_key=True)
    specialty = models.ForeignKey(MedicalSpecialty, on_delete=models.CASCADE, related_name='doctors', null=True, blank=True)
    specialization = models.CharField(max_length=100)  # Keep for backward compatibility
    license_number = models.CharField(max_length=50, unique=True)
    department = models.CharField(max_length=100)
    consultation_fee = models.DecimalField(max_digits=10, decimal_places=2, default=0.00)
    available_from = models.TimeField(null=True, blank=True)
    available_to = models.TimeField(null=True, blank=True)
    is_available = models.BooleanField(default=True)
    experience_years = models.PositiveIntegerField(default=0)
    qualifications = models.TextField(blank=True, null=True)

    def save(self, *args, **kwargs):
        # Auto-sync specialization with specialty for backward compatibility
        if self.specialty:
            self.specialization = self.specialty.name
        super().save(*args, **kwargs)

    def get_specialty_nurses(self):
        """Get all nurses working in the same specialty"""
        if self.specialty:
            return self.specialty.nurses.all().select_related('user')
        return Nurse.objects.none()

    def get_available_nurses(self):
        """Get available nurses in the same specialty"""
        if self.specialty:
            return self.specialty.nurses.filter(leave_status='available').select_related('user')
        return Nurse.objects.none()

    def get_nurses_on_leave(self):
        """Get nurses on leave in the same specialty"""
        if self.specialty:
            return self.specialty.nurses.exclude(leave_status='available').select_related('user')
        return Nurse.objects.none()

    def get_nurses_by_shift(self, shift=None):
        """Get nurses by shift in the same specialty"""
        nurses = self.get_specialty_nurses()
        if shift:
            nurses = nurses.filter(shift=shift)
        return nurses

    def __str__(self):
        return f"Dr. {self.user.first_name} {self.user.last_name} - {self.specialty.name if self.specialty else self.specialization}"

class Nurse(models.Model):
    LEAVE_STATUS_CHOICES = [
        ('available', 'Available'),
        ('on_leave', 'On Leave'),
        ('sick_leave', 'Sick Leave'),
        ('vacation', 'Vacation'),
        ('emergency_leave', 'Emergency Leave'),
    ]
    
    user = models.OneToOneField(User, on_delete=models.CASCADE, primary_key=True)
    specialty = models.ForeignKey(MedicalSpecialty, on_delete=models.CASCADE, related_name='nurses', null=True, blank=True)
    department = models.CharField(max_length=100)  # Keep for backward compatibility
    shift = models.CharField(max_length=20, choices=[
        ('morning', 'Morning (6 AM - 2 PM)'),
        ('evening', 'Evening (2 PM - 10 PM)'),
        ('night', 'Night (10 PM - 6 AM)'),
    ])
    license_number = models.CharField(max_length=50, unique=True)
    leave_status = models.CharField(max_length=20, choices=LEAVE_STATUS_CHOICES, default='available')
    leave_start_date = models.DateField(null=True, blank=True)
    leave_end_date = models.DateField(null=True, blank=True)
    leave_reason = models.TextField(blank=True, null=True)
    experience_years = models.PositiveIntegerField(default=0)
    qualifications = models.TextField(blank=True, null=True)

    def save(self, *args, **kwargs):
        # Auto-sync department with specialty for backward compatibility
        if self.specialty:
            self.department = self.specialty.name
        
        # Auto-update leave status based on dates
        from datetime import date
        today = date.today()
        if self.leave_start_date and self.leave_end_date:
            if self.leave_start_date <= today <= self.leave_end_date:
                if self.leave_status == 'available':
                    self.leave_status = 'on_leave'
            elif today > self.leave_end_date:
                if self.leave_status != 'available':
                    self.leave_status = 'available'
                    self.leave_start_date = None
                    self.leave_end_date = None
                    self.leave_reason = None
        
        super().save(*args, **kwargs)

    @property
    def is_available(self):
        """Check if nurse is currently available (not on leave)"""
        return self.leave_status == 'available'

    @property
    def is_on_leave(self):
        """Check if nurse is currently on any type of leave"""
        return self.leave_status != 'available'

    @property
    def leave_status_display(self):
        """Get human-readable leave status"""
        return dict(self.LEAVE_STATUS_CHOICES).get(self.leave_status, self.leave_status)

    @property
    def current_leave_info(self):
        """Get current leave information"""
        if self.is_on_leave and self.leave_start_date and self.leave_end_date:
            from datetime import date
            today = date.today()
            days_remaining = (self.leave_end_date - today).days
            return {
                'status': self.leave_status_display,
                'start_date': self.leave_start_date,
                'end_date': self.leave_end_date,
                'days_remaining': max(0, days_remaining),
                'reason': self.leave_reason
            }
        return None

    def __str__(self):
        status = " (On Leave)" if self.is_on_leave else ""
        return f"Nurse {self.user.first_name} {self.user.last_name} - {self.specialty.name if self.specialty else self.department}{status}"

class Admin(models.Model):
    user = models.OneToOneField(User, on_delete=models.CASCADE, primary_key=True)
    employee_id = models.CharField(max_length=20, unique=True)
    department = models.CharField(max_length=100, default='Administration')

    def __str__(self):
        return f"Admin {self.user.first_name} {self.user.last_name}"
