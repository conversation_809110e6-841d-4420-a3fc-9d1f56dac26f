{% extends 'base.html' %}

{% block title %}{{ patient.full_name }} - Patient Details{% endblock %}

{% block content %}
<div class="space-y-6">
    <!-- Header -->
    <div class="bg-white shadow rounded-lg p-6">
        <div class="flex justify-between items-start">
            <div>
                <h1 class="text-3xl font-bold text-gray-900">{{ patient.full_name }}</h1>
                <p class="text-gray-600 mt-2">Patient ID: {{ patient.patient_id }}</p>
                <div class="mt-2 flex items-center space-x-4 text-sm text-gray-500">
                    <span>{{ patient.age }} years old</span>
                    <span>{{ patient.get_gender_display }}</span>
                    <span>Blood Group: {{ patient.blood_group }}</span>
                </div>
            </div>
            <div class="flex space-x-3">
                <a href="{% url 'patients:list' %}" class="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
                    <i class="fas fa-arrow-left mr-2"></i>
                    Back to List
                </a>
                {% if user.user_type == 'admin' %}
                <a href="{% url 'patients:admission_direct' patient.id %}" class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-orange-600 hover:bg-orange-700">
                    <i class="fas fa-bed mr-2"></i>
                    Admit Patient
                </a>
                {% endif %}
                {% if user.user_type == 'admin' or user.user_type == 'doctor' %}
                <a href="{% url 'patients:edit' patient.id %}" class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700">
                    <i class="fas fa-edit mr-2"></i>
                    Edit Patient
                </a>
                {% endif %}
            </div>
        </div>
    </div>

    <!-- Patient Information Grid -->
    <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <!-- Personal Information -->
        <div class="bg-white shadow rounded-lg p-6">
            <h2 class="text-lg font-medium text-gray-900 mb-4">Personal Information</h2>
            <dl class="space-y-3">
                <div>
                    <dt class="text-sm font-medium text-gray-500">Full Name</dt>
                    <dd class="text-sm text-gray-900">{{ patient.full_name }}</dd>
                </div>
                <div>
                    <dt class="text-sm font-medium text-gray-500">Date of Birth</dt>
                    <dd class="text-sm text-gray-900">{{ patient.date_of_birth|date:"F d, Y" }}</dd>
                </div>
                <div>
                    <dt class="text-sm font-medium text-gray-500">Age</dt>
                    <dd class="text-sm text-gray-900">{{ patient.age }} years</dd>
                </div>
                <div>
                    <dt class="text-sm font-medium text-gray-500">Gender</dt>
                    <dd class="text-sm text-gray-900">{{ patient.get_gender_display }}</dd>
                </div>
                <div>
                    <dt class="text-sm font-medium text-gray-500">Blood Group</dt>
                    <dd class="text-sm text-gray-900">
                        <span class="px-2 py-1 text-xs font-semibold rounded-full bg-red-100 text-red-800">
                            {{ patient.blood_group }}
                        </span>
                    </dd>
                </div>
            </dl>
        </div>

        <!-- Contact Information -->
        <div class="bg-white shadow rounded-lg p-6">
            <h2 class="text-lg font-medium text-gray-900 mb-4">Contact Information</h2>
            <dl class="space-y-3">
                <div>
                    <dt class="text-sm font-medium text-gray-500">Phone</dt>
                    <dd class="text-sm text-gray-900">{{ patient.phone }}</dd>
                </div>
                {% if patient.email %}
                <div>
                    <dt class="text-sm font-medium text-gray-500">Email</dt>
                    <dd class="text-sm text-gray-900">{{ patient.email }}</dd>
                </div>
                {% endif %}
                <div>
                    <dt class="text-sm font-medium text-gray-500">Address</dt>
                    <dd class="text-sm text-gray-900">{{ patient.address }}</dd>
                </div>
            </dl>
        </div>

        <!-- Emergency Contact -->
        <div class="bg-white shadow rounded-lg p-6">
            <h2 class="text-lg font-medium text-gray-900 mb-4">Emergency Contact</h2>
            <dl class="space-y-3">
                <div>
                    <dt class="text-sm font-medium text-gray-500">Name</dt>
                    <dd class="text-sm text-gray-900">{{ patient.emergency_contact_name }}</dd>
                </div>
                <div>
                    <dt class="text-sm font-medium text-gray-500">Phone</dt>
                    <dd class="text-sm text-gray-900">{{ patient.emergency_contact_phone }}</dd>
                </div>
                <div>
                    <dt class="text-sm font-medium text-gray-500">Relationship</dt>
                    <dd class="text-sm text-gray-900">{{ patient.emergency_contact_relation }}</dd>
                </div>
            </dl>
        </div>
    </div>

    <!-- Medical Information -->
    <div class="bg-white shadow rounded-lg p-6">
        <h2 class="text-lg font-medium text-gray-900 mb-4">Medical Information</h2>
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            {% if patient.medical_history %}
            <div>
                <h3 class="text-sm font-medium text-gray-500 mb-2">Medical History</h3>
                <p class="text-sm text-gray-900 bg-gray-50 p-3 rounded-md">{{ patient.medical_history }}</p>
            </div>
            {% endif %}
            
            {% if patient.allergies %}
            <div>
                <h3 class="text-sm font-medium text-gray-500 mb-2">Allergies</h3>
                <p class="text-sm text-gray-900 bg-red-50 p-3 rounded-md border border-red-200">{{ patient.allergies }}</p>
            </div>
            {% endif %}
            
            {% if patient.current_medications %}
            <div>
                <h3 class="text-sm font-medium text-gray-500 mb-2">Current Medications</h3>
                <p class="text-sm text-gray-900 bg-blue-50 p-3 rounded-md border border-blue-200">{{ patient.current_medications }}</p>
            </div>
            {% endif %}
            
            {% if patient.insurance_number %}
            <div>
                <h3 class="text-sm font-medium text-gray-500 mb-2">Insurance Number</h3>
                <p class="text-sm text-gray-900">{{ patient.insurance_number }}</p>
            </div>
            {% endif %}
        </div>
    </div>

    <!-- Quick Actions -->
    <div class="bg-white shadow rounded-lg p-6">
        <h2 class="text-lg font-medium text-gray-900 mb-4">Quick Actions</h2>
        <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
            {% if user.user_type == 'nurse' or user.user_type == 'doctor' %}
            <a href="{% url 'patients:update' patient.id %}" class="inline-flex items-center justify-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-green-600 hover:bg-green-700">
                <i class="fas fa-notes-medical mr-2"></i>
                Add Update
            </a>
            {% endif %}
            
            {% if user.user_type == 'admin' %}
            <a href="{% url 'appointments:schedule' %}?patient={{ patient.id }}" class="inline-flex items-center justify-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700">
                <i class="fas fa-calendar-plus mr-2"></i>
                Schedule Appointment
            </a>
            
            <a href="{% url 'beds:assign' %}?patient={{ patient.id }}" class="inline-flex items-center justify-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-purple-600 hover:bg-purple-700">
                <i class="fas fa-bed mr-2"></i>
                Assign Bed
            </a>
            
            <a href="{% url 'patients:discharge' patient.id %}" class="inline-flex items-center justify-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-orange-600 hover:bg-orange-700">
                <i class="fas fa-sign-out-alt mr-2"></i>
                Discharge Patient
            </a>
            {% endif %}
        </div>
    </div>

    <!-- Patient Updates -->
    {% if updates %}
    <div class="bg-white shadow rounded-lg p-6">
        <h2 class="text-lg font-medium text-gray-900 mb-4">Recent Updates</h2>
        <div class="space-y-4">
            {% for update in updates %}
            <div class="border-l-4 border-blue-400 pl-4 py-2">
                <div class="flex justify-between items-start">
                    <div class="flex-1">
                        <p class="text-sm text-gray-900">{{ update.update_text }}</p>
                        {% if update.vital_signs %}
                        <p class="text-xs text-gray-600 mt-1">
                            <strong>Vitals:</strong> {{ update.vital_signs }}
                        </p>
                        {% endif %}
                        {% if update.medications_given %}
                        <p class="text-xs text-gray-600 mt-1">
                            <strong>Medications:</strong> {{ update.medications_given }}
                        </p>
                        {% endif %}
                        <div class="text-xs text-gray-500 mt-2">
                            By {{ update.updated_by.get_full_name }} on {{ update.created_at|date:"M d, Y H:i" }}
                        </div>
                    </div>
                </div>
            </div>
            {% endfor %}
        </div>
        <div class="mt-4">
            <a href="{% url 'patients:updates_list' %}?patient={{ patient.id }}" class="text-blue-600 hover:text-blue-900 text-sm font-medium">
                View all updates →
            </a>
        </div>
    </div>
    {% endif %}

    <!-- Registration Information -->
    <div class="bg-white shadow rounded-lg p-6">
        <h2 class="text-lg font-medium text-gray-900 mb-4">Registration Information</h2>
        <dl class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
                <dt class="text-sm font-medium text-gray-500">Registered On</dt>
                <dd class="text-sm text-gray-900">{{ patient.created_at|date:"F d, Y H:i" }}</dd>
            </div>
            {% if patient.created_by %}
            <div>
                <dt class="text-sm font-medium text-gray-500">Registered By</dt>
                <dd class="text-sm text-gray-900">{{ patient.created_by.get_full_name }}</dd>
            </div>
            {% endif %}
            <div>
                <dt class="text-sm font-medium text-gray-500">Last Updated</dt>
                <dd class="text-sm text-gray-900">{{ patient.updated_at|date:"F d, Y H:i" }}</dd>
            </div>
            <div>
                <dt class="text-sm font-medium text-gray-500">Status</dt>
                <dd class="text-sm text-gray-900">
                    <span class="px-2 py-1 text-xs font-semibold rounded-full {% if patient.is_active %}bg-green-100 text-green-800{% else %}bg-red-100 text-red-800{% endif %}">
                        {% if patient.is_active %}Active{% else %}Inactive{% endif %}
                    </span>
                </dd>
            </div>
        </dl>
    </div>
</div>
{% endblock %}
