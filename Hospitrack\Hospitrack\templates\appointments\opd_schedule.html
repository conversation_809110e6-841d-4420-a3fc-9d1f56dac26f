{% extends 'base.html' %}

{% block title %}OPD Schedule - HospiTrack{% endblock %}

{% block content %}
<div class="space-y-6">
    <!-- Header -->
    <div class="bg-white shadow rounded-lg p-6">
        <div class="flex justify-between items-center">
            <div>
                <h1 class="text-3xl font-bold text-gray-900">OPD Schedule</h1>
                <p class="text-gray-600 mt-2">Outpatient Department scheduling and management</p>
            </div>
            <div class="flex space-x-3">
                <a href="{% url 'appointments:list' %}" class="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
                    <i class="fas fa-arrow-left mr-2"></i>
                    Back to Appointments
                </a>
                <a href="{% url 'appointments:opd_registration' %}" class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-green-600 hover:bg-green-700">
                    <i class="fas fa-user-plus mr-2"></i>
                    OPD Registration
                </a>
                <a href="{% url 'appointments:opd_queue' %}" class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-orange-600 hover:bg-orange-700">
                    <i class="fas fa-list mr-2"></i>
                    View OPD Queue
                </a>
                {% if user.user_type == 'admin' %}
                <a href="{% url 'appointments:opd_schedule_create' %}" class="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
                    <i class="fas fa-calendar-plus mr-2"></i>
                    Admin Schedule
                </a>
                {% endif %}
            </div>
        </div>
    </div>

    <!-- OPD Statistics -->
    <div class="grid grid-cols-1 md:grid-cols-4 gap-6">
        <div class="bg-white overflow-hidden shadow rounded-lg">
            <div class="p-5">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <i class="fas fa-clinic-medical text-3xl text-blue-500"></i>
                    </div>
                    <div class="ml-5 w-0 flex-1">
                        <dl>
                            <dt class="text-sm font-medium text-gray-500 truncate">Today's OPD</dt>
                            <dd class="text-3xl font-bold text-gray-900">{{ today_opd.count }}</dd>
                        </dl>
                    </div>
                </div>
            </div>
        </div>

        <div class="bg-white overflow-hidden shadow rounded-lg">
            <div class="p-5">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <i class="fas fa-user-clock text-3xl text-yellow-500"></i>
                    </div>
                    <div class="ml-5 w-0 flex-1">
                        <dl>
                            <dt class="text-sm font-medium text-gray-500 truncate">Waiting</dt>
                            <dd class="text-3xl font-bold text-yellow-600">{{ waiting_patients.count }}</dd>
                        </dl>
                    </div>
                </div>
            </div>
        </div>

        <div class="bg-white overflow-hidden shadow rounded-lg">
            <div class="p-5">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <i class="fas fa-user-check text-3xl text-green-500"></i>
                    </div>
                    <div class="ml-5 w-0 flex-1">
                        <dl>
                            <dt class="text-sm font-medium text-gray-500 truncate">Consulted</dt>
                            <dd class="text-3xl font-bold text-green-600">{{ consulted_patients.count }}</dd>
                        </dl>
                    </div>
                </div>
            </div>
        </div>

        <div class="bg-white overflow-hidden shadow rounded-lg">
            <div class="p-5">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <i class="fas fa-stethoscope text-3xl text-purple-500"></i>
                    </div>
                    <div class="ml-5 w-0 flex-1">
                        <dl>
                            <dt class="text-sm font-medium text-gray-500 truncate">OPD Doctors</dt>
                            <dd class="text-3xl font-bold text-purple-600">{{ opd_doctors.count }}</dd>
                        </dl>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Department-wise OPD Schedule -->
    <div class="bg-white shadow rounded-lg p-6">
        <h2 class="text-lg font-medium text-gray-900 mb-6">Department-wise OPD Schedule</h2>
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {% for department in departments %}
            <div class="border rounded-lg p-4">
                <div class="flex justify-between items-center mb-4">
                    <h3 class="text-md font-semibold text-gray-900">{{ department.name }}</h3>
                    <span class="px-2 py-1 text-xs font-semibold rounded-full bg-blue-100 text-blue-800">
                        {{ department.today_appointments }} patients
                    </span>
                </div>
                
                <div class="space-y-3">
                    {% for doctor in department.doctors %}
                    <div class="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                        <div class="flex items-center">
                            <div class="w-8 h-8 bg-green-100 rounded-full flex items-center justify-center mr-3">
                                <i class="fas fa-user-md text-green-600 text-sm"></i>
                            </div>
                            <div>
                                <div class="font-medium text-sm">Dr. {{ doctor.user.get_full_name }}</div>
                                <div class="text-xs text-gray-500">{{ doctor.specialization|default:"General Medicine" }}</div>
                            </div>
                        </div>
                        <div class="text-right">
                            <div class="text-sm font-medium text-gray-900">9:00 AM - 5:00 PM</div>
                            <div class="text-xs text-gray-500">{{ doctor.today_patients|default:0 }} patients</div>
                        </div>
                    </div>
                    {% empty %}
                    <div class="text-center py-4 text-gray-500">
                        <i class="fas fa-user-md text-2xl mb-2"></i>
                        <p class="text-sm">No doctors assigned</p>
                    </div>
                    {% endfor %}
                </div>
            </div>
            {% empty %}
            <div class="col-span-full text-center py-8 text-gray-500">
                <i class="fas fa-hospital text-4xl mb-4"></i>
                <p>No departments found</p>
            </div>
            {% endfor %}
        </div>
    </div>

    <!-- Current Queue -->
    <div class="bg-white shadow rounded-lg p-6">
        <h2 class="text-lg font-medium text-gray-900 mb-6">Current OPD Queue</h2>
        
        {% if today_opd %}
        <div class="overflow-x-auto">
            <table class="min-w-full divide-y divide-gray-200">
                <thead class="bg-gray-50">
                    <tr>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Token No.</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Patient</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Doctor</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Department</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Time</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                    </tr>
                </thead>
                <tbody class="bg-white divide-y divide-gray-200">
                    {% for appointment in today_opd %}
                    <tr class="hover:bg-gray-50">
                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                            #{{ appointment.id|stringformat:"03d" }}
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            <div class="flex items-center">
                                <div class="flex-shrink-0 h-8 w-8">
                                    <div class="h-8 w-8 rounded-full bg-gray-200 flex items-center justify-center">
                                        <i class="fas fa-user text-gray-500 text-xs"></i>
                                    </div>
                                </div>
                                <div class="ml-3">
                                    <div class="text-sm font-medium text-gray-900">{{ appointment.patient.get_full_name }}</div>
                                    <div class="text-sm text-gray-500">{{ appointment.patient.patient_id }}</div>
                                </div>
                            </div>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                            {% if appointment.doctor %}
                                Dr. {{ appointment.doctor.user.get_full_name }}
                            {% else %}
                                Not Assigned
                            {% endif %}
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                            {{ appointment.department|default:"General" }}
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                            {{ appointment.appointment_time|time:"g:i A" }}
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full 
                                {% if appointment.status == 'scheduled' %}bg-yellow-100 text-yellow-800
                                {% elif appointment.status == 'completed' %}bg-green-100 text-green-800
                                {% elif appointment.status == 'cancelled' %}bg-red-100 text-red-800
                                {% else %}bg-gray-100 text-gray-800{% endif %}">
                                {{ appointment.get_status_display }}
                            </span>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                            <div class="flex space-x-2">
                                {% if appointment.status == 'scheduled' %}
                                <button class="text-green-600 hover:text-green-900 text-xs">Start Consultation</button>
                                {% endif %}
                                <a href="{% url 'appointments:detail' appointment.id %}" class="text-blue-600 hover:text-blue-900 text-xs">View</a>
                            </div>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
        {% else %}
        <div class="text-center py-12">
            <i class="fas fa-calendar-times text-4xl text-gray-400 mb-4"></i>
            <h3 class="text-lg font-medium text-gray-900 mb-2">No OPD appointments today</h3>
            <p class="text-gray-500 mb-4">Schedule new appointments to get started.</p>
            {% if user.user_type == 'admin' or user.user_type == 'doctor' %}
            <a href="{% url 'appointments:create' %}" class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700">
                <i class="fas fa-plus mr-2"></i>
                Schedule OPD Appointment
            </a>
            {% endif %}
        </div>
        {% endif %}
    </div>

    <!-- OPD Timing Information -->
    <div class="bg-white shadow rounded-lg p-6">
        <h2 class="text-lg font-medium text-gray-900 mb-4">OPD Timings</h2>
        <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div class="text-center p-4 bg-blue-50 rounded-lg">
                <i class="fas fa-sun text-2xl text-blue-600 mb-2"></i>
                <h3 class="font-medium text-gray-900">Morning OPD</h3>
                <p class="text-sm text-gray-600">9:00 AM - 12:00 PM</p>
                <p class="text-xs text-gray-500 mt-1">General Consultation</p>
            </div>
            
            <div class="text-center p-4 bg-orange-50 rounded-lg">
                <i class="fas fa-sun text-2xl text-orange-600 mb-2"></i>
                <h3 class="font-medium text-gray-900">Afternoon OPD</h3>
                <p class="text-sm text-gray-600">1:00 PM - 5:00 PM</p>
                <p class="text-xs text-gray-500 mt-1">Specialist Consultation</p>
            </div>
            
            <div class="text-center p-4 bg-purple-50 rounded-lg">
                <i class="fas fa-moon text-2xl text-purple-600 mb-2"></i>
                <h3 class="font-medium text-gray-900">Emergency OPD</h3>
                <p class="text-sm text-gray-600">24/7 Available</p>
                <p class="text-xs text-gray-500 mt-1">Emergency Cases Only</p>
            </div>
        </div>
    </div>
</div>
{% endblock %}
