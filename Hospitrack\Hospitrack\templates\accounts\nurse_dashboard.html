{% extends 'base.html' %}

{% block title %}Nurse Dashboard - HospiTrack{% endblock %}

{% block content %}
<div class="space-y-6">
    <!-- Header -->
    <div class="bg-white shadow rounded-lg p-6">
        <h1 class="text-3xl font-bold text-gray-900">Nurse Dashboard</h1>
        <p class="text-gray-600 mt-2">Welcome back, {{ nurse.user.first_name }} {{ nurse.user.last_name }}!</p>
        <div class="mt-4 grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
            <div><strong>Department:</strong> {{ nurse.department }}</div>
            <div><strong>Shift:</strong> {{ nurse.get_shift_display }}</div>
            <div><strong>License:</strong> {{ nurse.license_number }}</div>
        </div>
    </div>

    <!-- Bed Management Overview -->
    <div class="bg-white shadow rounded-lg p-6">
        <h2 class="text-xl font-bold text-gray-900 mb-4">{{ nurse.department }} - Bed Status</h2>
        <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div class="text-center p-4 bg-green-50 rounded-lg">
                <div class="text-3xl font-bold text-green-600">{{ available_beds }}</div>
                <div class="text-sm text-green-800">Available Beds</div>
            </div>
            <div class="text-center p-4 bg-red-50 rounded-lg">
                <div class="text-3xl font-bold text-red-600">{{ occupied_beds }}</div>
                <div class="text-sm text-red-800">Occupied Beds</div>
            </div>
            <div class="text-center p-4 bg-yellow-50 rounded-lg">
                <div class="text-3xl font-bold text-yellow-600">{{ maintenance_beds }}</div>
                <div class="text-sm text-yellow-800">Under Maintenance</div>
            </div>
        </div>
        <div class="mt-4">
            <a href="{% url 'beds:management' %}" class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700">
                <i class="fas fa-bed mr-2"></i>
                Manage Beds
            </a>
        </div>
    </div>

    <!-- Current Patients in Department -->
    {% if current_patients %}
    <div class="bg-white shadow rounded-lg p-6">
        <h2 class="text-xl font-bold text-gray-900 mb-4">Current Patients in {{ nurse.department }}</h2>
        <div class="overflow-x-auto">
            <table class="min-w-full divide-y divide-gray-200">
                <thead class="bg-gray-50">
                    <tr>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Bed</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Patient</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Age</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Admitted</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                    </tr>
                </thead>
                <tbody class="bg-white divide-y divide-gray-200">
                    {% for assignment in current_patients %}
                    <tr>
                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                            {{ assignment.bed.ward.name }} - {{ assignment.bed.bed_number }}
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                            {{ assignment.patient.full_name }}
                            <div class="text-xs text-gray-500">{{ assignment.patient.patient_id }}</div>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                            {{ assignment.patient.age }} years
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                            {{ assignment.assigned_date|date:"M d, Y" }}
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                            <a href="{% url 'patients:detail' assignment.patient.id %}" class="text-blue-600 hover:text-blue-900 mr-3">View</a>
                            <a href="{% url 'patients:update' assignment.patient.id %}" class="text-green-600 hover:text-green-900">Update</a>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
    </div>
    {% else %}
    <div class="bg-white shadow rounded-lg p-6">
        <h2 class="text-xl font-bold text-gray-900 mb-4">Current Patients in {{ nurse.department }}</h2>
        <div class="text-center py-8">
            <i class="fas fa-bed text-4xl text-gray-400 mb-4"></i>
            <p class="text-gray-500">No patients currently assigned to beds in this department.</p>
        </div>
    </div>
    {% endif %}

    <!-- Quick Actions -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        <!-- Patient Updates -->
        <div class="bg-white shadow rounded-lg p-6">
            <h3 class="text-lg font-medium text-gray-900 mb-4">Patient Care</h3>
            <div class="space-y-3">
                <a href="{% url 'patients:updates_list' %}" class="block w-full bg-blue-600 hover:bg-blue-700 text-white text-center py-2 px-4 rounded-md text-sm font-medium">
                    <i class="fas fa-notes-medical mr-2"></i>View Patient Updates
                </a>
                <a href="{% url 'patients:list' %}" class="block w-full bg-green-600 hover:bg-green-700 text-white text-center py-2 px-4 rounded-md text-sm font-medium">
                    <i class="fas fa-users mr-2"></i>All Patients
                </a>
            </div>
        </div>

        <!-- Bed Management -->
        <div class="bg-white shadow rounded-lg p-6">
            <h3 class="text-lg font-medium text-gray-900 mb-4">Bed Management</h3>
            <div class="space-y-3">
                <a href="{% url 'beds:management' %}" class="block w-full bg-purple-600 hover:bg-purple-700 text-white text-center py-2 px-4 rounded-md text-sm font-medium">
                    <i class="fas fa-bed mr-2"></i>Bed Status
                </a>
                <a href="{% url 'beds:assign' %}" class="block w-full bg-orange-600 hover:bg-orange-700 text-white text-center py-2 px-4 rounded-md text-sm font-medium">
                    <i class="fas fa-plus mr-2"></i>Assign Bed
                </a>
            </div>
        </div>

        <!-- Emergency Actions -->
        <div class="bg-white shadow rounded-lg p-6">
            <h3 class="text-lg font-medium text-gray-900 mb-4">Emergency</h3>
            <div class="space-y-3">
                <button class="block w-full bg-red-600 hover:bg-red-700 text-white text-center py-2 px-4 rounded-md text-sm font-medium">
                    <i class="fas fa-exclamation-triangle mr-2"></i>Emergency Alert
                </button>
                <a href="{% url 'beds:management' %}" class="block w-full bg-yellow-600 hover:bg-yellow-700 text-white text-center py-2 px-4 rounded-md text-sm font-medium">
                    <i class="fas fa-tools mr-2"></i>Maintenance Request
                </a>
            </div>
        </div>
    </div>

    <!-- Recent Patient Updates -->
    {% if recent_updates %}
    <div class="bg-white shadow rounded-lg p-6">
        <h2 class="text-xl font-bold text-gray-900 mb-4">My Recent Patient Updates</h2>
        <div class="space-y-4">
            {% for update in recent_updates %}
            <div class="border-l-4 border-green-400 pl-4">
                <div class="flex justify-between items-start">
                    <div>
                        <h4 class="font-medium text-gray-900">{{ update.patient.full_name }}</h4>
                        <p class="text-sm text-gray-600 mt-1">{{ update.update_text|truncatewords:20 }}</p>
                        {% if update.vital_signs %}
                        <p class="text-xs text-gray-500 mt-1">Vitals: {{ update.vital_signs }}</p>
                        {% endif %}
                    </div>
                    <div class="text-right text-sm text-gray-500">
                        {{ update.created_at|date:"M d, H:i" }}
                    </div>
                </div>
            </div>
            {% endfor %}
        </div>
        <div class="mt-4">
            <a href="{% url 'patients:updates_list' %}" class="text-blue-600 hover:text-blue-900 text-sm font-medium">
                View all updates →
            </a>
        </div>
    </div>
    {% endif %}

    <!-- Shift Information -->
    <div class="bg-white shadow rounded-lg p-6">
        <h2 class="text-xl font-bold text-gray-900 mb-4">Shift Information</h2>
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
                <h3 class="font-medium text-gray-900 mb-2">Current Shift</h3>
                <p class="text-gray-600">{{ nurse.get_shift_display }}</p>
                {% if nurse.shift == 'morning' %}
                <p class="text-sm text-gray-500">6:00 AM - 2:00 PM</p>
                {% elif nurse.shift == 'evening' %}
                <p class="text-sm text-gray-500">2:00 PM - 10:00 PM</p>
                {% else %}
                <p class="text-sm text-gray-500">10:00 PM - 6:00 AM</p>
                {% endif %}
            </div>
            <div>
                <h3 class="font-medium text-gray-900 mb-2">Department</h3>
                <p class="text-gray-600">{{ nurse.department }}</p>
                <p class="text-sm text-gray-500">License: {{ nurse.license_number }}</p>
            </div>
        </div>
    </div>
</div>

<!-- Add Patient Update Modal (if needed) -->
<div id="updateModal" class="hidden fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
    <div class="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
        <div class="mt-3">
            <h3 class="text-lg font-medium text-gray-900 mb-4">Quick Patient Update</h3>
            <form id="quickUpdateForm">
                <div class="mb-4">
                    <label class="block text-sm font-medium text-gray-700 mb-2">Patient</label>
                    <select class="w-full border border-gray-300 rounded-md px-3 py-2">
                        <option>Select Patient...</option>
                        {% for assignment in current_patients %}
                        <option value="{{ assignment.patient.id }}">{{ assignment.patient.full_name }} - {{ assignment.bed.ward.name }} {{ assignment.bed.bed_number }}</option>
                        {% endfor %}
                    </select>
                </div>
                <div class="mb-4">
                    <label class="block text-sm font-medium text-gray-700 mb-2">Update</label>
                    <textarea class="w-full border border-gray-300 rounded-md px-3 py-2" rows="3" placeholder="Enter patient update..."></textarea>
                </div>
                <div class="flex justify-end space-x-3">
                    <button type="button" onclick="closeModal()" class="px-4 py-2 bg-gray-300 text-gray-700 rounded-md hover:bg-gray-400">Cancel</button>
                    <button type="submit" class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700">Save Update</button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
function openModal() {
    document.getElementById('updateModal').classList.remove('hidden');
}

function closeModal() {
    document.getElementById('updateModal').classList.add('hidden');
}
</script>
{% endblock %}
