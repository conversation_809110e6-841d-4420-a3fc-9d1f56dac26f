#!/usr/bin/env python
"""
Test script to verify login functionality and user authentication
"""
import os
import sys
import django
from django.test import Client
from django.contrib.auth import authenticate

# Setup Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'hospitrack.settings')
django.setup()

from accounts.models import User, Doctor, Nurse, Admin

def test_login_functionality():
    """Test login functionality for all user types"""
    print("🔍 Testing HospiTrack Login Functionality")
    print("=" * 50)
    
    # Test credentials from LOGIN_CREDENTIALS.md
    test_credentials = [
        # Admin
        {'username': 'admin', 'password': 'admin123', 'expected_type': 'admin'},
        
        # New specialist doctors
        {'username': 'dr.emergency', 'password': 'doctor123', 'expected_type': 'doctor'},
        {'username': 'dr.oncology', 'password': 'doctor123', 'expected_type': 'doctor'},
        {'username': 'dr.anesthesiology', 'password': 'doctor123', 'expected_type': 'doctor'},
        {'username': 'dr.dermatology', 'password': 'doctor123', 'expected_type': 'doctor'},
        {'username': 'dr.radiology', 'password': 'doctor123', 'expected_type': 'doctor'},
        
        # Sample nurses from each department
        {'username': 'emma.johnson1', 'password': 'nurse123', 'expected_type': 'nurse'},
        {'username': 'amelia.jackson11', 'password': 'nurse123', 'expected_type': 'nurse'},
        {'username': 'elizabeth.lewis21', 'password': 'nurse123', 'expected_type': 'nurse'},
        {'username': 'madison.hill31', 'password': 'nurse123', 'expected_type': 'nurse'},
        {'username': 'layla.roberts41', 'password': 'nurse123', 'expected_type': 'nurse'},
    ]
    
    client = Client()
    success_count = 0
    total_tests = len(test_credentials)
    
    for i, cred in enumerate(test_credentials, 1):
        print(f"\n{i}. Testing {cred['username']} ({cred['expected_type']})")
        
        try:
            # Test authentication
            user = authenticate(username=cred['username'], password=cred['password'])
            
            if user is not None:
                if user.user_type == cred['expected_type']:
                    print(f"   ✅ Authentication successful")
                    print(f"   👤 User: {user.get_full_name()}")
                    print(f"   🏥 Type: {user.user_type}")
                    
                    # Test login via client
                    response = client.post('/login/', {
                        'username': cred['username'],
                        'password': cred['password']
                    })
                    
                    if response.status_code == 302:  # Redirect after successful login
                        print(f"   🌐 Web login successful (redirected)")
                        success_count += 1
                    else:
                        print(f"   ❌ Web login failed (status: {response.status_code})")
                        
                    # Logout for next test
                    client.logout()
                    
                else:
                    print(f"   ❌ User type mismatch: expected {cred['expected_type']}, got {user.user_type}")
            else:
                print(f"   ❌ Authentication failed - Invalid credentials")
                
        except Exception as e:
            print(f"   ❌ Error during test: {str(e)}")
    
    print(f"\n" + "=" * 50)
    print(f"📊 Test Results: {success_count}/{total_tests} successful")
    print(f"✅ Success Rate: {(success_count/total_tests)*100:.1f}%")
    
    return success_count == total_tests

def test_user_profiles():
    """Test user profile completeness"""
    print(f"\n🔍 Testing User Profile Completeness")
    print("=" * 50)
    
    # Test doctors
    doctors = Doctor.objects.all()
    print(f"\n👨‍⚕️ Doctors ({doctors.count()}):")
    for doctor in doctors:
        specialty_info = f" - {doctor.specialty.name}" if doctor.specialty else " - No specialty"
        print(f"   • Dr. {doctor.user.get_full_name()}{specialty_info}")
    
    # Test nurses
    nurses = Nurse.objects.all()
    print(f"\n👩‍⚕️ Nurses ({nurses.count()}):")
    specialty_counts = {}
    for nurse in nurses:
        specialty_name = nurse.specialty.name if nurse.specialty else "No specialty"
        specialty_counts[specialty_name] = specialty_counts.get(specialty_name, 0) + 1
    
    for specialty, count in specialty_counts.items():
        print(f"   • {specialty}: {count} nurses")
    
    # Test admins
    admins = Admin.objects.all()
    print(f"\n👑 Admins ({admins.count()}):")
    for admin in admins:
        print(f"   • {admin.user.get_full_name()}")

def test_dashboard_access():
    """Test dashboard access for different user types"""
    print(f"\n🔍 Testing Dashboard Access")
    print("=" * 50)
    
    client = Client()
    
    # Test admin dashboard
    print("\n1. Testing Admin Dashboard Access:")
    client.login(username='admin', password='admin123')
    response = client.get('/admin-dashboard/')
    print(f"   Status: {response.status_code} {'✅' if response.status_code == 200 else '❌'}")
    client.logout()
    
    # Test doctor dashboard
    print("\n2. Testing Doctor Dashboard Access:")
    client.login(username='dr.emergency', password='doctor123')
    response = client.get('/doctor-dashboard/')
    print(f"   Status: {response.status_code} {'✅' if response.status_code == 200 else '❌'}")
    client.logout()
    
    # Test nurse dashboard
    print("\n3. Testing Nurse Dashboard Access:")
    client.login(username='emma.johnson1', password='nurse123')
    response = client.get('/nurse-dashboard/')
    print(f"   Status: {response.status_code} {'✅' if response.status_code == 200 else '❌'}")
    client.logout()

if __name__ == '__main__':
    try:
        # Run all tests
        login_success = test_login_functionality()
        test_user_profiles()
        test_dashboard_access()
        
        print(f"\n🎯 Overall Test Status: {'✅ PASSED' if login_success else '❌ FAILED'}")
        
    except Exception as e:
        print(f"❌ Test execution failed: {str(e)}")
        sys.exit(1)