#!/usr/bin/env python
"""
Frontend functionality and template rendering test
"""
import os
import sys
import django
from django.test import Client
from django.template.loader import render_to_string

# Setup Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'hospitrack.settings')
django.setup()

from accounts.models import <PERSON>r, Doctor, Nurse, <PERSON><PERSON>

def test_template_rendering():
    """Test template rendering and context data"""
    print("🎨 Testing Template Rendering and Frontend")
    print("=" * 60)
    
    client = Client()
    results = {'passed': 0, 'failed': 0, 'errors': []}
    
    # Test admin dashboard template rendering
    print(f"\n📊 Testing Admin Dashboard Template")
    try:
        client.login(username='admin', password='admin123')
        response = client.get('/admin-dashboard/')
        
        if response.status_code == 200:
            content = response.content.decode()
            
            # Check for essential elements
            checks = [
                ('HospiTrack', 'Page title'),
                ('Total Patients', 'Patient statistics'),
                ('Total Doctors', 'Doctor statistics'),
                ('Total Nurses', 'Nurse statistics'),
                ('Available Beds', 'Bed statistics'),
                ('Welcome', 'User greeting'),
                ('Logout', 'Logout button')
            ]
            
            for check_text, description in checks:
                if check_text in content:
                    print(f"   ✅ {description} found")
                    results['passed'] += 1
                else:
                    print(f"   ❌ {description} missing")
                    results['failed'] += 1
                    results['errors'].append(f"Admin dashboard missing: {description}")
        else:
            print(f"   ❌ Admin dashboard failed to load: {response.status_code}")
            results['failed'] += 1
            results['errors'].append(f"Admin dashboard load failed: {response.status_code}")
            
    except Exception as e:
        print(f"   ❌ Admin dashboard test exception: {str(e)}")
        results['failed'] += 1
        results['errors'].append(f"Admin dashboard exception: {str(e)}")
    
    client.logout()
    
    # Test doctor dashboard template rendering
    print(f"\n👨‍⚕️ Testing Doctor Dashboard Template")
    try:
        client.login(username='dr.emergency', password='doctor123')
        response = client.get('/doctor-dashboard/')
        
        if response.status_code == 200:
            content = response.content.decode()
            
            # Check for essential elements
            checks = [
                ('Emergency Medicine', 'Doctor specialty'),
                ('Today\'s Appointments', 'Appointment section'),
                ('Specialty Nurses', 'Nurse team section'),
                ('Available Nurses', 'Available nurses'),
                ('Dr. Alexander Mitchell', 'Doctor name')
            ]
            
            for check_text, description in checks:
                if check_text in content:
                    print(f"   ✅ {description} found")
                    results['passed'] += 1
                else:
                    print(f"   ❌ {description} missing")
                    results['failed'] += 1
                    results['errors'].append(f"Doctor dashboard missing: {description}")
        else:
            print(f"   ❌ Doctor dashboard failed to load: {response.status_code}")
            results['failed'] += 1
            results['errors'].append(f"Doctor dashboard load failed: {response.status_code}")
            
    except Exception as e:
        print(f"   ❌ Doctor dashboard test exception: {str(e)}")
        results['failed'] += 1
        results['errors'].append(f"Doctor dashboard exception: {str(e)}")
    
    client.logout()
    
    # Test nurse dashboard template rendering
    print(f"\n👩‍⚕️ Testing Nurse Dashboard Template")
    try:
        client.login(username='emma.johnson1', password='nurse123')
        response = client.get('/nurse-dashboard/')
        
        if response.status_code == 200:
            content = response.content.decode()
            
            # Check for essential elements
            checks = [
                ('Emma Johnson', 'Nurse name'),
                ('Emergency Medicine', 'Nurse specialty'),
                ('Bed Statistics', 'Bed management section'),
                ('Patient Updates', 'Patient care section'),
                ('morning', 'Shift information')
            ]
            
            for check_text, description in checks:
                if check_text in content:
                    print(f"   ✅ {description} found")
                    results['passed'] += 1
                else:
                    print(f"   ❌ {description} missing")
                    results['failed'] += 1
                    results['errors'].append(f"Nurse dashboard missing: {description}")
        else:
            print(f"   ❌ Nurse dashboard failed to load: {response.status_code}")
            results['failed'] += 1
            results['errors'].append(f"Nurse dashboard load failed: {response.status_code}")
            
    except Exception as e:
        print(f"   ❌ Nurse dashboard test exception: {str(e)}")
        results['failed'] += 1
        results['errors'].append(f"Nurse dashboard exception: {str(e)}")
    
    return results

def test_navigation_and_links():
    """Test navigation and internal links"""
    print(f"\n🧭 Testing Navigation and Links")
    print("=" * 60)
    
    client = Client()
    results = {'passed': 0, 'failed': 0, 'errors': []}
    
    # Test admin navigation
    print(f"\n👑 Testing Admin Navigation")
    try:
        client.login(username='admin', password='admin123')
        response = client.get('/admin-dashboard/')
        
        if response.status_code == 200:
            content = response.content.decode()
            
            # Check for navigation links
            nav_links = [
                'Dashboard',
                'Patients',
                'OPD Management',
                'Bed Management',
                'Nurses'
            ]
            
            for link in nav_links:
                if link in content:
                    print(f"   ✅ Navigation link '{link}' found")
                    results['passed'] += 1
                else:
                    print(f"   ❌ Navigation link '{link}' missing")
                    results['failed'] += 1
                    results['errors'].append(f"Admin navigation missing: {link}")
        
    except Exception as e:
        print(f"   ❌ Admin navigation test exception: {str(e)}")
        results['failed'] += 1
        results['errors'].append(f"Admin navigation exception: {str(e)}")
    
    client.logout()
    
    # Test doctor navigation
    print(f"\n👨‍⚕️ Testing Doctor Navigation")
    try:
        client.login(username='dr.emergency', password='doctor123')
        response = client.get('/doctor-dashboard/')
        
        if response.status_code == 200:
            content = response.content.decode()
            
            # Check for doctor-specific navigation
            nav_links = [
                'Dashboard',
                'Today\'s OPD Queue',
                'Patients'
            ]
            
            for link in nav_links:
                if link in content:
                    print(f"   ✅ Doctor navigation link '{link}' found")
                    results['passed'] += 1
                else:
                    print(f"   ❌ Doctor navigation link '{link}' missing")
                    results['failed'] += 1
                    results['errors'].append(f"Doctor navigation missing: {link}")
        
    except Exception as e:
        print(f"   ❌ Doctor navigation test exception: {str(e)}")
        results['failed'] += 1
        results['errors'].append(f"Doctor navigation exception: {str(e)}")
    
    return results

def test_responsive_elements():
    """Test responsive design elements"""
    print(f"\n📱 Testing Responsive Design Elements")
    print("=" * 60)
    
    client = Client()
    results = {'passed': 0, 'failed': 0, 'errors': []}
    
    try:
        client.login(username='admin', password='admin123')
        response = client.get('/admin-dashboard/')
        
        if response.status_code == 200:
            content = response.content.decode()
            
            # Check for responsive classes (Tailwind CSS)
            responsive_classes = [
                'grid',
                'md:',
                'lg:',
                'sm:',
                'max-w-',
                'flex',
                'container',
                'mx-auto'
            ]
            
            for css_class in responsive_classes:
                if css_class in content:
                    print(f"   ✅ Responsive class '{css_class}' found")
                    results['passed'] += 1
                else:
                    print(f"   ❌ Responsive class '{css_class}' missing")
                    results['failed'] += 1
                    results['errors'].append(f"Responsive design missing: {css_class}")
        
    except Exception as e:
        print(f"   ❌ Responsive design test exception: {str(e)}")
        results['failed'] += 1
        results['errors'].append(f"Responsive design exception: {str(e)}")
    
    return results

def test_form_validation():
    """Test form validation and error handling"""
    print(f"\n📝 Testing Form Validation")
    print("=" * 60)
    
    client = Client()
    results = {'passed': 0, 'failed': 0, 'errors': []}
    
    # Test invalid login
    print(f"\n🔐 Testing Login Form Validation")
    try:
        response = client.post('/login/', {
            'username': 'invalid_user',
            'password': 'invalid_password'
        })
        
        if response.status_code == 200:  # Should stay on login page
            content = response.content.decode()
            if 'Invalid username or password' in content:
                print(f"   ✅ Login error message displayed correctly")
                results['passed'] += 1
            else:
                print(f"   ❌ Login error message not found")
                results['failed'] += 1
                results['errors'].append("Login error message missing")
        else:
            print(f"   ❌ Login validation test failed: {response.status_code}")
            results['failed'] += 1
            results['errors'].append(f"Login validation failed: {response.status_code}")
            
    except Exception as e:
        print(f"   ❌ Login validation test exception: {str(e)}")
        results['failed'] += 1
        results['errors'].append(f"Login validation exception: {str(e)}")
    
    # Test patient registration with missing data
    print(f"\n🏥 Testing Patient Registration Validation")
    try:
        client.login(username='admin', password='admin123')
        response = client.post('/patients/register/', {
            'first_name': '',  # Missing required field
            'last_name': 'Test',
            'date_of_birth': '1990-01-01'
        })
        
        # Should either show validation error or redirect with error message
        if response.status_code in [200, 302]:
            print(f"   ✅ Patient registration validation handled")
            results['passed'] += 1
        else:
            print(f"   ❌ Patient registration validation failed: {response.status_code}")
            results['failed'] += 1
            results['errors'].append(f"Patient registration validation failed: {response.status_code}")
            
    except Exception as e:
        print(f"   ❌ Patient registration validation exception: {str(e)}")
        results['failed'] += 1
        results['errors'].append(f"Patient registration validation exception: {str(e)}")
    
    return results

def main():
    """Run all frontend tests"""
    print("🎨 Starting Frontend Functionality Testing")
    print("=" * 80)
    
    all_results = {'passed': 0, 'failed': 0, 'errors': []}
    
    # Run all test suites
    test_suites = [
        ("Template Rendering", test_template_rendering),
        ("Navigation and Links", test_navigation_and_links),
        ("Responsive Design", test_responsive_elements),
        ("Form Validation", test_form_validation)
    ]
    
    for suite_name, test_function in test_suites:
        print(f"\n🧪 Running {suite_name} Tests...")
        results = test_function()
        all_results['passed'] += results['passed']
        all_results['failed'] += results['failed']
        all_results['errors'].extend(results['errors'])
        
        print(f"   📊 {suite_name} Results: {results['passed']} passed, {results['failed']} failed")
    
    # Final summary
    print(f"\n" + "=" * 80)
    print(f"🎯 FRONTEND TEST RESULTS")
    print(f"=" * 80)
    print(f"✅ Total Passed: {all_results['passed']}")
    print(f"❌ Total Failed: {all_results['failed']}")
    print(f"📊 Success Rate: {(all_results['passed']/(all_results['passed']+all_results['failed']))*100:.1f}%")
    
    if all_results['errors']:
        print(f"\n🔍 Error Details:")
        for i, error in enumerate(all_results['errors'], 1):
            print(f"   {i}. {error}")
    
    overall_status = "✅ PASSED" if all_results['failed'] == 0 else "❌ FAILED"
    print(f"\n🎯 Overall Frontend Status: {overall_status}")
    
    return all_results['failed'] == 0

if __name__ == '__main__':
    try:
        success = main()
        sys.exit(0 if success else 1)
    except Exception as e:
        print(f"❌ Frontend test execution failed: {str(e)}")
        sys.exit(1)