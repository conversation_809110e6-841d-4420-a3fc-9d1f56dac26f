from django.core.management.base import BaseCommand
from django.utils import timezone
from datetime import datetime, timedelta, date
import random
from decimal import Decimal

from analytics.services import AnalyticsDataCollector
from analytics.models import (
    PatientFlowMetrics, BedUtilizationMetrics, FinancialMetrics,
    QualityMetrics, StaffEfficiencyMetrics
)
from accounts.models import <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>, Nurse
from beds.models import Ward

class Command(BaseCommand):
    help = 'Generate sample analytics data for the last 30 days'

    def add_arguments(self, parser):
        parser.add_argument(
            '--days',
            type=int,
            default=30,
            help='Number of days to generate data for (default: 30)'
        )
        parser.add_argument(
            '--real-data',
            action='store_true',
            help='Use real data collection instead of sample data'
        )

    def handle(self, *args, **options):
        days = options['days']
        use_real_data = options['real_data']
        
        self.stdout.write(f'🚀 Generating analytics data for the last {days} days...')
        
        if use_real_data:
            self.generate_real_data(days)
        else:
            self.generate_sample_data(days)
        
        self.stdout.write(
            self.style.SUCCESS(f'✅ Successfully generated analytics data for {days} days')
        )

    def generate_real_data(self, days):
        """Generate analytics data using real hospital data"""
        collector = AnalyticsDataCollector()
        
        end_date = date.today()
        start_date = end_date - timedelta(days=days)
        
        current_date = start_date
        while current_date <= end_date:
            self.stdout.write(f'📊 Collecting data for {current_date}...')
            collector.collect_daily_metrics(current_date)
            current_date += timedelta(days=1)

    def generate_sample_data(self, days):
        """Generate sample analytics data for demonstration"""
        end_date = date.today()
        start_date = end_date - timedelta(days=days)
        
        # Get specialties and wards
        specialties = list(MedicalSpecialty.objects.all())
        wards = list(Ward.objects.filter(is_active=True))
        doctors = list(Doctor.objects.all())
        
        current_date = start_date
        while current_date <= end_date:
            self.stdout.write(f'📊 Generating sample data for {current_date}...')
            
            # Generate patient flow metrics
            self.generate_patient_flow_data(current_date, specialties)
            
            # Generate bed utilization metrics
            self.generate_bed_utilization_data(current_date, wards)
            
            # Generate financial metrics
            self.generate_financial_data(current_date, specialties)
            
            # Generate quality metrics
            self.generate_quality_data(current_date, specialties)
            
            # Generate staff efficiency metrics
            self.generate_staff_efficiency_data(current_date, doctors)
            
            current_date += timedelta(days=1)

    def generate_patient_flow_data(self, target_date, specialties):
        """Generate sample patient flow data"""
        # Overall metrics
        base_admissions = random.randint(15, 35)
        emergency_ratio = random.uniform(0.3, 0.5)
        
        PatientFlowMetrics.objects.update_or_create(
            date=target_date,
            department=None,
            defaults={
                'total_admissions': base_admissions,
                'emergency_admissions': int(base_admissions * emergency_ratio),
                'planned_admissions': int(base_admissions * (1 - emergency_ratio)),
                'total_discharges': random.randint(10, 30),
                'average_length_of_stay': Decimal(str(random.uniform(3.5, 7.2))),
                'total_occupied_beds': random.randint(80, 120),
                'occupancy_rate': Decimal(str(random.uniform(75, 95))),
                'average_wait_time_minutes': random.randint(15, 45),
            }
        )
        
        # Specialty-specific metrics
        for specialty in specialties:
            specialty_admissions = random.randint(2, 8)
            PatientFlowMetrics.objects.update_or_create(
                date=target_date,
                specialty=specialty,
                defaults={
                    'total_admissions': specialty_admissions,
                    'emergency_admissions': random.randint(0, specialty_admissions),
                    'planned_admissions': random.randint(0, specialty_admissions),
                    'total_discharges': random.randint(1, 6),
                    'average_length_of_stay': Decimal(str(random.uniform(2, 10))),
                }
            )

    def generate_bed_utilization_data(self, target_date, wards):
        """Generate sample bed utilization data"""
        for ward in wards:
            total_beds = ward.capacity
            occupied = random.randint(int(total_beds * 0.6), int(total_beds * 0.95))
            available = total_beds - occupied
            
            BedUtilizationMetrics.objects.update_or_create(
                date=target_date,
                ward=ward,
                defaults={
                    'total_beds': total_beds,
                    'occupied_beds': occupied,
                    'available_beds': available,
                    'maintenance_beds': random.randint(0, 2),
                    'utilization_rate': Decimal(str((occupied / total_beds) * 100)),
                    'turnover_rate': Decimal(str(random.uniform(0.1, 0.3))),
                    'average_length_of_stay': Decimal(str(random.uniform(3, 8))),
                    'revenue_per_bed': Decimal(str(random.uniform(800, 2500))),
                    'total_revenue': Decimal(str(occupied * random.uniform(800, 2500))),
                }
            )

    def generate_financial_data(self, target_date, specialties):
        """Generate sample financial data"""
        # Overall financial metrics
        consultation_revenue = Decimal(str(random.uniform(15000, 35000)))
        bed_revenue = Decimal(str(random.uniform(25000, 45000)))
        procedure_revenue = consultation_revenue * Decimal('0.4')
        pharmacy_revenue = consultation_revenue * Decimal('0.3')
        other_revenue = consultation_revenue * Decimal('0.2')
        
        FinancialMetrics.objects.update_or_create(
            date=target_date,
            department=None,
            defaults={
                'consultation_revenue': consultation_revenue,
                'bed_revenue': bed_revenue,
                'procedure_revenue': procedure_revenue,
                'pharmacy_revenue': pharmacy_revenue,
                'other_revenue': other_revenue,
                'staff_costs': consultation_revenue * Decimal('0.45'),
                'operational_costs': consultation_revenue * Decimal('0.25'),
                'equipment_costs': consultation_revenue * Decimal('0.15'),
            }
        )
        
        # Specialty-specific financial metrics
        for specialty in specialties:
            spec_consultation = Decimal(str(random.uniform(2000, 8000)))
            FinancialMetrics.objects.update_or_create(
                date=target_date,
                specialty=specialty,
                defaults={
                    'consultation_revenue': spec_consultation,
                    'bed_revenue': Decimal(str(random.uniform(3000, 12000))),
                    'procedure_revenue': spec_consultation * Decimal('0.3'),
                    'pharmacy_revenue': spec_consultation * Decimal('0.2'),
                    'other_revenue': spec_consultation * Decimal('0.1'),
                    'staff_costs': spec_consultation * Decimal('0.4'),
                    'operational_costs': spec_consultation * Decimal('0.2'),
                    'equipment_costs': spec_consultation * Decimal('0.1'),
                }
            )

    def generate_quality_data(self, target_date, specialties):
        """Generate sample quality metrics"""
        # Overall quality metrics
        QualityMetrics.objects.update_or_create(
            date=target_date,
            department=None,
            defaults={
                'average_satisfaction_score': Decimal(str(random.uniform(3.8, 4.8))),
                'satisfaction_responses': random.randint(30, 80),
                'readmission_rate': Decimal(str(random.uniform(5, 15))),
                'infection_rate': Decimal(str(random.uniform(1, 5))),
                'mortality_rate': Decimal(str(random.uniform(0.5, 3))),
                'average_wait_time': random.randint(15, 45),
                'appointment_punctuality': Decimal(str(random.uniform(80, 95))),
                'incident_count': random.randint(0, 3),
                'near_miss_count': random.randint(0, 5),
            }
        )
        
        # Specialty-specific quality metrics
        for specialty in specialties:
            QualityMetrics.objects.update_or_create(
                date=target_date,
                specialty=specialty,
                defaults={
                    'average_satisfaction_score': Decimal(str(random.uniform(3.5, 4.9))),
                    'satisfaction_responses': random.randint(5, 20),
                    'readmission_rate': Decimal(str(random.uniform(3, 18))),
                    'average_wait_time': random.randint(10, 60),
                    'appointment_punctuality': Decimal(str(random.uniform(75, 98))),
                    'incident_count': random.randint(0, 2),
                }
            )

    def generate_staff_efficiency_data(self, target_date, doctors):
        """Generate sample staff efficiency data"""
        for doctor in doctors:
            patients_seen = random.randint(8, 25)
            appointments = random.randint(patients_seen, patients_seen + 5)
            
            StaffEfficiencyMetrics.objects.update_or_create(
                date=target_date,
                staff_member=doctor.user,
                defaults={
                    'patients_seen': patients_seen,
                    'appointments_completed': appointments,
                    'average_consultation_time': random.randint(20, 45),
                    'patient_satisfaction_score': Decimal(str(random.uniform(3.5, 4.8))),
                    'no_show_rate': Decimal(str(random.uniform(5, 20))),
                    'scheduled_hours': Decimal('8.0'),
                    'actual_hours': Decimal(str(random.uniform(7.5, 9.5))),
                    'overtime_hours': Decimal(str(random.uniform(0, 2))),
                    'revenue_generated': Decimal(str(patients_seen * random.uniform(200, 800))),
                }
            )
