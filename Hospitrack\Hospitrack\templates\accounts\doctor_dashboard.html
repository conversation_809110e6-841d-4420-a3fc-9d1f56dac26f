{% extends 'base.html' %}

{% block title %}Doctor Dashboard - HospiTrack{% endblock %}

{% block content %}
<div class="space-y-6">
    <!-- Header -->
    <div class="bg-white shadow rounded-lg p-6">
        <h1 class="text-3xl font-bold text-gray-900">Doctor Dashboard</h1>
        <p class="text-gray-600 mt-2">Welcome back, Dr. {{ doctor.user.first_name }} {{ doctor.user.last_name }}!</p>
        <div class="mt-4 grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
            <div><strong>Specialization:</strong> {{ doctor.specialization }}</div>
            <div><strong>Department:</strong> {{ doctor.department }}</div>
            <div><strong>License:</strong> {{ doctor.license_number }}</div>
        </div>
    </div>

    <!-- Today's Statistics -->
    <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
        <!-- Today's Appointments -->
        <div class="bg-white overflow-hidden shadow rounded-lg">
            <div class="p-5">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <i class="fas fa-calendar-day text-3xl text-blue-500"></i>
                    </div>
                    <div class="ml-5 w-0 flex-1">
                        <dl>
                            <dt class="text-sm font-medium text-gray-500 truncate">Today's Appointments</dt>
                            <dd class="text-3xl font-bold text-gray-900">{{ today_count }}</dd>
                        </dl>
                    </div>
                </div>
            </div>
            <div class="bg-gray-50 px-5 py-3">
                <div class="text-sm">
                    <a href="{% url 'appointments:today' %}" class="font-medium text-blue-700 hover:text-blue-900">
                        View all appointments
                    </a>
                </div>
            </div>
        </div>

        <!-- Consultation Fee -->
        <div class="bg-white overflow-hidden shadow rounded-lg">
            <div class="p-5">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <i class="fas fa-rupee-sign text-3xl text-green-500"></i>
                    </div>
                    <div class="ml-5 w-0 flex-1">
                        <dl>
                            <dt class="text-sm font-medium text-gray-500 truncate">Consultation Fee</dt>
                            <dd class="text-3xl font-bold text-gray-900">₹{{ doctor.consultation_fee }}</dd>
                        </dl>
                    </div>
                </div>
            </div>
        </div>

        <!-- Availability -->
        <div class="bg-white overflow-hidden shadow rounded-lg">
            <div class="p-5">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <i class="fas fa-clock text-3xl text-purple-500"></i>
                    </div>
                    <div class="ml-5 w-0 flex-1">
                        <dl>
                            <dt class="text-sm font-medium text-gray-500 truncate">Available Hours</dt>
                            <dd class="text-lg font-bold text-gray-900">{{ doctor.available_from }} - {{ doctor.available_to }}</dd>
                        </dl>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Today's Appointments -->
    {% if today_appointments %}
    <div class="bg-white shadow rounded-lg p-6">
        <h2 class="text-xl font-bold text-gray-900 mb-4">Today's Appointments</h2>
        <div class="overflow-x-auto">
            <table class="min-w-full divide-y divide-gray-200">
                <thead class="bg-gray-50">
                    <tr>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Time</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Patient</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Type</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                    </tr>
                </thead>
                <tbody class="bg-white divide-y divide-gray-200">
                    {% for appointment in today_appointments %}
                    <tr>
                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                            {{ appointment.appointment_time }}
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                            {{ appointment.patient.full_name }}
                            <div class="text-xs text-gray-500">{{ appointment.patient.patient_id }}</div>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                            {{ appointment.get_appointment_type_display }}
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full 
                                {% if appointment.status == 'completed' %}bg-green-100 text-green-800
                                {% elif appointment.status == 'in_progress' %}bg-blue-100 text-blue-800
                                {% elif appointment.status == 'confirmed' %}bg-yellow-100 text-yellow-800
                                {% else %}bg-gray-100 text-gray-800{% endif %}">
                                {{ appointment.get_status_display }}
                            </span>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                            <a href="{% url 'appointments:detail' appointment.id %}" class="text-blue-600 hover:text-blue-900 mr-3">View</a>
                            <a href="{% url 'patients:detail' appointment.patient.id %}" class="text-green-600 hover:text-green-900">Patient</a>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
    </div>
    {% else %}
    <div class="bg-white shadow rounded-lg p-6">
        <h2 class="text-xl font-bold text-gray-900 mb-4">Today's Appointments</h2>
        <div class="text-center py-8">
            <i class="fas fa-calendar-times text-4xl text-gray-400 mb-4"></i>
            <p class="text-gray-500">No appointments scheduled for today.</p>
        </div>
    </div>
    {% endif %}

    <!-- Upcoming Appointments -->
    {% if upcoming_appointments %}
    <div class="bg-white shadow rounded-lg p-6">
        <h2 class="text-xl font-bold text-gray-900 mb-4">Upcoming Appointments</h2>
        <div class="space-y-3">
            {% for appointment in upcoming_appointments %}
            <div class="border border-gray-200 rounded-lg p-4">
                <div class="flex justify-between items-start">
                    <div>
                        <h3 class="font-medium text-gray-900">{{ appointment.patient.full_name }}</h3>
                        <p class="text-sm text-gray-600">{{ appointment.patient.patient_id }}</p>
                        <p class="text-sm text-gray-500">{{ appointment.get_appointment_type_display }}</p>
                    </div>
                    <div class="text-right">
                        <p class="text-sm font-medium text-gray-900">{{ appointment.appointment_date }}</p>
                        <p class="text-sm text-gray-600">{{ appointment.appointment_time }}</p>
                    </div>
                </div>
            </div>
            {% endfor %}
        </div>
    </div>
    {% endif %}

    <!-- Quick Actions -->
    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
        <!-- Patient Management -->
        <div class="bg-white shadow rounded-lg p-6">
            <h3 class="text-lg font-medium text-gray-900 mb-4">Patient Management</h3>
            <div class="space-y-3">
                <a href="{% url 'patients:list' %}" class="block w-full bg-blue-600 hover:bg-blue-700 text-white text-center py-2 px-4 rounded-md text-sm font-medium">
                    <i class="fas fa-users mr-2"></i>View All Patients
                </a>
                <a href="{% url 'patients:updates_list' %}" class="block w-full bg-green-600 hover:bg-green-700 text-white text-center py-2 px-4 rounded-md text-sm font-medium">
                    <i class="fas fa-notes-medical mr-2"></i>Patient Updates
                </a>
            </div>
        </div>

        <!-- Appointments -->
        <div class="bg-white shadow rounded-lg p-6">
            <h3 class="text-lg font-medium text-gray-900 mb-4">Appointments</h3>
            <div class="space-y-3">
                <a href="{% url 'appointments:today' %}" class="block w-full bg-purple-600 hover:bg-purple-700 text-white text-center py-2 px-4 rounded-md text-sm font-medium">
                    <i class="fas fa-calendar-day mr-2"></i>Today's Schedule
                </a>
                <a href="{% url 'appointments:opd_queue' %}" class="block w-full bg-blue-600 hover:bg-blue-700 text-white text-center py-2 px-4 rounded-md text-sm font-medium">
                    <i class="fas fa-users mr-2"></i>My OPD Queue
                </a>
                <a href="{% url 'appointments:list' %}" class="block w-full bg-gray-600 hover:bg-gray-700 text-white text-center py-2 px-4 rounded-md text-sm font-medium">
                    <i class="fas fa-calendar mr-2"></i>All Appointments
                </a>
            </div>
        </div>

        <!-- Hospital Admissions -->
        <div class="bg-white shadow rounded-lg p-6">
            <h3 class="text-lg font-medium text-gray-900 mb-4">My Hospital Patients</h3>
            <div class="space-y-3">
                <a href="{% url 'patients:admission_list' %}" class="block w-full bg-orange-600 hover:bg-orange-700 text-white text-center py-2 px-4 rounded-md text-sm font-medium">
                    <i class="fas fa-bed mr-2"></i>Admitted Patients
                </a>
                <div class="text-center text-sm text-gray-500 mt-2">
                    View patients admitted under your care
                </div>
            </div>
        </div>

        <!-- Specialty Team -->
        {% if doctor.specialty %}
        <div class="bg-white shadow rounded-lg p-6">
            <h3 class="text-lg font-medium text-gray-900 mb-4">{{ doctor.specialty.name }} Team</h3>
            <div class="space-y-3">
                <a href="{% url 'specialty_team' %}" class="block w-full bg-indigo-600 hover:bg-indigo-700 text-white text-center py-2 px-4 rounded-md text-sm font-medium">
                    <i class="fas fa-users mr-2"></i>View Team & Nurses
                </a>
                <div class="grid grid-cols-2 gap-2 text-sm text-gray-600 mt-3">
                    <div class="text-center">
                        <span class="block font-medium text-green-600">{{ available_nurses_count }}</span>
                        <span class="text-xs">Available</span>
                    </div>
                    <div class="text-center">
                        <span class="block font-medium text-red-600">{{ nurses_on_leave_count }}</span>
                        <span class="text-xs">On Leave</span>
                    </div>
                </div>
            </div>
        </div>
        {% endif %}
    </div>

    <!-- Recent Patient Updates -->
    {% if recent_updates %}
    <div class="bg-white shadow rounded-lg p-6">
        <h2 class="text-xl font-bold text-gray-900 mb-4">Recent Patient Updates</h2>
        <div class="space-y-4">
            {% for update in recent_updates %}
            <div class="border-l-4 border-blue-400 pl-4">
                <div class="flex justify-between items-start">
                    <div>
                        <h4 class="font-medium text-gray-900">{{ update.patient.full_name }}</h4>
                        <p class="text-sm text-gray-600 mt-1">{{ update.update_text|truncatewords:20 }}</p>
                    </div>
                    <div class="text-right text-sm text-gray-500">
                        {{ update.created_at|date:"M d, H:i" }}
                    </div>
                </div>
            </div>
            {% endfor %}
        </div>
    </div>
    {% endif %}
</div>
{% endblock %}
