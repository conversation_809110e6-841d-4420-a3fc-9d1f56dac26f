# Nurse Leave Management & Doctor-Nurse Team View System

## Overview
The HospiTrack system now includes comprehensive nurse leave management and allows doctors to view which nurses are working under their specialty, including their leave status.

## Key Features

### 1. Nurse Leave Management
- **Leave Status Tracking**: Available, On Leave, Sick Leave, Vacation, Emergency Leave
- **Date Management**: Start and end dates for leave periods
- **Reason Tracking**: Detailed reasons for leave
- **Automatic Status Updates**: System automatically updates status based on dates

### 2. Doctor Team View
- **Specialty Team Overview**: Doctors can see all nurses in their specialty
- **Availability Status**: Real-time view of which nurses are available or on leave
- **Shift Information**: View nurses by shift (morning, evening, night)
- **Leave Details**: See leave duration and reasons

### 3. Admin Leave Management
- **Centralized Control**: <PERSON><PERSON> can manage leave for all nurses
- **Quick Status Updates**: Easy interface to set/update leave status
- **Leave History**: Track leave patterns and history

## Database Schema

### Nurse Model Updates:
```python
class Nurse(models.Model):
    # ... existing fields ...
    leave_status = models.Char<PERSON><PERSON>(max_length=20, choices=LEAVE_STATUS_CHOICES, default='available')
    leave_start_date = models.DateField(null=True, blank=True)
    leave_end_date = models.DateField(null=True, blank=True)
    leave_reason = models.TextField(blank=True, null=True)
```

### Leave Status Choices:
- `available` - Available for duty
- `on_leave` - General leave
- `sick_leave` - Medical leave
- `vacation` - Vacation leave
- `emergency_leave` - Emergency leave

## Doctor Dashboard Features

### Team Statistics:
- **Available Nurses Count**: Number of nurses currently available
- **On Leave Count**: Number of nurses currently on leave
- **Total Nurses**: Total nurses in the specialty
- **Quick Access**: Direct link to detailed team view

### Specialty Team View:
- **Available Nurses**: List of nurses ready for duty with shift information
- **Nurses on Leave**: List of nurses on leave with leave type and duration
- **Leave Details**: End dates and days remaining for each nurse on leave
- **Other Doctors**: Other doctors in the same specialty

## Access Control Integration

### Doctor Access:
- Doctors can only see nurses in their own specialty
- Cannot access nurses from other specialties
- Can view detailed leave information for their team

### Admin Access:
- Full access to all nurses across all specialties
- Can manage leave status for any nurse
- Can view comprehensive leave reports

### Nurse Access:
- Nurses can view their own leave status
- Cannot modify their own leave status (admin-controlled)

## URL Structure

### New URLs:
- `/nurses/leave/` - Nurse leave management overview (admin)
- `/nurses/leave/<nurse_id>/` - Edit specific nurse leave status (admin)
- `/specialty-team/` - Doctor's specialty team view (doctor)

## Templates

### New Templates:
1. **`specialty_team.html`** - Doctor's team overview
2. **`nurse_leave_form.html`** - Leave management form
3. **Updated `nurses_list.html`** - Added leave status column
4. **Updated `doctor_dashboard.html`** - Added team statistics

## Management Commands

### Demo Commands:
```bash
# Set up nurse leave demonstration
python manage.py demo_nurse_leave

# View complete system overview
python manage.py demo_specialty_access
```

## Current System Status

### Nurses by Leave Status:
- **Available**: 7 nurses
- **On Leave**: 3 nurses
  - Amy Adams (Emergency Medicine) - Sick Leave until Jul 29
  - Christine Martinez (Cardiology) - Vacation until Aug 2
  - Michelle Davis (General Medicine) - Emergency Leave until Jul 27

### Specialty Distribution:
- **Cardiology**: 2 nurses (1 available, 1 on vacation)
- **Emergency Medicine**: 2 nurses (1 available, 1 on sick leave)
- **General Medicine**: 3 nurses (2 available, 1 on emergency leave)
- **Orthopedics**: 1 nurse (available)
- **Pediatrics**: 1 nurse (available)
- **Surgery**: 1 nurse (available)

## Example Workflows

### 1. Doctor Checking Team Status:
1. Doctor logs into dashboard
2. Sees team statistics (available/on leave counts)
3. Clicks "View Team & Nurses"
4. Views detailed team page with:
   - Available nurses by shift
   - Nurses on leave with details
   - Other doctors in specialty

### 2. Admin Managing Nurse Leave:
1. Admin goes to nurses list
2. Clicks "Manage Leave" for specific nurse
3. Sets leave status, dates, and reason
4. System automatically updates availability

### 3. Automatic Leave Management:
1. System checks leave dates daily
2. Automatically updates status when leave ends
3. Clears leave dates and reasons
4. Updates availability for doctors to see

## Benefits

### For Doctors:
- **Team Awareness**: Know which nurses are available for patient care
- **Shift Planning**: See nurse availability by shift
- **Leave Visibility**: Understand team capacity and plan accordingly
- **Specialty Focus**: Only see relevant team members

### For Admins:
- **Centralized Management**: Control all nurse leave from one interface
- **Real-time Updates**: Immediate visibility of leave status changes
- **Planning Support**: Help with staffing and scheduling decisions

### For Nurses:
- **Status Transparency**: Clear visibility of their leave status
- **Professional Tracking**: Proper documentation of leave periods

## Integration with Existing Features

### Patient Care:
- Doctors can assign patients knowing which nurses are available
- Patient updates can be directed to available nurses
- Specialty-based care remains intact with leave considerations

### Scheduling:
- Appointment scheduling can consider nurse availability
- Shift planning accounts for leave status
- Emergency coverage can be planned based on availability

## Future Enhancements

1. **Leave Requests**: Allow nurses to request leave through the system
2. **Leave Approval Workflow**: Multi-step approval process
3. **Leave Calendar**: Visual calendar showing all leave periods
4. **Automatic Notifications**: Alert doctors when team members go on/return from leave
5. **Leave Reports**: Generate reports on leave patterns and usage
6. **Shift Coverage**: Automatic suggestions for covering shifts during leave
7. **Leave Balance**: Track annual leave entitlements and usage

---

The nurse leave management system is now fully integrated with the specialty-based access control, providing doctors with complete visibility of their team's availability while maintaining proper access controls and administrative oversight.