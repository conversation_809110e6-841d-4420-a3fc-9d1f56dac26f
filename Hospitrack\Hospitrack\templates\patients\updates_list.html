{% extends 'base.html' %}

{% block title %}Patient Updates - HospiTrack{% endblock %}

{% block content %}
<div class="space-y-6">
    <!-- Header -->
    <div class="bg-white shadow rounded-lg p-6">
        <div class="flex justify-between items-center">
            <div>
                <h1 class="text-3xl font-bold text-gray-900">Patient Updates</h1>
                <p class="text-gray-600 mt-2">Daily patient care updates and observations</p>
            </div>
            {% if user.user_type == 'nurse' or user.user_type == 'doctor' %}
            <a href="{% url 'patients:list' %}" class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-green-600 hover:bg-green-700">
                <i class="fas fa-plus mr-2"></i>
                Add Patient Update
            </a>
            {% endif %}
        </div>
    </div>

    <!-- Filters -->
    <div class="bg-white shadow rounded-lg p-6">
        <form method="get" class="grid grid-cols-1 md:grid-cols-4 gap-4">
            <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">Search Patient</label>
                <input type="text" name="search" value="{{ request.GET.search }}" placeholder="Patient name or ID..." 
                       class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500">
            </div>
            <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">Updated By</label>
                <select name="updated_by" class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500">
                    <option value="">All Staff</option>
                    {% if user.user_type == 'admin' %}
                    <option value="nurse">Nurses</option>
                    <option value="doctor">Doctors</option>
                    {% endif %}
                </select>
            </div>
            <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">Date Range</label>
                <select name="date_range" class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500">
                    <option value="">All Time</option>
                    <option value="today">Today</option>
                    <option value="week">This Week</option>
                    <option value="month">This Month</option>
                </select>
            </div>
            <div class="flex items-end">
                <button type="submit" class="w-full bg-blue-600 hover:bg-blue-700 text-white py-2 px-4 rounded-md text-sm font-medium">
                    <i class="fas fa-search mr-2"></i>Filter
                </button>
            </div>
        </form>
    </div>

    <!-- Updates List -->
    <div class="bg-white shadow rounded-lg overflow-hidden">
        <div class="px-6 py-4 border-b border-gray-200">
            <h2 class="text-lg font-medium text-gray-900">Recent Updates ({{ updates.count }})</h2>
        </div>
        
        {% if updates %}
        <div class="divide-y divide-gray-200">
            {% for update in updates %}
            <div class="p-6 hover:bg-gray-50">
                <div class="flex items-start justify-between">
                    <div class="flex-1">
                        <!-- Patient Info -->
                        <div class="flex items-center mb-3">
                            <div class="flex-shrink-0">
                                <div class="w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center">
                                    <i class="fas fa-user text-blue-600"></i>
                                </div>
                            </div>
                            <div class="ml-3">
                                <h3 class="text-lg font-medium text-gray-900">
                                    <a href="{% url 'patients:detail' update.patient.id %}" class="hover:text-blue-600">
                                        {{ update.patient.full_name }}
                                    </a>
                                </h3>
                                <p class="text-sm text-gray-500">{{ update.patient.patient_id }} • {{ update.patient.age }} years • {{ update.patient.blood_group }}</p>
                            </div>
                        </div>

                        <!-- Update Content -->
                        <div class="bg-gray-50 rounded-lg p-4 mb-3">
                            <p class="text-gray-900 mb-2">{{ update.update_text }}</p>
                            
                            {% if update.vital_signs %}
                            <div class="mb-2">
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                    <i class="fas fa-heartbeat mr-1"></i>
                                    Vitals: {{ update.vital_signs }}
                                </span>
                            </div>
                            {% endif %}
                            
                            {% if update.medications_given %}
                            <div class="mb-2">
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                                    <i class="fas fa-pills mr-1"></i>
                                    Medications: {{ update.medications_given }}
                                </span>
                            </div>
                            {% endif %}
                            
                            {% if update.notes %}
                            <div class="text-sm text-gray-600 mt-2">
                                <strong>Notes:</strong> {{ update.notes }}
                            </div>
                            {% endif %}
                        </div>

                        <!-- Update Meta -->
                        <div class="flex items-center text-sm text-gray-500">
                            <div class="flex items-center">
                                <i class="fas fa-user-md mr-1"></i>
                                <span>{{ update.updated_by.get_full_name }}</span>
                                <span class="mx-2">•</span>
                                <span class="capitalize">{{ update.updated_by.user_type }}</span>
                            </div>
                            <div class="ml-auto flex items-center">
                                <i class="fas fa-clock mr-1"></i>
                                <span>{{ update.created_at|date:"M d, Y H:i" }}</span>
                            </div>
                        </div>
                    </div>

                    <!-- Actions -->
                    <div class="ml-4 flex-shrink-0">
                        <div class="flex space-x-2">
                            <a href="{% url 'patients:detail' update.patient.id %}" 
                               class="inline-flex items-center px-3 py-1 border border-gray-300 text-xs font-medium rounded text-gray-700 bg-white hover:bg-gray-50">
                                <i class="fas fa-eye mr-1"></i>
                                View Patient
                            </a>
                            {% if user.user_type == 'nurse' or user.user_type == 'doctor' %}
                            <a href="{% url 'patients:update' update.patient.id %}" 
                               class="inline-flex items-center px-3 py-1 border border-transparent text-xs font-medium rounded text-white bg-green-600 hover:bg-green-700">
                                <i class="fas fa-plus mr-1"></i>
                                Add Update
                            </a>
                            {% endif %}
                        </div>
                    </div>
                </div>
            </div>
            {% endfor %}
        </div>

        <!-- Pagination -->
        <div class="bg-white px-4 py-3 border-t border-gray-200 sm:px-6">
            <div class="flex items-center justify-between">
                <div class="flex-1 flex justify-between sm:hidden">
                    <a href="#" class="relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
                        Previous
                    </a>
                    <a href="#" class="ml-3 relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
                        Next
                    </a>
                </div>
                <div class="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
                    <div>
                        <p class="text-sm text-gray-700">
                            Showing <span class="font-medium">1</span> to <span class="font-medium">{{ updates.count }}</span> of <span class="font-medium">{{ updates.count }}</span> results
                        </p>
                    </div>
                </div>
            </div>
        </div>
        {% else %}
        <div class="text-center py-12">
            <i class="fas fa-notes-medical text-4xl text-gray-400 mb-4"></i>
            <h3 class="text-lg font-medium text-gray-900 mb-2">No patient updates found</h3>
            <p class="text-gray-500 mb-4">Start by adding updates for your patients.</p>
            {% if user.user_type == 'nurse' or user.user_type == 'doctor' %}
            <a href="{% url 'patients:list' %}" class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-green-600 hover:bg-green-700">
                <i class="fas fa-plus mr-2"></i>
                Add Patient Update
            </a>
            {% endif %}
        </div>
        {% endif %}
    </div>

    <!-- Quick Stats -->
    <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
        <div class="bg-white overflow-hidden shadow rounded-lg">
            <div class="p-5">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <i class="fas fa-notes-medical text-2xl text-green-500"></i>
                    </div>
                    <div class="ml-5 w-0 flex-1">
                        <dl>
                            <dt class="text-sm font-medium text-gray-500 truncate">Total Updates</dt>
                            <dd class="text-2xl font-bold text-gray-900">{{ updates.count }}</dd>
                        </dl>
                    </div>
                </div>
            </div>
        </div>

        <div class="bg-white overflow-hidden shadow rounded-lg">
            <div class="p-5">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <i class="fas fa-calendar-day text-2xl text-blue-500"></i>
                    </div>
                    <div class="ml-5 w-0 flex-1">
                        <dl>
                            <dt class="text-sm font-medium text-gray-500 truncate">Today's Updates</dt>
                            <dd class="text-2xl font-bold text-gray-900">{{ updates.count }}</dd>
                        </dl>
                    </div>
                </div>
            </div>
        </div>

        <div class="bg-white overflow-hidden shadow rounded-lg">
            <div class="p-5">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <i class="fas fa-user-nurse text-2xl text-purple-500"></i>
                    </div>
                    <div class="ml-5 w-0 flex-1">
                        <dl>
                            <dt class="text-sm font-medium text-gray-500 truncate">My Updates</dt>
                            <dd class="text-2xl font-bold text-gray-900">{{ updates.count }}</dd>
                        </dl>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
