from django.core.management.base import BaseCommand
from django.db import transaction
from accounts.models import Doctor, Nurse, MedicalSpecialty
from datetime import date

class Command(BaseCommand):
    help = 'Demonstrate how doctors can see nurses in their specialty and leave status'

    def handle(self, *args, **options):
        self.stdout.write(self.style.SUCCESS('=== Doctor-Nurse Team View Demonstration ===\n'))
        
        # Get all doctors with specialties
        doctors = Doctor.objects.filter(specialty__isnull=False).select_related('user', 'specialty')
        
        for doctor in doctors:
            self.stdout.write(self.style.WARNING(f'👨‍⚕️ Dr. {doctor.user.get_full_name()} - {doctor.specialty.name} Specialist'))
            self.stdout.write('=' * 60)
            
            # Get nurses in the same specialty
            specialty_nurses = doctor.get_specialty_nurses()
            available_nurses = doctor.get_available_nurses()
            nurses_on_leave = doctor.get_nurses_on_leave()
            
            # Show team statistics
            self.stdout.write(f'📊 Team Statistics:')
            self.stdout.write(f'   Total Nurses: {specialty_nurses.count()}')
            self.stdout.write(f'   Available: {available_nurses.count()}')
            self.stdout.write(f'   On Leave: {nurses_on_leave.count()}')
            self.stdout.write('')
            
            if specialty_nurses.exists():
                # Show available nurses
                if available_nurses.exists():
                    self.stdout.write(self.style.SUCCESS('✅ AVAILABLE NURSES:'))
                    for nurse in available_nurses:
                        shift_display = dict(nurse._meta.get_field('shift').choices).get(nurse.shift, nurse.shift)
                        self.stdout.write(f'   • {nurse.user.get_full_name()}')
                        self.stdout.write(f'     Shift: {shift_display}')
                        self.stdout.write(f'     License: {nurse.license_number}')
                        self.stdout.write(f'     Status: ✅ Available for duty')
                        self.stdout.write('')
                else:
                    self.stdout.write(self.style.WARNING('   No nurses available'))
                    self.stdout.write('')
                
                # Show nurses on leave
                if nurses_on_leave.exists():
                    self.stdout.write(self.style.ERROR('❌ NURSES ON LEAVE:'))
                    for nurse in nurses_on_leave:
                        shift_display = dict(nurse._meta.get_field('shift').choices).get(nurse.shift, nurse.shift)
                        leave_info = nurse.current_leave_info
                        self.stdout.write(f'   • {nurse.user.get_full_name()}')
                        self.stdout.write(f'     Shift: {shift_display}')
                        self.stdout.write(f'     License: {nurse.license_number}')
                        self.stdout.write(f'     Status: ❌ {nurse.leave_status_display}')
                        if leave_info:
                            self.stdout.write(f'     Leave Period: {nurse.leave_start_date} to {nurse.leave_end_date}')
                            if leave_info['days_remaining'] > 0:
                                self.stdout.write(f'     Days Remaining: {leave_info["days_remaining"]}')
                            else:
                                self.stdout.write(f'     Status: Leave ending today')
                            if nurse.leave_reason:
                                self.stdout.write(f'     Reason: {nurse.leave_reason}')
                        self.stdout.write('')
                else:
                    self.stdout.write(self.style.SUCCESS('   All nurses are available! 🎉'))
                    self.stdout.write('')
                
                # Show nurses by shift
                self.stdout.write('🕐 NURSES BY SHIFT:')
                shifts = [('morning', 'Morning (6 AM - 2 PM)'), ('evening', 'Evening (2 PM - 10 PM)'), ('night', 'Night (10 PM - 6 AM)')]
                
                for shift_code, shift_name in shifts:
                    shift_nurses = doctor.get_nurses_by_shift(shift_code)
                    if shift_nurses.exists():
                        self.stdout.write(f'   {shift_name}:')
                        for nurse in shift_nurses:
                            status_icon = '✅' if nurse.is_available else '❌'
                            status_text = 'Available' if nurse.is_available else nurse.leave_status_display
                            self.stdout.write(f'     {status_icon} {nurse.user.get_full_name()} - {status_text}')
                    else:
                        self.stdout.write(f'   {shift_name}: No nurses assigned')
                
            else:
                self.stdout.write(self.style.WARNING(f'   No nurses assigned to {doctor.specialty.name} specialty'))
            
            self.stdout.write('\n' + '='*80 + '\n')
        
        # Show what doctors CANNOT see (other specialties)
        self.stdout.write(self.style.WARNING('🔒 ACCESS CONTROL DEMONSTRATION:'))
        self.stdout.write('Doctors can ONLY see nurses in their own specialty\n')
        
        # Example with Cardiology doctor
        cardio_doctor = Doctor.objects.filter(specialty__name='Cardiology').first()
        if cardio_doctor:
            self.stdout.write(f'Example: Dr. {cardio_doctor.user.get_full_name()} (Cardiology) can see:')
            cardio_nurses = cardio_doctor.get_specialty_nurses()
            for nurse in cardio_nurses:
                self.stdout.write(f'   ✅ {nurse.user.get_full_name()} (Cardiology)')
            
            # Show what they cannot see
            other_nurses = Nurse.objects.exclude(specialty=cardio_doctor.specialty)[:3]
            if other_nurses:
                self.stdout.write(f'\nBut CANNOT see nurses from other specialties:')
                for nurse in other_nurses:
                    specialty_name = nurse.specialty.name if nurse.specialty else 'No specialty'
                    self.stdout.write(f'   ❌ {nurse.user.get_full_name()} ({specialty_name})')
        
        self.stdout.write('\n' + '='*80)
        self.stdout.write(self.style.SUCCESS('🎯 HOW TO USE THE SYSTEM:'))
        self.stdout.write('1. Doctor logs into their dashboard')
        self.stdout.write('2. Sees team statistics (available/on leave counts)')
        self.stdout.write('3. Clicks "View Team & Nurses" button')
        self.stdout.write('4. Views detailed team page with:')
        self.stdout.write('   • Available nurses with shift information')
        self.stdout.write('   • Nurses on leave with leave details')
        self.stdout.write('   • Leave duration and reasons')
        self.stdout.write('   • Quick actions for patient management')
        self.stdout.write('')
        self.stdout.write('🔗 URLs to test:')
        self.stdout.write('   • Doctor Dashboard: http://127.0.0.1:8000/accounts/doctor-dashboard/')
        self.stdout.write('   • Specialty Team View: http://127.0.0.1:8000/accounts/specialty-team/')
        self.stdout.write('   • Admin Nurse Management: http://127.0.0.1:8000/accounts/nurses/')
        
        self.stdout.write('\n' + self.style.SUCCESS('Demo completed! 🚀'))