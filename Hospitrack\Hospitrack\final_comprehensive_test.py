#!/usr/bin/env python
"""
Final comprehensive test to verify all critical functionality
"""
import os
import sys
import django
from django.test import Client

# Setup Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'hospitrack.settings')
django.setup()

from accounts.models import User, Doctor, Nurse, Admin, MedicalSpecialty

def test_critical_functionality():
    """Test all critical system functionality"""
    print("🚀 Final Comprehensive System Test")
    print("=" * 80)
    
    client = Client()
    results = {'passed': 0, 'failed': 0, 'errors': []}
    
    # 1. Test Authentication System
    print(f"\n🔐 Testing Authentication System")
    auth_tests = [
        ('admin', 'admin123', 'admin'),
        ('dr.emergency', 'doctor123', 'doctor'),
        ('dr.oncology', 'doctor123', 'doctor'),
        ('emma.johnson1', 'nurse123', 'nurse'),
        ('amelia.jackson11', 'nurse123', 'nurse')
    ]
    
    for username, password, expected_type in auth_tests:
        try:
            login_success = client.login(username=username, password=password)
            if login_success:
                user = User.objects.get(username=username)
                if user.user_type == expected_type:
                    print(f"   ✅ {username} ({expected_type}) login successful")
                    results['passed'] += 1
                else:
                    print(f"   ❌ {username} type mismatch: {user.user_type} != {expected_type}")
                    results['failed'] += 1
                    results['errors'].append(f"{username} type mismatch")
            else:
                print(f"   ❌ {username} login failed")
                results['failed'] += 1
                results['errors'].append(f"{username} login failed")
            client.logout()
        except Exception as e:
            print(f"   ❌ {username} test exception: {str(e)}")
            results['failed'] += 1
            results['errors'].append(f"{username} exception: {str(e)}")
    
    # 2. Test Dashboard Access
    print(f"\n📊 Testing Dashboard Access")
    dashboard_tests = [
        ('admin', 'admin123', '/admin-dashboard/'),
        ('dr.emergency', 'doctor123', '/doctor-dashboard/'),
        ('emma.johnson1', 'nurse123', '/nurse-dashboard/')
    ]
    
    for username, password, dashboard_url in dashboard_tests:
        try:
            client.login(username=username, password=password)
            response = client.get(dashboard_url)
            if response.status_code == 200:
                print(f"   ✅ {username} dashboard accessible")
                results['passed'] += 1
            else:
                print(f"   ❌ {username} dashboard failed: {response.status_code}")
                results['failed'] += 1
                results['errors'].append(f"{username} dashboard failed: {response.status_code}")
            client.logout()
        except Exception as e:
            print(f"   ❌ {username} dashboard exception: {str(e)}")
            results['failed'] += 1
            results['errors'].append(f"{username} dashboard exception: {str(e)}")
    
    # 3. Test Specialty System
    print(f"\n🏥 Testing Specialty System")
    try:
        # Check specialties exist
        specialties = MedicalSpecialty.objects.count()
        if specialties >= 5:
            print(f"   ✅ Medical specialties created: {specialties}")
            results['passed'] += 1
        else:
            print(f"   ❌ Insufficient specialties: {specialties}")
            results['failed'] += 1
            results['errors'].append(f"Insufficient specialties: {specialties}")
        
        # Check doctor-specialty assignments
        doctors_with_specialty = Doctor.objects.filter(specialty__isnull=False).count()
        total_doctors = Doctor.objects.count()
        if doctors_with_specialty >= total_doctors * 0.8:  # At least 80% should have specialties
            print(f"   ✅ Doctor specialty assignments: {doctors_with_specialty}/{total_doctors}")
            results['passed'] += 1
        else:
            print(f"   ❌ Poor doctor specialty assignments: {doctors_with_specialty}/{total_doctors}")
            results['failed'] += 1
            results['errors'].append(f"Poor doctor specialty assignments")
        
        # Check nurse-specialty assignments
        nurses_with_specialty = Nurse.objects.filter(specialty__isnull=False).count()
        total_nurses = Nurse.objects.count()
        if nurses_with_specialty >= total_nurses * 0.8:  # At least 80% should have specialties
            print(f"   ✅ Nurse specialty assignments: {nurses_with_specialty}/{total_nurses}")
            results['passed'] += 1
        else:
            print(f"   ❌ Poor nurse specialty assignments: {nurses_with_specialty}/{total_nurses}")
            results['failed'] += 1
            results['errors'].append(f"Poor nurse specialty assignments")
            
    except Exception as e:
        print(f"   ❌ Specialty system exception: {str(e)}")
        results['failed'] += 1
        results['errors'].append(f"Specialty system exception: {str(e)}")
    
    # 4. Test Core Pages
    print(f"\n📄 Testing Core Pages")
    client.login(username='admin', password='admin123')
    
    core_pages = [
        ('/', 'Landing Page'),
        ('/login/', 'Login Page'),
        ('/admin-dashboard/', 'Admin Dashboard'),
        ('/doctors/', 'Doctors List'),
        ('/nurses/', 'Nurses List'),
        ('/patients/', 'Patients List'),
        ('/appointments/', 'Appointments List'),
        ('/beds/', 'Bed Management'),
        ('/staff/', 'Staff Management')
    ]
    
    for url, page_name in core_pages:
        try:
            response = client.get(url)
            if response.status_code == 200:
                print(f"   ✅ {page_name} accessible")
                results['passed'] += 1
            else:
                print(f"   ❌ {page_name} failed: {response.status_code}")
                results['failed'] += 1
                results['errors'].append(f"{page_name} failed: {response.status_code}")
        except Exception as e:
            print(f"   ❌ {page_name} exception: {str(e)}")
            results['failed'] += 1
            results['errors'].append(f"{page_name} exception: {str(e)}")
    
    # 5. Test Database Integrity
    print(f"\n🗄️ Testing Database Integrity")
    try:
        # User counts
        total_users = User.objects.count()
        admin_count = User.objects.filter(user_type='admin').count()
        doctor_count = User.objects.filter(user_type='doctor').count()
        nurse_count = User.objects.filter(user_type='nurse').count()
        
        print(f"   📊 Users: {total_users} total ({admin_count} admins, {doctor_count} doctors, {nurse_count} nurses)")
        
        # Profile integrity
        doctor_profiles = Doctor.objects.count()
        nurse_profiles = Nurse.objects.count()
        admin_profiles = Admin.objects.count()
        
        if doctor_count == doctor_profiles and nurse_count == nurse_profiles and admin_count == admin_profiles:
            print(f"   ✅ Profile integrity maintained")
            results['passed'] += 1
        else:
            print(f"   ❌ Profile integrity issues: Users({doctor_count},{nurse_count},{admin_count}) vs Profiles({doctor_profiles},{nurse_profiles},{admin_profiles})")
            results['failed'] += 1
            results['errors'].append("Profile integrity issues")
        
        # Check for expected user counts based on credentials file
        if doctor_count >= 5 and nurse_count >= 50:
            print(f"   ✅ Expected user counts met")
            results['passed'] += 1
        else:
            print(f"   ❌ Insufficient users: {doctor_count} doctors, {nurse_count} nurses")
            results['failed'] += 1
            results['errors'].append(f"Insufficient users")
            
    except Exception as e:
        print(f"   ❌ Database integrity exception: {str(e)}")
        results['failed'] += 1
        results['errors'].append(f"Database integrity exception: {str(e)}")
    
    # 6. Test Specialty Team Access
    print(f"\n👥 Testing Specialty Team Access")
    try:
        client.login(username='dr.emergency', password='doctor123')
        response = client.get('/specialty-team/')
        if response.status_code == 200:
            print(f"   ✅ Doctor can access specialty team")
            results['passed'] += 1
        else:
            print(f"   ❌ Specialty team access failed: {response.status_code}")
            results['failed'] += 1
            results['errors'].append(f"Specialty team access failed")
    except Exception as e:
        print(f"   ❌ Specialty team exception: {str(e)}")
        results['failed'] += 1
        results['errors'].append(f"Specialty team exception: {str(e)}")
    
    return results

def test_security_and_permissions():
    """Test security and permission controls"""
    print(f"\n🔒 Testing Security and Permissions")
    print("=" * 60)
    
    client = Client()
    results = {'passed': 0, 'failed': 0, 'errors': []}
    
    # Test unauthorized access
    print(f"\n🚫 Testing Unauthorized Access")
    try:
        # Try accessing admin dashboard without login
        response = client.get('/admin-dashboard/')
        if response.status_code == 302:  # Should redirect to login
            print(f"   ✅ Unauthorized admin access blocked")
            results['passed'] += 1
        else:
            print(f"   ❌ Unauthorized admin access allowed: {response.status_code}")
            results['failed'] += 1
            results['errors'].append("Unauthorized admin access allowed")
        
        # Try accessing doctor dashboard as nurse
        client.login(username='emma.johnson1', password='nurse123')
        response = client.get('/doctor-dashboard/')
        if response.status_code == 302:  # Should redirect
            print(f"   ✅ Cross-role access blocked (nurse->doctor)")
            results['passed'] += 1
        else:
            print(f"   ❌ Cross-role access allowed: {response.status_code}")
            results['failed'] += 1
            results['errors'].append("Cross-role access allowed")
        
        client.logout()
        
    except Exception as e:
        print(f"   ❌ Security test exception: {str(e)}")
        results['failed'] += 1
        results['errors'].append(f"Security test exception: {str(e)}")
    
    return results

def main():
    """Run all critical tests"""
    print("🎯 HospiTrack Critical System Verification")
    print("=" * 80)
    
    all_results = {'passed': 0, 'failed': 0, 'errors': []}
    
    # Run test suites
    test_suites = [
        ("Critical Functionality", test_critical_functionality),
        ("Security & Permissions", test_security_and_permissions)
    ]
    
    for suite_name, test_function in test_suites:
        print(f"\n🧪 Running {suite_name} Tests...")
        results = test_function()
        all_results['passed'] += results['passed']
        all_results['failed'] += results['failed']
        all_results['errors'].extend(results['errors'])
        
        print(f"   📊 {suite_name} Results: {results['passed']} passed, {results['failed']} failed")
    
    # Final summary
    print(f"\n" + "=" * 80)
    print(f"🎯 FINAL SYSTEM VERIFICATION RESULTS")
    print(f"=" * 80)
    print(f"✅ Total Passed: {all_results['passed']}")
    print(f"❌ Total Failed: {all_results['failed']}")
    
    if all_results['passed'] + all_results['failed'] > 0:
        success_rate = (all_results['passed']/(all_results['passed']+all_results['failed']))*100
        print(f"📊 Success Rate: {success_rate:.1f}%")
    
    if all_results['errors']:
        print(f"\n🔍 Issues Found:")
        for i, error in enumerate(all_results['errors'], 1):
            print(f"   {i}. {error}")
    
    # System status
    if all_results['failed'] == 0:
        print(f"\n🎉 SYSTEM STATUS: ✅ FULLY OPERATIONAL")
        print(f"   All critical functionality is working correctly.")
        print(f"   The HospiTrack system is ready for use.")
    elif all_results['failed'] <= 2:
        print(f"\n⚠️  SYSTEM STATUS: 🟡 MOSTLY OPERATIONAL")
        print(f"   Minor issues detected but core functionality works.")
        print(f"   System can be used with caution.")
    else:
        print(f"\n❌ SYSTEM STATUS: 🔴 NEEDS ATTENTION")
        print(f"   Multiple issues detected that need resolution.")
        print(f"   Please review and fix the identified problems.")
    
    return all_results['failed'] <= 2  # Allow up to 2 minor issues

if __name__ == '__main__':
    try:
        success = main()
        sys.exit(0 if success else 1)
    except Exception as e:
        print(f"❌ Critical test execution failed: {str(e)}")
        sys.exit(1)