{% extends 'base.html' %}

{% block title %}Edit OPD Registration - HospiTrack{% endblock %}

{% block content %}
<div class="space-y-6">
    <!-- Header -->
    <div class="bg-white shadow rounded-lg p-6">
        <div class="flex justify-between items-center">
            <div>
                <h1 class="text-3xl font-bold text-gray-900">Edit OPD Registration</h1>
                <p class="text-gray-600 mt-2">{{ registration.opd_number }} - {{ registration.patient_name }}</p>
            </div>
            <div class="flex space-x-3">
                <a href="{% url 'appointments:opd_detail' registration.id %}" class="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
                    <i class="fas fa-arrow-left mr-2"></i>
                    Back to Details
                </a>
                <a href="{% url 'appointments:opd_schedule' %}" class="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
                    <i class="fas fa-calendar mr-2"></i>
                    OPD Management
                </a>
            </div>
        </div>
    </div>

    <!-- Edit Form -->
    <div class="bg-white shadow rounded-lg p-6">
        <form method="post" class="space-y-6">
            {% csrf_token %}
            
            <!-- Patient Information -->
            <div>
                <h2 class="text-lg font-medium text-gray-900 mb-4">Patient Information</h2>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">
                            Patient Name <span class="text-red-500">*</span>
                        </label>
                        <input type="text" name="patient_name" value="{{ registration.patient_name }}" required class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500">
                    </div>
                    
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">
                            Age <span class="text-red-500">*</span>
                        </label>
                        <input type="number" name="age" value="{{ registration.age }}" min="0" max="120" required class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500">
                    </div>
                    
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">
                            Gender <span class="text-red-500">*</span>
                        </label>
                        <select name="gender" required class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500 bg-white">
                            <option value="">Select Gender</option>
                            <option value="M" {% if registration.gender == 'M' %}selected{% endif %}>Male</option>
                            <option value="F" {% if registration.gender == 'F' %}selected{% endif %}>Female</option>
                            <option value="O" {% if registration.gender == 'O' %}selected{% endif %}>Other</option>
                        </select>
                    </div>
                    
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">
                            Phone Number <span class="text-red-500">*</span>
                        </label>
                        <input type="tel" name="phone" value="{{ registration.phone }}" required class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500">
                    </div>
                </div>
                
                <div class="mt-4">
                    <label class="block text-sm font-medium text-gray-700 mb-2">
                        Address <span class="text-red-500">*</span>
                    </label>
                    <textarea name="address" rows="2" required class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500 resize-vertical">{{ registration.address }}</textarea>
                </div>
            </div>

            <!-- Medical Information -->
            <div>
                <h2 class="text-lg font-medium text-gray-900 mb-4">Medical Information</h2>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">
                            Visit Type <span class="text-red-500">*</span>
                        </label>
                        <select name="visit_type" required class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500 bg-white">
                            <option value="new" {% if registration.visit_type == 'new' %}selected{% endif %}>New Patient</option>
                            <option value="follow_up" {% if registration.visit_type == 'follow_up' %}selected{% endif %}>Follow-up</option>
                            <option value="consultation" {% if registration.visit_type == 'consultation' %}selected{% endif %}>Consultation</option>
                            <option value="emergency" {% if registration.visit_type == 'emergency' %}selected{% endif %}>Emergency</option>
                        </select>
                    </div>
                    
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">
                            Preferred Time <span class="text-red-500">*</span>
                        </label>
                        <select name="preferred_time" required class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500 bg-white">
                            <option value="">Select Preferred Time</option>
                            <!-- Morning Slots -->
                            <optgroup label="Morning OPD (9:00 AM - 12:00 PM)">
                                <option value="09:00" {% if registration.preferred_time == '09:00' %}selected{% endif %}>9:00 AM</option>
                                <option value="09:30" {% if registration.preferred_time == '09:30' %}selected{% endif %}>9:30 AM</option>
                                <option value="10:00" {% if registration.preferred_time == '10:00' %}selected{% endif %}>10:00 AM</option>
                                <option value="10:30" {% if registration.preferred_time == '10:30' %}selected{% endif %}>10:30 AM</option>
                                <option value="11:00" {% if registration.preferred_time == '11:00' %}selected{% endif %}>11:00 AM</option>
                                <option value="11:30" {% if registration.preferred_time == '11:30' %}selected{% endif %}>11:30 AM</option>
                            </optgroup>
                            <!-- Afternoon Slots -->
                            <optgroup label="Afternoon OPD (2:00 PM - 5:00 PM)">
                                <option value="14:00" {% if registration.preferred_time == '14:00' %}selected{% endif %}>2:00 PM</option>
                                <option value="14:30" {% if registration.preferred_time == '14:30' %}selected{% endif %}>2:30 PM</option>
                                <option value="15:00" {% if registration.preferred_time == '15:00' %}selected{% endif %}>3:00 PM</option>
                                <option value="15:30" {% if registration.preferred_time == '15:30' %}selected{% endif %}>3:30 PM</option>
                                <option value="16:00" {% if registration.preferred_time == '16:00' %}selected{% endif %}>4:00 PM</option>
                                <option value="16:30" {% if registration.preferred_time == '16:30' %}selected{% endif %}>4:30 PM</option>
                            </optgroup>
                            <!-- Evening Slots -->
                            <optgroup label="Evening OPD (6:00 PM - 8:00 PM)">
                                <option value="18:00" {% if registration.preferred_time == '18:00' %}selected{% endif %}>6:00 PM</option>
                                <option value="18:30" {% if registration.preferred_time == '18:30' %}selected{% endif %}>6:30 PM</option>
                                <option value="19:00" {% if registration.preferred_time == '19:00' %}selected{% endif %}>7:00 PM</option>
                                <option value="19:30" {% if registration.preferred_time == '19:30' %}selected{% endif %}>7:30 PM</option>
                            </optgroup>
                        </select>
                    </div>
                </div>
                
                <div class="mt-4">
                    <label class="block text-sm font-medium text-gray-700 mb-2">
                        Chief Complaint <span class="text-red-500">*</span>
                    </label>
                    <textarea name="chief_complaint" rows="3" required class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500 resize-vertical">{{ registration.chief_complaint }}</textarea>
                </div>
                
                <div class="mt-4">
                    <label class="block text-sm font-medium text-gray-700 mb-2">
                        Symptoms (Optional)
                    </label>
                    <textarea name="symptoms" rows="2" class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500 resize-vertical">{{ registration.symptoms }}</textarea>
                </div>
            </div>

            <!-- Additional Options -->
            <div>
                <h2 class="text-lg font-medium text-gray-900 mb-4">Additional Options</h2>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">
                            Payment Method
                        </label>
                        <select name="payment_method" class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500 bg-white">
                            <option value="cash" {% if registration.payment_method == 'cash' %}selected{% endif %}>Cash</option>
                            <option value="card" {% if registration.payment_method == 'card' %}selected{% endif %}>Card</option>
                            <option value="upi" {% if registration.payment_method == 'upi' %}selected{% endif %}>UPI</option>
                            <option value="insurance" {% if registration.payment_method == 'insurance' %}selected{% endif %}>Insurance</option>
                        </select>
                    </div>
                    
                    <div class="flex items-center">
                        <input type="checkbox" name="is_emergency" id="is_emergency" {% if registration.is_emergency %}checked{% endif %} class="h-4 w-4 text-red-600 focus:ring-red-500 border-gray-300 rounded">
                        <label for="is_emergency" class="ml-2 block text-sm font-medium text-red-700">
                            Emergency Case (Priority Treatment)
                        </label>
                    </div>
                </div>
            </div>

            <!-- Current Information Display -->
            <div class="bg-gray-50 rounded-lg p-4">
                <h3 class="text-sm font-medium text-gray-900 mb-2">Current Registration Information</h3>
                <div class="grid grid-cols-2 md:grid-cols-4 gap-4 text-xs text-gray-600">
                    <div>
                        <span class="font-medium">OPD Number:</span><br>
                        {{ registration.opd_number }}
                    </div>
                    <div>
                        <span class="font-medium">Doctor:</span><br>
                        Dr. {{ registration.doctor.user.get_full_name }}
                    </div>
                    <div>
                        <span class="font-medium">Queue Number:</span><br>
                        #{{ registration.queue_number }}
                    </div>
                    <div>
                        <span class="font-medium">Registration Date:</span><br>
                        {{ registration.registration_date|date:"M d, Y" }}
                    </div>
                </div>
            </div>

            <!-- Form Actions -->
            <div class="flex justify-end space-x-3 pt-6 border-t border-gray-200">
                <a href="{% url 'appointments:opd_detail' registration.id %}" class="px-6 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
                    Cancel
                </a>
                <button type="submit" class="px-6 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700">
                    <i class="fas fa-save mr-2"></i>
                    Update Registration
                </button>
            </div>
        </form>
    </div>

    <!-- Information Panel -->
    <div class="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
        <div class="flex">
            <div class="flex-shrink-0">
                <i class="fas fa-exclamation-triangle text-yellow-400"></i>
            </div>
            <div class="ml-3">
                <h3 class="text-sm font-medium text-yellow-800">Important Notes</h3>
                <div class="mt-2 text-sm text-yellow-700">
                    <ul class="list-disc list-inside space-y-1">
                        <li>Doctor assignment and OPD number cannot be changed</li>
                        <li>Changes will be reflected immediately in the system</li>
                        <li>Emergency status affects queue priority</li>
                        <li>All required fields must be filled</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
