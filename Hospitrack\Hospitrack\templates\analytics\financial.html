{% extends 'base.html' %}
{% load static %}

{% block title %}Financial Analytics{% endblock %}

{% block extra_css %}
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<style>
    .metric-card {
        background: white;
        border-radius: 12px;
        padding: 1.5rem;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        border-left: 4px solid #10b981;
    }
    .metric-value {
        font-size: 2rem;
        font-weight: bold;
        color: #1f2937;
    }
    .metric-label {
        color: #6b7280;
        font-size: 0.9rem;
    }
    .chart-container {
        background: white;
        border-radius: 12px;
        padding: 1.5rem;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        margin-bottom: 2rem;
    }
    .nav-analytics {
        background: #1f2937;
        padding: 1rem;
        border-radius: 8px;
        margin-bottom: 2rem;
    }
    .nav-analytics a {
        color: #d1d5db;
        text-decoration: none;
        padding: 0.5rem 1rem;
        border-radius: 6px;
        margin-right: 0.5rem;
        transition: all 0.3s;
    }
    .nav-analytics a:hover {
        background: #374151;
        color: white;
    }
    .nav-analytics a.active {
        background: #10b981;
        color: white;
    }
</style>
{% endblock %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <!-- Header -->
    <div class="mb-8">
        <h1 class="text-4xl font-bold text-gray-900 mb-2">💰 Financial Analytics</h1>
        <p class="text-gray-600">Track revenue, costs, and profitability across all departments</p>
    </div>

    <!-- Analytics Navigation -->
    <div class="nav-analytics">
        <a href="{% url 'analytics:dashboard' %}">📊 Overview</a>
        <a href="{% url 'analytics:patient_flow' %}">🚶 Patient Flow</a>
        <a href="{% url 'analytics:financial' %}" class="active">💰 Financial</a>
        <a href="{% url 'analytics:bed_utilization' %}">🛏️ Bed Utilization</a>
        <a href="{% url 'analytics:staff_efficiency' %}">👥 Staff Efficiency</a>
        <a href="{% url 'analytics:quality' %}">⭐ Quality Metrics</a>
    </div>

    <!-- Filters -->
    <div class="bg-white p-4 rounded-lg shadow mb-6">
        <form method="get" class="flex items-center space-x-4">
            <div>
                <label class="block text-sm font-medium text-gray-700">Start Date</label>
                <input type="date" name="start_date" value="{{ start_date|date:'Y-m-d' }}" 
                       class="mt-1 block w-full rounded-md border-gray-300 shadow-sm">
            </div>
            <div>
                <label class="block text-sm font-medium text-gray-700">End Date</label>
                <input type="date" name="end_date" value="{{ end_date|date:'Y-m-d' }}" 
                       class="mt-1 block w-full rounded-md border-gray-300 shadow-sm">
            </div>
            <div class="pt-6">
                <button type="submit" class="bg-green-600 text-white px-4 py-2 rounded-md hover:bg-green-700">
                    Apply Filters
                </button>
            </div>
        </form>
    </div>

    <!-- Summary Metrics -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        <div class="metric-card">
            <div class="metric-value">${{ totals.total_revenue|default:0|floatformat:0 }}</div>
            <div class="metric-label">Total Revenue</div>
        </div>
        <div class="metric-card" style="border-left-color: #ef4444;">
            <div class="metric-value">${{ totals.total_costs|default:0|floatformat:0 }}</div>
            <div class="metric-label">Total Costs</div>
        </div>
        <div class="metric-card" style="border-left-color: #3b82f6;">
            <div class="metric-value">${{ totals.total_profit|default:0|floatformat:0 }}</div>
            <div class="metric-label">Net Profit</div>
        </div>
        <div class="metric-card" style="border-left-color: #8b5cf6;">
            <div class="metric-value">{{ avg_profit_margin }}%</div>
            <div class="metric-label">Avg Profit Margin</div>
        </div>
    </div>

    <!-- Charts -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
        <!-- Revenue vs Costs -->
        <div class="chart-container">
            <h3 class="text-xl font-semibold mb-4">📈 Revenue vs Costs Trend</h3>
            <canvas id="revenueCostsChart" height="300"></canvas>
        </div>

        <!-- Revenue Breakdown -->
        <div class="chart-container">
            <h3 class="text-xl font-semibold mb-4">🥧 Revenue Breakdown</h3>
            <canvas id="revenueBreakdownChart" height="300"></canvas>
        </div>
    </div>

    <!-- Profit Trend -->
    <div class="chart-container">
        <h3 class="text-xl font-semibold mb-4">💹 Profit Trend</h3>
        <canvas id="profitChart" height="200"></canvas>
    </div>

    <!-- Revenue Sources Detail -->
    <div class="chart-container">
        <h3 class="text-xl font-semibold mb-4">💰 Revenue Sources Over Time</h3>
        <canvas id="revenueSourcesChart" height="300"></canvas>
    </div>
</div>

<script>
// Chart data from Django
const chartData = {{ chart_data|safe }};
const revenueBreakdown = {{ revenue_breakdown|safe }};

// Revenue vs Costs Chart
const revenueCostsCtx = document.getElementById('revenueCostsChart').getContext('2d');
new Chart(revenueCostsCtx, {
    type: 'line',
    data: {
        labels: chartData.dates,
        datasets: [
            {
                label: 'Revenue',
                data: chartData.revenue,
                borderColor: '#10b981',
                backgroundColor: 'rgba(16, 185, 129, 0.1)',
                tension: 0.4,
                fill: true
            },
            {
                label: 'Costs',
                data: chartData.costs,
                borderColor: '#ef4444',
                backgroundColor: 'rgba(239, 68, 68, 0.1)',
                tension: 0.4,
                fill: true
            }
        ]
    },
    options: {
        responsive: true,
        maintainAspectRatio: false,
        scales: {
            y: {
                beginAtZero: true,
                title: {
                    display: true,
                    text: 'Amount ($)'
                }
            }
        },
        plugins: {
            legend: {
                display: true,
                position: 'top'
            }
        }
    }
});

// Revenue Breakdown Pie Chart
const revenueBreakdownCtx = document.getElementById('revenueBreakdownChart').getContext('2d');
new Chart(revenueBreakdownCtx, {
    type: 'doughnut',
    data: {
        labels: ['Consultation', 'Bed Revenue', 'Procedures'],
        datasets: [{
            data: [revenueBreakdown.consultation, revenueBreakdown.bed, revenueBreakdown.procedure],
            backgroundColor: [
                '#3b82f6',
                '#10b981',
                '#f59e0b'
            ],
            borderWidth: 2,
            borderColor: '#ffffff'
        }]
    },
    options: {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
            legend: {
                display: true,
                position: 'bottom'
            }
        }
    }
});

// Profit Chart
const profitCtx = document.getElementById('profitChart').getContext('2d');
new Chart(profitCtx, {
    type: 'bar',
    data: {
        labels: chartData.dates,
        datasets: [
            {
                label: 'Net Profit',
                data: chartData.profit,
                backgroundColor: chartData.profit.map(value => value >= 0 ? 'rgba(16, 185, 129, 0.8)' : 'rgba(239, 68, 68, 0.8)'),
                borderColor: chartData.profit.map(value => value >= 0 ? '#10b981' : '#ef4444'),
                borderWidth: 1
            }
        ]
    },
    options: {
        responsive: true,
        maintainAspectRatio: false,
        scales: {
            y: {
                beginAtZero: true,
                title: {
                    display: true,
                    text: 'Profit ($)'
                }
            }
        },
        plugins: {
            legend: {
                display: false
            }
        }
    }
});

// Revenue Sources Chart
const revenueSourcesCtx = document.getElementById('revenueSourcesChart').getContext('2d');
new Chart(revenueSourcesCtx, {
    type: 'line',
    data: {
        labels: chartData.dates,
        datasets: [
            {
                label: 'Consultation Revenue',
                data: chartData.consultation_revenue,
                borderColor: '#3b82f6',
                backgroundColor: 'rgba(59, 130, 246, 0.1)',
                tension: 0.4
            },
            {
                label: 'Bed Revenue',
                data: chartData.bed_revenue,
                borderColor: '#10b981',
                backgroundColor: 'rgba(16, 185, 129, 0.1)',
                tension: 0.4
            },
            {
                label: 'Procedure Revenue',
                data: chartData.procedure_revenue,
                borderColor: '#f59e0b',
                backgroundColor: 'rgba(245, 158, 11, 0.1)',
                tension: 0.4
            }
        ]
    },
    options: {
        responsive: true,
        maintainAspectRatio: false,
        scales: {
            y: {
                beginAtZero: true,
                title: {
                    display: true,
                    text: 'Revenue ($)'
                }
            }
        },
        plugins: {
            legend: {
                display: true,
                position: 'top'
            }
        }
    }
});
</script>
{% endblock %}
