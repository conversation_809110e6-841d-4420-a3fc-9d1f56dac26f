from django.core.management.base import BaseCommand
from django.contrib.auth import get_user_model
from accounts.models import Doctor, Nurse, Admin
from patients.models import Patient
from beds.models import Ward, Bed
from staff.models import Department, StaffMember
from datetime import date, datetime, time

User = get_user_model()

class Command(BaseCommand):
    help = 'Set up sample data for HospiTrack'

    def handle(self, *args, **options):
        self.stdout.write('Setting up sample data...')
        
        # Update admin user
        try:
            admin_user = User.objects.get(username='admin')
            admin_user.user_type = 'admin'
            admin_user.first_name = 'Admin'
            admin_user.last_name = 'User'
            admin_user.save()
            
            # Create Admin profile
            Admin.objects.get_or_create(
                user=admin_user,
                defaults={'employee_id': 'ADM001'}
            )
            self.stdout.write('✓ Admin user updated')
        except User.DoesNotExist:
            self.stdout.write('Admin user not found')

        # Create sample doctor
        doctor_user, created = User.objects.get_or_create(
            username='doctor',
            defaults={
                'email': '<EMAIL>',
                'first_name': '<PERSON>',
                'last_name': '<PERSON>',
                'user_type': 'doctor'
            }
        )
        if created:
            doctor_user.set_password('doctor123')
            doctor_user.save()
        
        Doctor.objects.get_or_create(
            user=doctor_user,
            defaults={
                'specialization': 'General Medicine',
                'license_number': 'DOC001',
                'department': 'General Medicine',
                'consultation_fee': 500.00,
                'available_from': time(9, 0),
                'available_to': time(17, 0)
            }
        )
        self.stdout.write('✓ Doctor created')

        # Create sample nurse
        nurse_user, created = User.objects.get_or_create(
            username='nurse',
            defaults={
                'email': '<EMAIL>',
                'first_name': 'Mary',
                'last_name': 'Johnson',
                'user_type': 'nurse'
            }
        )
        if created:
            nurse_user.set_password('nurse123')
            nurse_user.save()
        
        Nurse.objects.get_or_create(
            user=nurse_user,
            defaults={
                'department': 'General Ward',
                'shift': 'morning',
                'license_number': 'NUR001'
            }
        )
        self.stdout.write('✓ Nurse created')

        # Create departments
        departments = [
            'General Medicine', 'Emergency', 'Surgery', 'Pediatrics', 
            'Cardiology', 'Orthopedics', 'Administration'
        ]
        for dept_name in departments:
            Department.objects.get_or_create(name=dept_name)
        self.stdout.write('✓ Departments created')

        # Create wards
        wards_data = [
            {'name': 'General Ward A', 'ward_type': 'general', 'floor': 1, 'capacity': 20},
            {'name': 'General Ward B', 'ward_type': 'general', 'floor': 2, 'capacity': 20},
            {'name': 'ICU', 'ward_type': 'icu', 'floor': 3, 'capacity': 10},
            {'name': 'Emergency Ward', 'ward_type': 'emergency', 'floor': 1, 'capacity': 15},
            {'name': 'Private Ward', 'ward_type': 'private', 'floor': 4, 'capacity': 8},
        ]
        
        for ward_data in wards_data:
            ward, created = Ward.objects.get_or_create(
                name=ward_data['name'],
                defaults=ward_data
            )
            
            # Create beds for each ward
            if created:
                for i in range(1, ward_data['capacity'] + 1):
                    Bed.objects.create(
                        bed_number=f"{i:02d}",
                        ward=ward,
                        bed_type='Standard' if ward_data['ward_type'] != 'icu' else 'ICU',
                        daily_rate=1000.00 if ward_data['ward_type'] != 'private' else 2500.00
                    )
        
        self.stdout.write('✓ Wards and beds created')

        # Create sample patients
        patients_data = [
            {
                'patient_id': 'PAT001',
                'first_name': 'Alice',
                'last_name': 'Brown',
                'date_of_birth': date(1985, 5, 15),
                'gender': 'F',
                'blood_group': 'A+',
                'phone': '**********',
                'email': '<EMAIL>',
                'address': '123 Main St, City',
                'emergency_contact_name': 'Bob Brown',
                'emergency_contact_phone': '**********',
                'emergency_contact_relation': 'Husband'
            },
            {
                'patient_id': 'PAT002',
                'first_name': 'David',
                'last_name': 'Wilson',
                'date_of_birth': date(1978, 12, 3),
                'gender': 'M',
                'blood_group': 'O+',
                'phone': '**********',
                'email': '<EMAIL>',
                'address': '456 Oak Ave, City',
                'emergency_contact_name': 'Sarah Wilson',
                'emergency_contact_phone': '**********',
                'emergency_contact_relation': 'Wife'
            }
        ]
        
        for patient_data in patients_data:
            Patient.objects.get_or_create(
                patient_id=patient_data['patient_id'],
                defaults=patient_data
            )
        
        self.stdout.write('✓ Sample patients created')
        self.stdout.write(self.style.SUCCESS('Sample data setup completed!'))
        self.stdout.write('\nLogin credentials:')
        self.stdout.write('Admin: admin / admin123')
        self.stdout.write('Doctor: doctor / doctor123')
        self.stdout.write('Nurse: nurse / nurse123')
