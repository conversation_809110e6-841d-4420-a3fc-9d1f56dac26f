#!/usr/bin/env python
"""
Manual test for OPD scheduling functionality
"""
import os
import sys
import django
from django.test import Client

# Setup Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'hospitrack.settings')
django.setup()

from accounts.models import Doctor
from patients.models import Patient
from appointments.models import PatientOPDSchedule

def test_opd_manual():
    """Manual test of OPD scheduling"""
    print("🔧 Manual OPD Scheduling Test")
    print("=" * 50)
    
    client = Client()
    
    # Login as admin
    login_success = client.login(username='admin', password='admin123')
    if not login_success:
        print("   ❌ Admin login failed")
        return False
    
    print("   ✅ Admin logged in successfully")
    
    # Get doctor and patient
    doctor = Doctor.objects.first()
    patient = Patient.objects.first()
    
    if not doctor or not patient:
        print("   ❌ Missing doctor or patient")
        return False
    
    print(f"   📋 Doctor: Dr. {doctor.user.get_full_name()} (User ID: {doctor.user.id})")
    print(f"   📋 Patient: {patient.full_name} (ID: {patient.id})")
    
    # Test GET request first
    response = client.get('/appointments/opd-schedule/create/')
    if response.status_code == 200:
        print("   ✅ OPD create form accessible")
    else:
        print(f"   ❌ OPD create form failed: {response.status_code}")
        return False
    
    # Test POST request
    from datetime import date, timedelta
    tomorrow = date.today() + timedelta(days=1)
    
    form_data = {
        'patient': patient.id,
        'doctor': doctor.user.id,
        'schedule_date': tomorrow.strftime('%Y-%m-%d'),
        'time_slot': '09:00',
        'reason': 'Test consultation',
        'special_instructions': 'Manual test'
    }
    
    print(f"   📋 Form data: {form_data}")
    
    response = client.post('/appointments/opd-schedule/create/', form_data)
    print(f"   📋 Response status: {response.status_code}")
    
    if response.status_code == 302:
        print("   ✅ OPD schedule created successfully")
        
        # Check if schedule was created
        schedule = PatientOPDSchedule.objects.filter(
            doctor=doctor,
            patient=patient,
            schedule_date=tomorrow
        ).first()
        
        if schedule:
            print(f"   ✅ Schedule found in database: {schedule.schedule_id}")
            return True
        else:
            print("   ❌ Schedule not found in database")
            return False
            
    elif response.status_code == 200:
        print("   ⚠️  Form returned with validation errors")
        # Print form errors if any
        content = response.content.decode()
        if 'error' in content.lower():
            print("   📋 Form contains errors")
        return False
    else:
        print(f"   ❌ Unexpected response: {response.status_code}")
        return False

if __name__ == '__main__':
    try:
        success = test_opd_manual()
        if success:
            print("\n🎉 OPD scheduling is working correctly!")
        else:
            print("\n❌ OPD scheduling needs attention")
        sys.exit(0 if success else 1)
    except Exception as e:
        print(f"❌ Test execution failed: {str(e)}")
        sys.exit(1)