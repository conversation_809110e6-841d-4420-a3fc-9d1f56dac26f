{% extends 'base.html' %}

{% block title %}OPD Queue - HospiTrack{% endblock %}

{% block content %}
<div class="space-y-6">
    <!-- Header -->
    <div class="bg-white shadow rounded-lg p-6">
        <div class="flex justify-between items-center">
            <div>
                {% if user.user_type == 'doctor' %}
                <h1 class="text-3xl font-bold text-gray-900">My OPD Queue</h1>
                <p class="text-gray-600 mt-2">Your outpatient registrations for today - {{ today|date:"F d, Y" }}</p>
                {% else %}
                <h1 class="text-3xl font-bold text-gray-900">OPD Queue</h1>
                <p class="text-gray-600 mt-2">Today's outpatient registrations - {{ today|date:"F d, Y" }}</p>
                {% endif %}
            </div>
            <div class="flex space-x-3">
                {% if user.user_type == 'admin' %}
                <a href="{% url 'appointments:opd_registration' %}" class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-green-600 hover:bg-green-700">
                    <i class="fas fa-user-plus mr-2"></i>
                    New OPD Registration
                </a>
                {% endif %}
                <a href="{% url 'appointments:opd_schedule' %}" class="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
                    <i class="fas fa-calendar mr-2"></i>
                    OPD Schedule
                </a>
            </div>
        </div>
    </div>

    <!-- Statistics -->
    <div class="grid grid-cols-1 md:grid-cols-4 gap-6">
        <div class="bg-white overflow-hidden shadow rounded-lg">
            <div class="p-5">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <i class="fas fa-users text-3xl text-blue-500"></i>
                    </div>
                    <div class="ml-5 w-0 flex-1">
                        <dl>
                            <dt class="text-sm font-medium text-gray-500 truncate">Total Registrations</dt>
                            <dd class="text-3xl font-bold text-gray-900">{{ total_registrations }}</dd>
                        </dl>
                    </div>
                </div>
            </div>
        </div>

        <div class="bg-white overflow-hidden shadow rounded-lg">
            <div class="p-5">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <i class="fas fa-clock text-3xl text-yellow-500"></i>
                    </div>
                    <div class="ml-5 w-0 flex-1">
                        <dl>
                            <dt class="text-sm font-medium text-gray-500 truncate">Waiting</dt>
                            <dd class="text-3xl font-bold text-yellow-600">{{ waiting_count }}</dd>
                        </dl>
                    </div>
                </div>
            </div>
        </div>

        <div class="bg-white overflow-hidden shadow rounded-lg">
            <div class="p-5">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <i class="fas fa-user-md text-3xl text-green-500"></i>
                    </div>
                    <div class="ml-5 w-0 flex-1">
                        <dl>
                            <dt class="text-sm font-medium text-gray-500 truncate">Consulting</dt>
                            <dd class="text-3xl font-bold text-green-600">{{ consulting_count }}</dd>
                        </dl>
                    </div>
                </div>
            </div>
        </div>

        <div class="bg-white overflow-hidden shadow rounded-lg">
            <div class="p-5">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <i class="fas fa-check-circle text-3xl text-purple-500"></i>
                    </div>
                    <div class="ml-5 w-0 flex-1">
                        <dl>
                            <dt class="text-sm font-medium text-gray-500 truncate">Completed</dt>
                            <dd class="text-3xl font-bold text-purple-600">{{ completed_count }}</dd>
                        </dl>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Doctor-wise Queue -->
    {% if doctors_queue %}
    <div class="space-y-6">
        {% for doctor_name, queue_data in doctors_queue.items %}
        <div class="bg-white shadow rounded-lg p-6">
            <div class="flex justify-between items-center mb-4">
                <div>
                    <h2 class="text-lg font-medium text-gray-900">Dr. {{ doctor_name }}</h2>
                    <p class="text-sm text-gray-600">{{ queue_data.doctor.specialization }}</p>
                </div>
                <div class="flex space-x-4 text-sm">
                    <span class="px-2 py-1 bg-yellow-100 text-yellow-800 rounded-full">{{ queue_data.waiting }} Waiting</span>
                    <span class="px-2 py-1 bg-green-100 text-green-800 rounded-full">{{ queue_data.consulting }} Consulting</span>
                    <span class="px-2 py-1 bg-purple-100 text-purple-800 rounded-full">{{ queue_data.completed }} Completed</span>
                </div>
            </div>
            
            <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-gray-200">
                    <thead class="bg-gray-50">
                        <tr>
                            <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Queue #</th>
                            <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">OPD Number</th>
                            <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Patient</th>
                            <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Age/Gender</th>
                            <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Chief Complaint</th>
                            <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Time</th>
                            <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                            <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200">
                        {% for registration in queue_data.registrations %}
                        <tr class="hover:bg-gray-50 {% if registration.is_emergency %}bg-red-50{% endif %}">
                            <td class="px-4 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                                {{ registration.queue_number }}
                                {% if registration.is_emergency %}
                                <span class="ml-1 text-red-500" title="Emergency">⚡</span>
                                {% endif %}
                            </td>
                            <td class="px-4 py-4 whitespace-nowrap text-sm text-gray-900">{{ registration.opd_number }}</td>
                            <td class="px-4 py-4 whitespace-nowrap">
                                <div class="text-sm font-medium text-gray-900">{{ registration.patient_name }}</div>
                                <div class="text-sm text-gray-500">{{ registration.phone }}</div>
                            </td>
                            <td class="px-4 py-4 whitespace-nowrap text-sm text-gray-900">
                                {{ registration.age }}Y / {{ registration.get_gender_display }}
                            </td>
                            <td class="px-4 py-4 text-sm text-gray-900">
                                <div class="max-w-xs truncate" title="{{ registration.chief_complaint }}">
                                    {{ registration.chief_complaint }}
                                </div>
                            </td>
                            <td class="px-4 py-4 whitespace-nowrap text-sm text-gray-500">
                                {{ registration.get_preferred_time_display }}
                            </td>
                            <td class="px-4 py-4 whitespace-nowrap">
                                <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full 
                                    {% if registration.status == 'registered' %}bg-blue-100 text-blue-800
                                    {% elif registration.status == 'waiting' %}bg-yellow-100 text-yellow-800
                                    {% elif registration.status == 'consulting' %}bg-green-100 text-green-800
                                    {% elif registration.status == 'completed' %}bg-purple-100 text-purple-800
                                    {% elif registration.status == 'cancelled' %}bg-red-100 text-red-800
                                    {% else %}bg-gray-100 text-gray-800{% endif %}">
                                    {{ registration.get_status_display }}
                                </span>
                            </td>
                            <td class="px-4 py-4 whitespace-nowrap text-sm font-medium">
                                <div class="flex space-x-2">
                                    <a href="{% url 'appointments:opd_detail' registration.id %}" class="text-blue-600 hover:text-blue-900 text-xs">
                                        View Details
                                    </a>
                                    {% if registration.status != 'cancelled' and registration.status != 'completed' and user.user_type == 'admin' %}
                                    <a href="{% url 'appointments:opd_cancel' registration.id %}" class="text-red-600 hover:text-red-900 text-xs">
                                        Cancel
                                    </a>
                                    {% endif %}
                                </div>
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        </div>
        {% endfor %}
    </div>
    {% else %}
    <div class="bg-white shadow rounded-lg p-12">
        <div class="text-center">
            {% if user.user_type == 'doctor' %}
            <i class="fas fa-stethoscope text-4xl text-blue-400 mb-4"></i>
            <h3 class="text-lg font-medium text-gray-900 mb-2">No Patients Assigned Today</h3>
            <p class="text-gray-500 mb-4">You don't have any OPD patients assigned for today. Patients will appear here when the admin registers them for your consultation.</p>
            <div class="mt-6">
                <a href="{% url 'doctor_dashboard' %}" class="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
                    <i class="fas fa-arrow-left mr-2"></i>
                    Back to Dashboard
                </a>
            </div>
            {% else %}
            <i class="fas fa-calendar-times text-4xl text-gray-400 mb-4"></i>
            <h3 class="text-lg font-medium text-gray-900 mb-2">No OPD Registrations Today</h3>
            <p class="text-gray-500 mb-4">No patients have registered for OPD consultation today.</p>
            <a href="{% url 'appointments:opd_registration' %}" class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-green-600 hover:bg-green-700">
                <i class="fas fa-user-plus mr-2"></i>
                Register First Patient
            </a>
            {% endif %}
        </div>
    </div>
    {% endif %}

    <!-- Legend -->
    <div class="bg-white shadow rounded-lg p-4">
        <h3 class="text-sm font-medium text-gray-900 mb-3">Status Legend</h3>
        <div class="grid grid-cols-2 md:grid-cols-5 gap-3 text-xs">
            <div class="flex items-center">
                <span class="px-2 py-1 bg-blue-100 text-blue-800 rounded-full mr-2">Registered</span>
                <span class="text-gray-600">Just registered</span>
            </div>
            <div class="flex items-center">
                <span class="px-2 py-1 bg-yellow-100 text-yellow-800 rounded-full mr-2">Waiting</span>
                <span class="text-gray-600">In queue</span>
            </div>
            <div class="flex items-center">
                <span class="px-2 py-1 bg-green-100 text-green-800 rounded-full mr-2">Consulting</span>
                <span class="text-gray-600">With doctor</span>
            </div>
            <div class="flex items-center">
                <span class="px-2 py-1 bg-purple-100 text-purple-800 rounded-full mr-2">Completed</span>
                <span class="text-gray-600">Consultation done</span>
            </div>
            <div class="flex items-center">
                <span class="text-red-500 mr-2">⚡</span>
                <span class="text-gray-600">Emergency case</span>
            </div>
        </div>
    </div>
</div>
{% endblock %}
