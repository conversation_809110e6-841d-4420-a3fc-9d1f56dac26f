{% extends 'base.html' %}

{% block title %}Today's Appointments - HospiTrack{% endblock %}

{% block content %}
<div class="space-y-6">
    <!-- Header -->
    <div class="bg-white shadow rounded-lg p-6">
        <div class="flex justify-between items-center">
            <div>
                <h1 class="text-3xl font-bold text-gray-900">Today's Appointments</h1>
                <p class="text-gray-600 mt-2">{{ appointments.count }} appointments scheduled for {{ "today"|date:"F d, Y" }}</p>
            </div>
            {% if user.user_type == 'admin' %}
            <a href="{% url 'appointments:schedule' %}" class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700">
                <i class="fas fa-calendar-plus mr-2"></i>
                Schedule Appointment
            </a>
            {% endif %}
        </div>
    </div>

    <!-- Quick Stats -->
    <div class="grid grid-cols-1 md:grid-cols-4 gap-6">
        <div class="bg-white overflow-hidden shadow rounded-lg">
            <div class="p-5">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <i class="fas fa-calendar-day text-3xl text-blue-500"></i>
                    </div>
                    <div class="ml-5 w-0 flex-1">
                        <dl>
                            <dt class="text-sm font-medium text-gray-500 truncate">Total Today</dt>
                            <dd class="text-3xl font-bold text-gray-900">{{ appointments.count }}</dd>
                        </dl>
                    </div>
                </div>
            </div>
        </div>

        <div class="bg-white overflow-hidden shadow rounded-lg">
            <div class="p-5">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <i class="fas fa-check-circle text-3xl text-green-500"></i>
                    </div>
                    <div class="ml-5 w-0 flex-1">
                        <dl>
                            <dt class="text-sm font-medium text-gray-500 truncate">Completed</dt>
                            <dd class="text-3xl font-bold text-green-600">0</dd>
                        </dl>
                    </div>
                </div>
            </div>
        </div>

        <div class="bg-white overflow-hidden shadow rounded-lg">
            <div class="p-5">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <i class="fas fa-clock text-3xl text-yellow-500"></i>
                    </div>
                    <div class="ml-5 w-0 flex-1">
                        <dl>
                            <dt class="text-sm font-medium text-gray-500 truncate">Pending</dt>
                            <dd class="text-3xl font-bold text-yellow-600">{{ appointments.count }}</dd>
                        </dl>
                    </div>
                </div>
            </div>
        </div>

        <div class="bg-white overflow-hidden shadow rounded-lg">
            <div class="p-5">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <i class="fas fa-times-circle text-3xl text-red-500"></i>
                    </div>
                    <div class="ml-5 w-0 flex-1">
                        <dl>
                            <dt class="text-sm font-medium text-gray-500 truncate">Cancelled</dt>
                            <dd class="text-3xl font-bold text-red-600">0</dd>
                        </dl>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Appointments Timeline -->
    {% if appointments %}
    <div class="bg-white shadow rounded-lg p-6">
        <h2 class="text-xl font-bold text-gray-900 mb-6">Appointment Schedule</h2>
        
        <div class="space-y-4">
            {% for appointment in appointments %}
            <div class="flex items-start space-x-4 p-4 border border-gray-200 rounded-lg hover:bg-gray-50">
                <!-- Time -->
                <div class="flex-shrink-0 w-20 text-center">
                    <div class="text-lg font-bold text-gray-900">{{ appointment.appointment_time|time:"H:i" }}</div>
                    <div class="text-xs text-gray-500">{{ appointment.appointment_time|time:"A" }}</div>
                </div>

                <!-- Appointment Details -->
                <div class="flex-1 min-w-0">
                    <div class="flex items-center justify-between">
                        <div>
                            <h3 class="text-lg font-medium text-gray-900">{{ appointment.patient.full_name }}</h3>
                            <p class="text-sm text-gray-600">{{ appointment.patient.patient_id }} • {{ appointment.patient.age }} years • {{ appointment.patient.get_gender_display }}</p>
                        </div>
                        <div class="flex items-center space-x-2">
                            <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full 
                                {% if appointment.status == 'completed' %}bg-green-100 text-green-800
                                {% elif appointment.status == 'in_progress' %}bg-blue-100 text-blue-800
                                {% elif appointment.status == 'confirmed' %}bg-yellow-100 text-yellow-800
                                {% else %}bg-gray-100 text-gray-800{% endif %}">
                                {{ appointment.get_status_display }}
                            </span>
                        </div>
                    </div>

                    <div class="mt-2 grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
                        <div>
                            <span class="font-medium text-gray-500">Type:</span>
                            <span class="text-gray-900">{{ appointment.get_appointment_type_display }}</span>
                        </div>
                        {% if user.user_type != 'doctor' %}
                        <div>
                            <span class="font-medium text-gray-500">Doctor:</span>
                            <span class="text-gray-900">Dr. {{ appointment.doctor.user.last_name }}</span>
                        </div>
                        {% endif %}
                        <div>
                            <span class="font-medium text-gray-500">Department:</span>
                            <span class="text-gray-900">{{ appointment.doctor.department }}</span>
                        </div>
                    </div>

                    {% if appointment.reason %}
                    <div class="mt-2">
                        <span class="font-medium text-gray-500 text-sm">Reason:</span>
                        <p class="text-sm text-gray-900 mt-1">{{ appointment.reason }}</p>
                    </div>
                    {% endif %}

                    <!-- Patient Contact -->
                    <div class="mt-3 flex items-center space-x-4 text-sm text-gray-500">
                        <div class="flex items-center">
                            <i class="fas fa-phone mr-1"></i>
                            <span>{{ appointment.patient.phone }}</span>
                        </div>
                        {% if appointment.patient.blood_group %}
                        <div class="flex items-center">
                            <i class="fas fa-tint mr-1"></i>
                            <span>{{ appointment.patient.blood_group }}</span>
                        </div>
                        {% endif %}
                    </div>
                </div>

                <!-- Actions -->
                <div class="flex-shrink-0">
                    <div class="flex flex-col space-y-2">
                        <a href="{% url 'patients:detail' appointment.patient.id %}" 
                           class="inline-flex items-center px-3 py-1 border border-gray-300 text-xs font-medium rounded text-gray-700 bg-white hover:bg-gray-50">
                            <i class="fas fa-user mr-1"></i>
                            Patient
                        </a>
                        {% if user.user_type == 'doctor' %}
                        <button onclick="updateAppointmentStatus({{ appointment.id }}, 'in_progress')"
                                class="inline-flex items-center px-3 py-1 border border-transparent text-xs font-medium rounded text-white bg-blue-600 hover:bg-blue-700">
                            <i class="fas fa-play mr-1"></i>
                            Start
                        </button>
                        <button onclick="updateAppointmentStatus({{ appointment.id }}, 'completed')"
                                class="inline-flex items-center px-3 py-1 border border-transparent text-xs font-medium rounded text-white bg-green-600 hover:bg-green-700">
                            <i class="fas fa-check mr-1"></i>
                            Complete
                        </button>
                        {% endif %}
                    </div>
                </div>
            </div>
            {% endfor %}
        </div>
    </div>
    {% else %}
    <div class="bg-white shadow rounded-lg p-12">
        <div class="text-center">
            <i class="fas fa-calendar-times text-6xl text-gray-400 mb-4"></i>
            <h3 class="text-xl font-medium text-gray-900 mb-2">No appointments today</h3>
            <p class="text-gray-500 mb-6">You have a free day! No appointments are scheduled for today.</p>
            {% if user.user_type == 'admin' %}
            <a href="{% url 'appointments:schedule' %}" class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700">
                <i class="fas fa-calendar-plus mr-2"></i>
                Schedule New Appointment
            </a>
            {% endif %}
        </div>
    </div>
    {% endif %}

    <!-- Quick Actions -->
    <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
        <div class="bg-white shadow rounded-lg p-6">
            <h3 class="text-lg font-medium text-gray-900 mb-4">Quick Actions</h3>
            <div class="space-y-3">
                {% if user.user_type == 'admin' %}
                <a href="{% url 'appointments:schedule' %}" class="block w-full bg-blue-600 hover:bg-blue-700 text-white text-center py-2 px-4 rounded-md text-sm font-medium">
                    <i class="fas fa-calendar-plus mr-2"></i>Schedule Appointment
                </a>
                {% endif %}
                <a href="{% url 'appointments:list' %}" class="block w-full bg-gray-600 hover:bg-gray-700 text-white text-center py-2 px-4 rounded-md text-sm font-medium">
                    <i class="fas fa-calendar mr-2"></i>All Appointments
                </a>
                <a href="{% url 'patients:list' %}" class="block w-full bg-green-600 hover:bg-green-700 text-white text-center py-2 px-4 rounded-md text-sm font-medium">
                    <i class="fas fa-users mr-2"></i>View Patients
                </a>
            </div>
        </div>

        <div class="bg-white shadow rounded-lg p-6">
            <h3 class="text-lg font-medium text-gray-900 mb-4">Next Appointment</h3>
            {% if appointments %}
            <div class="text-center">
                <div class="text-2xl font-bold text-blue-600">{{ appointments.first.appointment_time|time:"H:i" }}</div>
                <div class="text-sm text-gray-600">{{ appointments.first.patient.full_name }}</div>
                <div class="text-xs text-gray-500 mt-1">{{ appointments.first.get_appointment_type_display }}</div>
            </div>
            {% else %}
            <div class="text-center text-gray-500">
                <i class="fas fa-calendar-times text-2xl mb-2"></i>
                <p class="text-sm">No upcoming appointments</p>
            </div>
            {% endif %}
        </div>

        <div class="bg-white shadow rounded-lg p-6">
            <h3 class="text-lg font-medium text-gray-900 mb-4">Today's Summary</h3>
            <div class="space-y-2 text-sm">
                <div class="flex justify-between">
                    <span class="text-gray-600">Total Appointments:</span>
                    <span class="font-medium">{{ appointments.count }}</span>
                </div>
                <div class="flex justify-between">
                    <span class="text-gray-600">Completed:</span>
                    <span class="font-medium text-green-600">0</span>
                </div>
                <div class="flex justify-between">
                    <span class="text-gray-600">Remaining:</span>
                    <span class="font-medium text-blue-600">{{ appointments.count }}</span>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
function updateAppointmentStatus(appointmentId, status) {
    // This would typically make an AJAX call to update the appointment status
    fetch(`/appointments/${appointmentId}/update-status/`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value
        },
        body: JSON.stringify({
            status: status
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            location.reload();
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('Failed to update appointment status');
    });
}
</script>
{% endblock %}
