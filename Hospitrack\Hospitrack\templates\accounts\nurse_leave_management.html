{% extends 'base.html' %}

{% block title %}Nurse Leave Management - HospiTrack{% endblock %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="bg-white rounded-lg shadow-md">
        <!-- Header -->
        <div class="px-6 py-4 border-b border-gray-200">
            <div class="flex items-center justify-between">
                <div class="flex items-center">
                    <div class="bg-purple-100 p-3 rounded-full mr-4">
                        <svg class="w-6 h-6 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                        </svg>
                    </div>
                    <div>
                        <h1 class="text-2xl font-bold text-gray-900">Nurse Leave Management</h1>
                        <p class="text-gray-600">Manage leave status for all nurses</p>
                    </div>
                </div>
                <div class="text-sm text-gray-500">
                    Total Nurses: {{ nurses.count }}
                </div>
            </div>
        </div>

        {% if messages %}
            <div class="px-6 py-4">
                {% for message in messages %}
                    <div class="mb-4 p-4 rounded-md {% if message.tags == 'error' %}bg-red-50 text-red-700 border border-red-200{% else %}bg-green-50 text-green-700 border border-green-200{% endif %}">
                        {{ message }}
                    </div>
                {% endfor %}
            </div>
        {% endif %}

        <!-- Statistics -->
        <div class="px-6 py-4 bg-gray-50 border-b border-gray-200">
            <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
                <div class="bg-white p-4 rounded-lg shadow-sm">
                    <div class="flex items-center">
                        <div class="bg-green-100 p-2 rounded-full mr-3">
                            <svg class="w-4 h-4 text-green-600" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                            </svg>
                        </div>
                        <div>
                            <p class="text-sm text-gray-500">Available</p>
                            <p class="text-lg font-semibold text-gray-900">{{ nurses|length|add:"-"|add:nurses|length }}</p>
                        </div>
                    </div>
                </div>
                
                <div class="bg-white p-4 rounded-lg shadow-sm">
                    <div class="flex items-center">
                        <div class="bg-yellow-100 p-2 rounded-full mr-3">
                            <svg class="w-4 h-4 text-yellow-600" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd"></path>
                            </svg>
                        </div>
                        <div>
                            <p class="text-sm text-gray-500">On Leave</p>
                            <p class="text-lg font-semibold text-gray-900">
                                {% for nurse in nurses %}{% if nurse.leave_status != 'available' %}1{% endif %}{% empty %}0{% endfor %}
                            </p>
                        </div>
                    </div>
                </div>
                
                <div class="bg-white p-4 rounded-lg shadow-sm">
                    <div class="flex items-center">
                        <div class="bg-red-100 p-2 rounded-full mr-3">
                            <svg class="w-4 h-4 text-red-600" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd"></path>
                            </svg>
                        </div>
                        <div>
                            <p class="text-sm text-gray-500">Sick Leave</p>
                            <p class="text-lg font-semibold text-gray-900">
                                {% for nurse in nurses %}{% if nurse.leave_status == 'sick_leave' %}1{% endif %}{% empty %}0{% endfor %}
                            </p>
                        </div>
                    </div>
                </div>
                
                <div class="bg-white p-4 rounded-lg shadow-sm">
                    <div class="flex items-center">
                        <div class="bg-blue-100 p-2 rounded-full mr-3">
                            <svg class="w-4 h-4 text-blue-600" fill="currentColor" viewBox="0 0 20 20">
                                <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                            </svg>
                        </div>
                        <div>
                            <p class="text-sm text-gray-500">Vacation</p>
                            <p class="text-lg font-semibold text-gray-900">
                                {% for nurse in nurses %}{% if nurse.leave_status == 'vacation' %}1{% endif %}{% empty %}0{% endfor %}
                            </p>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Nurses Table -->
        <div class="overflow-x-auto">
            <table class="min-w-full divide-y divide-gray-200">
                <thead class="bg-gray-50">
                    <tr>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Nurse</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Specialty</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Shift</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Leave Period</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                    </tr>
                </thead>
                <tbody class="bg-white divide-y divide-gray-200">
                    {% for nurse in nurses %}
                    <tr class="hover:bg-gray-50">
                        <td class="px-6 py-4 whitespace-nowrap">
                            <div class="flex items-center">
                                <div class="flex-shrink-0 h-10 w-10">
                                    <div class="h-10 w-10 rounded-full bg-purple-100 flex items-center justify-center">
                                        <span class="text-sm font-medium text-purple-600">
                                            {{ nurse.user.first_name|first }}{{ nurse.user.last_name|first }}
                                        </span>
                                    </div>
                                </div>
                                <div class="ml-4">
                                    <div class="text-sm font-medium text-gray-900">{{ nurse.user.get_full_name }}</div>
                                    <div class="text-sm text-gray-500">{{ nurse.user.username }}</div>
                                </div>
                            </div>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            <div class="text-sm text-gray-900">{{ nurse.specialty.name|default:nurse.department }}</div>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium
                                {% if nurse.shift == 'morning' %}bg-yellow-100 text-yellow-800
                                {% elif nurse.shift == 'evening' %}bg-orange-100 text-orange-800
                                {% else %}bg-indigo-100 text-indigo-800{% endif %}">
                                {% if nurse.shift == 'morning' %}Morning (6 AM - 2 PM)
                                {% elif nurse.shift == 'evening' %}Evening (2 PM - 10 PM)
                                {% else %}Night (10 PM - 6 AM){% endif %}
                            </span>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium
                                {% if nurse.leave_status == 'available' %}bg-green-100 text-green-800
                                {% elif nurse.leave_status == 'sick_leave' %}bg-red-100 text-red-800
                                {% elif nurse.leave_status == 'vacation' %}bg-blue-100 text-blue-800
                                {% else %}bg-yellow-100 text-yellow-800{% endif %}">
                                {{ nurse.leave_status_display }}
                            </span>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                            {% if nurse.current_leave_info %}
                                <div>{{ nurse.leave_start_date|date:"M d" }} - {{ nurse.leave_end_date|date:"M d, Y" }}</div>
                                {% if nurse.current_leave_info.days_remaining > 0 %}
                                    <div class="text-xs text-gray-500">{{ nurse.current_leave_info.days_remaining }} days left</div>
                                {% endif %}
                            {% else %}
                                <span class="text-gray-400">-</span>
                            {% endif %}
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                            <a href="{% url 'nurse_leave_edit' nurse.user.id %}" 
                               class="text-blue-600 hover:text-blue-900 mr-3">
                                Manage Leave
                            </a>
                        </td>
                    </tr>
                    {% empty %}
                    <tr>
                        <td colspan="6" class="px-6 py-4 text-center text-gray-500">
                            No nurses found.
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
    </div>
</div>
{% endblock %}