from django.core.management.base import BaseCommand
from django.db import transaction
from accounts.models import MedicalSpecialty
from patients.models import Patient
import random

class Command(BaseCommand):
    help = 'Assign existing patients to medical specialties for demonstration'

    def handle(self, *args, **options):
        self.stdout.write(self.style.SUCCESS('Assigning patients to medical specialties...'))
        
        with transaction.atomic():
            # Get all active specialties
            specialties = list(MedicalSpecialty.objects.filter(is_active=True))
            
            # Get all patients without specialty
            patients_without_specialty = Patient.objects.filter(primary_specialty__isnull=True, is_active=True)
            
            self.stdout.write(f'Found {patients_without_specialty.count()} patients without specialty assignment')
            
            assigned_count = 0
            for patient in patients_without_specialty:
                # Randomly assign to a specialty for demonstration
                # In real scenario, this would be based on medical condition
                specialty = random.choice(specialties)
                patient.primary_specialty = specialty
                patient.save()
                assigned_count += 1
                self.stdout.write(f'Assigned {patient.full_name} (ID: {patient.patient_id}) to {specialty.name}')
            
            self.stdout.write(
                self.style.SUCCESS(
                    f'Successfully assigned {assigned_count} patients to specialties'
                )
            )
            
            # Show distribution
            self.stdout.write('\nPatient distribution by specialty:')
            for specialty in specialties:
                count = specialty.patients.filter(is_active=True).count()
                self.stdout.write(f'  {specialty.name}: {count} patients')