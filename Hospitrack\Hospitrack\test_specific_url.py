#!/usr/bin/env python
"""
Test the specific URL that was causing the NoReverseMatch error
"""
import os
import sys
import django
from django.test import Client

# Setup Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'hospitrack.settings')
django.setup()

def test_specific_doctor_edit_url():
    """Test the specific URL that was failing: /doctors/54/edit/"""
    print("🎯 Testing Specific Doctor Edit URL")
    print("=" * 50)
    
    client = Client()
    
    # Login as admin
    login_success = client.login(username='admin', password='admin123')
    if not login_success:
        print("   ❌ Admin login failed")
        return False
    
    print("   ✅ Admin logged in successfully")
    
    # Test the specific URL that was causing the error
    test_url = '/doctors/54/edit/'
    print(f"   🔗 Testing URL: {test_url}")
    
    try:
        response = client.get(test_url)
        
        if response.status_code == 200:
            print("   ✅ URL loads successfully (200 OK)")
            print("   ✅ NoReverseMatch error has been resolved!")
            
            # Check if the page contains expected content
            content = response.content.decode()
            if 'Edit Doctor' in content:
                print("   ✅ Page contains expected 'Edit Doctor' content")
            if 'csrf_token' in content:
                print("   ✅ CSRF token is present in the form")
            
            return True
            
        elif response.status_code == 404:
            print("   ❌ Doctor with ID 54 not found (404)")
            return False
            
        else:
            print(f"   ❌ Unexpected status code: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"   ❌ Exception occurred: {str(e)}")
        return False

def test_form_submission():
    """Test form submission on the specific URL"""
    print(f"\n📝 Testing Form Submission")
    print("=" * 50)
    
    client = Client()
    client.login(username='admin', password='admin123')
    
    test_url = '/doctors/54/edit/'
    
    # Test form submission with sample data
    form_data = {
        'first_name': 'Sarah',
        'last_name': 'Johnson',
        'email': '<EMAIL>',
        'phone': '**********',
        'specialization': 'Emergency Medicine',
        'license_number': 'LIC001',
        'department': 'Emergency',
        'experience_years': '10',
        'consultation_fee': '500.00',
        'qualifications': 'MD, Emergency Medicine Specialist'
    }
    
    try:
        response = client.post(test_url, form_data)
        
        if response.status_code == 302:  # Redirect after successful submission
            print("   ✅ Form submission successful (302 redirect)")
            
            # Check redirect URL
            redirect_url = response.url
            print(f"   ✅ Redirects to: {redirect_url}")
            
            # Test if redirect URL is accessible
            redirect_response = client.get(redirect_url)
            if redirect_response.status_code == 200:
                print("   ✅ Redirect URL is accessible")
                return True
            else:
                print(f"   ❌ Redirect URL failed: {redirect_response.status_code}")
                return False
                
        else:
            print(f"   ❌ Form submission failed: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"   ❌ Form submission exception: {str(e)}")
        return False

def main():
    """Run the specific URL tests"""
    print("🎯 Specific Doctor Edit URL Testing")
    print("=" * 80)
    print("Testing the exact URL that was causing the NoReverseMatch error:")
    print("URL: http://127.0.0.1:8000/doctors/54/edit/")
    print("=" * 80)
    
    # Test URL access
    url_test_passed = test_specific_doctor_edit_url()
    
    # Test form submission
    form_test_passed = test_form_submission()
    
    # Final result
    print(f"\n" + "=" * 80)
    print(f"🎯 FINAL RESULT")
    print(f"=" * 80)
    
    if url_test_passed and form_test_passed:
        print(f"🎉 SUCCESS: The NoReverseMatch error has been completely resolved!")
        print(f"✅ URL Access: Working")
        print(f"✅ Form Submission: Working")
        print(f"✅ Redirect: Working")
        print(f"\nThe doctor edit functionality is now fully operational.")
        return True
    else:
        print(f"❌ PARTIAL SUCCESS: Some issues remain")
        print(f"{'✅' if url_test_passed else '❌'} URL Access: {'Working' if url_test_passed else 'Failed'}")
        print(f"{'✅' if form_test_passed else '❌'} Form Submission: {'Working' if form_test_passed else 'Failed'}")
        return False

if __name__ == '__main__':
    try:
        success = main()
        sys.exit(0 if success else 1)
    except Exception as e:
        print(f"❌ Test execution failed: {str(e)}")
        sys.exit(1)