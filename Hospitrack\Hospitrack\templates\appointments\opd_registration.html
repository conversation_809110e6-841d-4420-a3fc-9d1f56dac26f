{% extends 'base.html' %}

{% block title %}OPD Registration - HospiTrack{% endblock %}

{% block content %}
<div class="space-y-6">
    <!-- Header -->
    <div class="bg-white shadow rounded-lg p-6">
        <div class="flex justify-between items-center">
            <div>
                <h1 class="text-3xl font-bold text-gray-900">OPD Registration</h1>
                <p class="text-gray-600 mt-2">Register for outpatient consultation with specialist doctors</p>
            </div>
            <div class="flex space-x-3">
                <a href="{% url 'appointments:opd_queue' %}" class="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
                    <i class="fas fa-list mr-2"></i>
                    View OPD Queue
                </a>
                <a href="{% url 'appointments:opd_schedule' %}" class="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
                    <i class="fas fa-calendar mr-2"></i>
                    OPD Schedule
                </a>
            </div>
        </div>
    </div>

    <!-- Registration Form -->
    <div class="bg-white shadow rounded-lg p-6">
        <form method="post" class="space-y-6">
            {% csrf_token %}
            
            <!-- Patient Information -->
            <div>
                <h2 class="text-lg font-medium text-gray-900 mb-4">Patient Information</h2>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">
                            Patient Name <span class="text-red-500">*</span>
                        </label>
                        <input type="text" name="patient_name" required class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500" placeholder="Enter full name">
                    </div>
                    
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">
                            Age <span class="text-red-500">*</span>
                        </label>
                        <input type="number" name="age" min="0" max="120" required class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500" placeholder="Age in years">
                    </div>
                    
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">
                            Gender <span class="text-red-500">*</span>
                        </label>
                        <select name="gender" required class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500 bg-white">
                            <option value="">Select Gender</option>
                            {% for value, label in gender_choices %}
                            <option value="{{ value }}">{{ label }}</option>
                            {% endfor %}
                        </select>
                    </div>
                    
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">
                            Phone Number <span class="text-red-500">*</span>
                        </label>
                        <input type="tel" name="phone" required class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500" placeholder="10-digit mobile number">
                    </div>
                </div>
                
                <div class="mt-4">
                    <label class="block text-sm font-medium text-gray-700 mb-2">
                        Address <span class="text-red-500">*</span>
                    </label>
                    <textarea name="address" rows="2" required class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500 resize-vertical" placeholder="Complete address"></textarea>
                </div>
            </div>

            <!-- Medical Information -->
            <div>
                <h2 class="text-lg font-medium text-gray-900 mb-4">Medical Information</h2>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">
                            Select Doctor <span class="text-red-500">*</span>
                        </label>
                        <select name="doctor" required class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500 bg-white" onchange="updateConsultationFee()">
                            <option value="">Choose Specialist Doctor</option>
                            {% for doctor in doctors %}
                            <option value="{{ doctor.user.id }}" data-fee="{{ doctor.consultation_fee }}">
                                Dr. {{ doctor.user.get_full_name }} - {{ doctor.specialization }} (₹{{ doctor.consultation_fee }})
                            </option>
                            {% empty %}
                            <option value="">No doctors available</option>
                            {% endfor %}
                        </select>
                    </div>
                    
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">
                            Visit Type <span class="text-red-500">*</span>
                        </label>
                        <select name="visit_type" required class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500 bg-white">
                            {% for value, label in visit_types %}
                            <option value="{{ value }}">{{ label }}</option>
                            {% endfor %}
                        </select>
                    </div>
                </div>
                
                <div class="mt-4">
                    <label class="block text-sm font-medium text-gray-700 mb-2">
                        Chief Complaint <span class="text-red-500">*</span>
                    </label>
                    <textarea name="chief_complaint" rows="3" required class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500 resize-vertical" placeholder="Main reason for visit (e.g., chest pain, fever, headache)"></textarea>
                </div>
                
                <div class="mt-4">
                    <label class="block text-sm font-medium text-gray-700 mb-2">
                        Symptoms (Optional)
                    </label>
                    <textarea name="symptoms" rows="2" class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500 resize-vertical" placeholder="Additional symptoms or details"></textarea>
                </div>
            </div>

            <!-- Appointment Preferences -->
            <div>
                <h2 class="text-lg font-medium text-gray-900 mb-4">Appointment Preferences</h2>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">
                            Preferred Time <span class="text-red-500">*</span>
                        </label>
                        <select name="preferred_time" required class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500 bg-white">
                            <option value="">Select Preferred Time</option>
                            <!-- Morning Slots -->
                            <optgroup label="Morning OPD (9:00 AM - 12:00 PM)">
                                <option value="09:00">9:00 AM</option>
                                <option value="09:30">9:30 AM</option>
                                <option value="10:00">10:00 AM</option>
                                <option value="10:30">10:30 AM</option>
                                <option value="11:00">11:00 AM</option>
                                <option value="11:30">11:30 AM</option>
                            </optgroup>
                            <!-- Afternoon Slots -->
                            <optgroup label="Afternoon OPD (2:00 PM - 5:00 PM)">
                                <option value="14:00">2:00 PM</option>
                                <option value="14:30">2:30 PM</option>
                                <option value="15:00">3:00 PM</option>
                                <option value="15:30">3:30 PM</option>
                                <option value="16:00">4:00 PM</option>
                                <option value="16:30">4:30 PM</option>
                            </optgroup>
                            <!-- Evening Slots -->
                            <optgroup label="Evening OPD (6:00 PM - 8:00 PM)">
                                <option value="18:00">6:00 PM</option>
                                <option value="18:30">6:30 PM</option>
                                <option value="19:00">7:00 PM</option>
                                <option value="19:30">7:30 PM</option>
                            </optgroup>
                        </select>
                    </div>
                    
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">
                            Payment Method
                        </label>
                        <select name="payment_method" class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500 bg-white">
                            {% for value, label in payment_methods %}
                            <option value="{{ value }}">{{ label }}</option>
                            {% endfor %}
                        </select>
                    </div>
                </div>
                
                <div class="mt-4">
                    <div class="flex items-center">
                        <input type="checkbox" name="is_emergency" id="is_emergency" class="h-4 w-4 text-red-600 focus:ring-red-500 border-gray-300 rounded">
                        <label for="is_emergency" class="ml-2 block text-sm font-medium text-red-700">
                            Emergency Case (Priority Treatment)
                        </label>
                    </div>
                </div>
            </div>

            <!-- Consultation Fee Display -->
            <div class="bg-blue-50 border border-blue-200 rounded-lg p-4">
                <div class="flex items-center">
                    <i class="fas fa-rupee-sign text-blue-600 mr-2"></i>
                    <span class="text-sm font-medium text-blue-800">Consultation Fee: </span>
                    <span id="consultation_fee" class="text-lg font-bold text-blue-900 ml-2">₹0</span>
                </div>
                <p class="text-xs text-blue-600 mt-1">Fee will be collected at the payment counter after registration</p>
            </div>

            <!-- Form Actions -->
            <div class="flex justify-end space-x-3 pt-6 border-t border-gray-200">
                <a href="{% url 'appointments:opd_schedule' %}" class="px-6 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
                    Cancel
                </a>
                <button type="submit" class="px-6 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-green-600 hover:bg-green-700">
                    <i class="fas fa-user-plus mr-2"></i>
                    Register for OPD
                </button>
            </div>
        </form>
    </div>

    <!-- Information Panel -->
    <div class="bg-green-50 border border-green-200 rounded-lg p-4">
        <div class="flex">
            <div class="flex-shrink-0">
                <i class="fas fa-info-circle text-green-400"></i>
            </div>
            <div class="ml-3">
                <h3 class="text-sm font-medium text-green-800">OPD Registration Information</h3>
                <div class="mt-2 text-sm text-green-700">
                    <ul class="list-disc list-inside space-y-1">
                        <li>OPD registration is for outpatient consultation (same day visit)</li>
                        <li>You will receive an OPD number and queue number after registration</li>
                        <li>Please carry a valid ID proof and any previous medical reports</li>
                        <li>Emergency cases will be given priority in the queue</li>
                        <li>Consultation fee must be paid at the payment counter</li>
                        <li>Please arrive 15 minutes before your preferred time</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
function updateConsultationFee() {
    const doctorSelect = document.querySelector('select[name="doctor"]');
    const feeDisplay = document.getElementById('consultation_fee');
    
    if (doctorSelect.value) {
        const selectedOption = doctorSelect.options[doctorSelect.selectedIndex];
        const fee = selectedOption.getAttribute('data-fee');
        feeDisplay.textContent = '₹' + fee;
    } else {
        feeDisplay.textContent = '₹0';
    }
}
</script>
{% endblock %}
