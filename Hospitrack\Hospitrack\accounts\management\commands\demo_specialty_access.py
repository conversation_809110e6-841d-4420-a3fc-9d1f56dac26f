from django.core.management.base import BaseCommand
from django.db import transaction
from accounts.models import User, Doctor, Nurse, MedicalSpecialty
from patients.models import Patient

class Command(BaseCommand):
    help = 'Demonstrate specialty-based access control system'

    def handle(self, *args, **options):
        self.stdout.write(self.style.SUCCESS('=== Specialty-Based Access Control Demo ===\n'))
        
        # Show all specialties
        self.stdout.write(self.style.WARNING('Available Medical Specialties:'))
        specialties = MedicalSpecialty.objects.all()
        for specialty in specialties:
            doctors_count = specialty.doctors.count()
            nurses_count = specialty.nurses.count()
            patients_count = specialty.patients.count()
            self.stdout.write(f'  • {specialty.name}: {doctors_count} doctors, {nurses_count} nurses, {patients_count} patients')
        
        self.stdout.write('\n' + '='*60 + '\n')
        
        # Show doctors by specialty
        self.stdout.write(self.style.WARNING('Doctors by Specialty:'))
        for specialty in specialties:
            doctors = specialty.doctors.all()
            if doctors:
                self.stdout.write(f'\n{specialty.name}:')
                for doctor in doctors:
                    self.stdout.write(f'  • Dr. {doctor.user.get_full_name()} (License: {doctor.license_number})')
        
        self.stdout.write('\n' + '='*60 + '\n')
        
        # Show nurses by specialty
        self.stdout.write(self.style.WARNING('Nurses by Specialty:'))
        for specialty in specialties:
            nurses = specialty.nurses.all()
            if nurses:
                self.stdout.write(f'\n{specialty.name}:')
                for nurse in nurses:
                    shift_display = dict(nurse._meta.get_field('shift').choices).get(nurse.shift, nurse.shift)
                    self.stdout.write(f'  • {nurse.user.get_full_name()} - {shift_display} (License: {nurse.license_number})')
        
        self.stdout.write('\n' + '='*60 + '\n')
        
        # Show patients by specialty
        self.stdout.write(self.style.WARNING('Patients by Specialty:'))
        for specialty in specialties:
            patients = specialty.patients.filter(is_active=True)
            if patients:
                self.stdout.write(f'\n{specialty.name}:')
                for patient in patients:
                    self.stdout.write(f'  • {patient.full_name} (ID: {patient.patient_id})')
        
        self.stdout.write('\n' + '='*60 + '\n')
        
        # Demonstrate access control
        self.stdout.write(self.style.WARNING('Access Control Demonstration:'))
        
        # Get a cardiology doctor and nurse
        cardiology = MedicalSpecialty.objects.filter(name='Cardiology').first()
        if cardiology:
            cardio_doctor = cardiology.doctors.first()
            cardio_nurse = cardiology.nurses.first()
            
            if cardio_doctor and cardio_nurse:
                self.stdout.write(f'\nCardiology Team:')
                self.stdout.write(f'  Doctor: Dr. {cardio_doctor.user.get_full_name()}')
                self.stdout.write(f'  Nurse: {cardio_nurse.user.get_full_name()}')
                
                # Show what patients they can access
                cardio_patients = Patient.objects.filter(primary_specialty=cardiology, is_active=True)
                self.stdout.write(f'\nPatients they can access:')
                for patient in cardio_patients:
                    self.stdout.write(f'  • {patient.full_name} (ID: {patient.patient_id})')
                
                # Show what patients they CANNOT access
                other_patients = Patient.objects.exclude(primary_specialty=cardiology).filter(is_active=True)[:3]
                if other_patients:
                    self.stdout.write(f'\nPatients they CANNOT access (different specialties):')
                    for patient in other_patients:
                        specialty_name = patient.primary_specialty.name if patient.primary_specialty else 'No specialty'
                        self.stdout.write(f'  • {patient.full_name} (ID: {patient.patient_id}) - {specialty_name}')
        
        self.stdout.write('\n' + '='*60 + '\n')
        
        # Show how the system works
        self.stdout.write(self.style.SUCCESS('How the Specialty-Based System Works:'))
        self.stdout.write('1. Each doctor and nurse is assigned to a medical specialty')
        self.stdout.write('2. Each patient is assigned to a primary specialty')
        self.stdout.write('3. Doctors can only see patients in their specialty')
        self.stdout.write('4. Nurses can only see patients in their specialty')
        self.stdout.write('5. Admins can see all patients regardless of specialty')
        self.stdout.write('6. When creating new staff, they are automatically assigned to their qualification specialty')
        self.stdout.write('7. When registering patients, they are assigned to the specialty of the staff member creating them')
        
        self.stdout.write('\n' + self.style.SUCCESS('Demo completed successfully!'))