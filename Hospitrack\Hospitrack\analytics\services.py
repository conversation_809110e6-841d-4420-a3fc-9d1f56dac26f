"""
Analytics data collection and calculation services
"""
from django.db.models import Count, Avg, Sum, Q, F
from django.utils import timezone
from datetime import datetime, timedelta, date
from decimal import Decimal

from .models import (
    AnalyticsMetric, PatientFlowMetrics, StaffEfficiencyMetrics,
    BedUtilizationMetrics, FinancialMetrics, QualityMetrics
)
from patients.models import Patient, PatientAdmission
from appointments.models import Appointment, PatientOPDSchedule
from beds.models import Bed, Ward
from accounts.models import Doctor, Nurse, User

class AnalyticsDataCollector:
    """Main service for collecting and calculating analytics data"""
    
    def collect_daily_metrics(self, target_date=None):
        """Collect all daily metrics for a specific date"""
        if target_date is None:
            target_date = date.today()
        
        print(f"Collecting analytics data for {target_date}")
        
        # Collect all metric types
        self.collect_patient_flow_metrics(target_date)
        self.collect_bed_utilization_metrics(target_date)
        self.collect_staff_efficiency_metrics(target_date)
        self.collect_financial_metrics(target_date)
        self.collect_quality_metrics(target_date)
        
        print(f"Analytics data collection completed for {target_date}")
    
    def collect_patient_flow_metrics(self, target_date):
        """Collect patient flow metrics for a specific date"""
        # Get admissions for the date
        admissions = PatientAdmission.objects.filter(
            admission_date__date=target_date
        )
        
        total_admissions = admissions.count()
        emergency_admissions = admissions.filter(admission_type='emergency').count()
        planned_admissions = admissions.filter(admission_type='planned').count()
        
        # Get discharges for the date
        discharges = PatientAdmission.objects.filter(
            actual_discharge_date__date=target_date
        )
        total_discharges = discharges.count()
        
        # Calculate average length of stay for discharged patients
        if discharges.exists():
            avg_los = discharges.aggregate(
                avg_los=Avg(
                    F('actual_discharge_date') - F('admission_date')
                )
            )['avg_los']
            avg_los_days = avg_los.days if avg_los else 0
        else:
            avg_los_days = 0
        
        # Get bed occupancy
        total_beds = Bed.objects.filter(is_active=True).count()
        occupied_beds = Bed.objects.filter(status='occupied', is_active=True).count()
        occupancy_rate = (occupied_beds / total_beds * 100) if total_beds > 0 else 0
        
        # Calculate average wait time (simplified - using appointment data)
        appointments = Appointment.objects.filter(appointment_date=target_date)
        avg_wait_time = 15  # Placeholder - would need actual wait time tracking
        
        # Create or update patient flow metrics
        PatientFlowMetrics.objects.update_or_create(
            date=target_date,
            department=None,  # Overall metrics
            defaults={
                'total_admissions': total_admissions,
                'emergency_admissions': emergency_admissions,
                'planned_admissions': planned_admissions,
                'total_discharges': total_discharges,
                'average_length_of_stay': avg_los_days,
                'total_occupied_beds': occupied_beds,
                'occupancy_rate': occupancy_rate,
                'average_wait_time_minutes': avg_wait_time,
            }
        )
        
        # Collect specialty-specific metrics
        from accounts.models import MedicalSpecialty
        for specialty in MedicalSpecialty.objects.all():
            specialty_admissions = admissions.filter(patient__primary_specialty=specialty)
            specialty_discharges = discharges.filter(patient__primary_specialty=specialty)
            
            PatientFlowMetrics.objects.update_or_create(
                date=target_date,
                specialty=specialty,
                defaults={
                    'total_admissions': specialty_admissions.count(),
                    'emergency_admissions': specialty_admissions.filter(admission_type='emergency').count(),
                    'planned_admissions': specialty_admissions.filter(admission_type='planned').count(),
                    'total_discharges': specialty_discharges.count(),
                }
            )
    
    def collect_bed_utilization_metrics(self, target_date):
        """Collect bed utilization metrics for each ward"""
        for ward in Ward.objects.filter(is_active=True):
            ward_beds = ward.beds.filter(is_active=True)
            total_beds = ward_beds.count()
            
            if total_beds == 0:
                continue
            
            occupied_beds = ward_beds.filter(status='occupied').count()
            available_beds = ward_beds.filter(status='available').count()
            maintenance_beds = ward_beds.filter(status='maintenance').count()
            
            utilization_rate = (occupied_beds / total_beds * 100) if total_beds > 0 else 0
            
            # Calculate revenue (simplified)
            daily_rates = ward_beds.aggregate(avg_rate=Avg('daily_rate'))['avg_rate'] or 0
            total_revenue = occupied_beds * daily_rates
            revenue_per_bed = total_revenue / total_beds if total_beds > 0 else 0
            
            # Turnover rate calculation (simplified)
            turnover_rate = 0.1  # Placeholder - would need actual turnover tracking
            
            BedUtilizationMetrics.objects.update_or_create(
                date=target_date,
                ward=ward,
                defaults={
                    'total_beds': total_beds,
                    'occupied_beds': occupied_beds,
                    'available_beds': available_beds,
                    'maintenance_beds': maintenance_beds,
                    'utilization_rate': utilization_rate,
                    'turnover_rate': turnover_rate,
                    'revenue_per_bed': revenue_per_bed,
                    'total_revenue': total_revenue,
                }
            )
    
    def collect_staff_efficiency_metrics(self, target_date):
        """Collect staff efficiency metrics for doctors and nurses"""
        # Get all doctors and nurses
        doctors = Doctor.objects.all()
        nurses = Nurse.objects.all()
        
        for doctor in doctors:
            # Get appointments for the doctor on the target date
            appointments = Appointment.objects.filter(
                doctor=doctor,
                appointment_date=target_date
            )
            
            patients_seen = appointments.filter(status='completed').count()
            appointments_completed = appointments.count()
            
            # Calculate revenue generated
            revenue = appointments.aggregate(
                total=Sum('fee')
            )['total'] or 0
            
            # Calculate average consultation time (placeholder)
            avg_consultation_time = 30  # minutes
            
            # No-show rate
            no_shows = appointments.filter(status='no_show').count()
            no_show_rate = (no_shows / appointments_completed * 100) if appointments_completed > 0 else 0
            
            StaffEfficiencyMetrics.objects.update_or_create(
                date=target_date,
                staff_member=doctor.user,
                defaults={
                    'patients_seen': patients_seen,
                    'appointments_completed': appointments_completed,
                    'average_consultation_time': avg_consultation_time,
                    'no_show_rate': no_show_rate,
                    'scheduled_hours': 8,
                    'actual_hours': 8,  # Placeholder
                    'revenue_generated': revenue,
                }
            )
    
    def collect_financial_metrics(self, target_date):
        """Collect financial metrics"""
        # Calculate consultation revenue
        consultation_revenue = Appointment.objects.filter(
            appointment_date=target_date,
            is_paid=True
        ).aggregate(total=Sum('fee'))['total'] or 0
        
        # Calculate bed revenue
        occupied_beds = Bed.objects.filter(status='occupied', is_active=True)
        bed_revenue = occupied_beds.aggregate(
            total=Sum('daily_rate')
        )['total'] or 0
        
        # Placeholder for other revenue streams
        procedure_revenue = consultation_revenue * Decimal('0.3')  # 30% of consultation
        pharmacy_revenue = consultation_revenue * Decimal('0.2')   # 20% of consultation
        other_revenue = consultation_revenue * Decimal('0.1')      # 10% of consultation
        
        # Placeholder costs
        staff_costs = consultation_revenue * Decimal('0.4')        # 40% of revenue
        operational_costs = consultation_revenue * Decimal('0.2')  # 20% of revenue
        equipment_costs = consultation_revenue * Decimal('0.1')    # 10% of revenue
        
        FinancialMetrics.objects.update_or_create(
            date=target_date,
            department=None,  # Overall metrics
            defaults={
                'consultation_revenue': consultation_revenue,
                'bed_revenue': bed_revenue,
                'procedure_revenue': procedure_revenue,
                'pharmacy_revenue': pharmacy_revenue,
                'other_revenue': other_revenue,
                'staff_costs': staff_costs,
                'operational_costs': operational_costs,
                'equipment_costs': equipment_costs,
            }
        )
    
    def collect_quality_metrics(self, target_date):
        """Collect quality metrics"""
        # Patient satisfaction (placeholder - would need actual survey data)
        avg_satisfaction = Decimal('4.2')  # Out of 5
        satisfaction_responses = 50
        
        # Readmission rate (simplified calculation)
        total_discharges = PatientAdmission.objects.filter(
            actual_discharge_date__date=target_date
        ).count()
        
        # Readmissions within 30 days
        readmissions = PatientAdmission.objects.filter(
            admission_date__date__gte=target_date - timedelta(days=30),
            admission_date__date__lte=target_date
        ).values('patient').annotate(
            admission_count=Count('id')
        ).filter(admission_count__gt=1).count()
        
        readmission_rate = (readmissions / total_discharges * 100) if total_discharges > 0 else 0
        
        # Average wait time
        avg_wait_time = 25  # minutes - placeholder
        
        # Appointment punctuality
        total_appointments = Appointment.objects.filter(appointment_date=target_date).count()
        punctual_appointments = total_appointments * 0.85  # 85% punctuality rate
        punctuality = (punctual_appointments / total_appointments * 100) if total_appointments > 0 else 0
        
        QualityMetrics.objects.update_or_create(
            date=target_date,
            department=None,  # Overall metrics
            defaults={
                'average_satisfaction_score': avg_satisfaction,
                'satisfaction_responses': satisfaction_responses,
                'readmission_rate': readmission_rate,
                'average_wait_time': avg_wait_time,
                'appointment_punctuality': punctuality,
                'incident_count': 0,  # Placeholder
                'near_miss_count': 0,  # Placeholder
            }
        )

class AnalyticsCalculator:
    """Service for calculating derived metrics and trends"""
    
    def calculate_trends(self, metric_type, days=30):
        """Calculate trends for a specific metric type over a period"""
        end_date = date.today()
        start_date = end_date - timedelta(days=days)
        
        metrics = AnalyticsMetric.objects.filter(
            metric_type=metric_type,
            date_recorded__date__gte=start_date,
            date_recorded__date__lte=end_date
        ).order_by('date_recorded')
        
        if metrics.count() < 2:
            return {'trend': 'insufficient_data', 'change_percent': 0}
        
        first_value = metrics.first().value
        last_value = metrics.last().value
        
        if first_value == 0:
            change_percent = 0
        else:
            change_percent = ((last_value - first_value) / first_value) * 100
        
        if change_percent > 5:
            trend = 'increasing'
        elif change_percent < -5:
            trend = 'decreasing'
        else:
            trend = 'stable'
        
        return {
            'trend': trend,
            'change_percent': float(change_percent),
            'first_value': float(first_value),
            'last_value': float(last_value),
            'data_points': metrics.count()
        }
    
    def calculate_kpis(self, target_date=None):
        """Calculate key performance indicators"""
        if target_date is None:
            target_date = date.today()
        
        kpis = {}
        
        # Patient flow KPIs
        patient_flow = PatientFlowMetrics.objects.filter(
            date=target_date, department=None
        ).first()
        
        if patient_flow:
            kpis['occupancy_rate'] = float(patient_flow.occupancy_rate)
            kpis['total_admissions'] = patient_flow.total_admissions
            kpis['average_length_of_stay'] = float(patient_flow.average_length_of_stay)
        
        # Financial KPIs
        financial = FinancialMetrics.objects.filter(
            date=target_date, department=None
        ).first()
        
        if financial:
            kpis['total_revenue'] = float(financial.total_revenue)
            kpis['net_profit'] = float(financial.net_profit)
            kpis['profit_margin'] = float(financial.profit_margin)
        
        # Quality KPIs
        quality = QualityMetrics.objects.filter(
            date=target_date, department=None
        ).first()
        
        if quality:
            kpis['patient_satisfaction'] = float(quality.average_satisfaction_score)
            kpis['readmission_rate'] = float(quality.readmission_rate)
            kpis['average_wait_time'] = quality.average_wait_time
        
        return kpis
