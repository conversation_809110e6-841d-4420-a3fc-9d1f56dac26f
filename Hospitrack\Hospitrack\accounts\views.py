from django.shortcuts import render, redirect, get_object_or_404
from django.contrib.auth import authenticate, login, logout
from django.contrib.auth.decorators import login_required
from django.contrib import messages
from django.db.models import Count, Q
from datetime import date, datetime
from .models import User, Doctor, Nurse, Admin
from patients.models import Patient, PatientUpdate
from appointments.models import Appointment
from beds.models import <PERSON>, Ward
from staff.models import StaffMember

def landing_page(request):
    """HospiTrack landing page - shows login form for all users"""
    # Always show the login page, even for authenticated users
    # If user is already logged in, they can access dashboard via direct URL
    response = render(request, 'accounts/landing.html')
    # Add cache control headers to prevent form caching
    response['Cache-Control'] = 'no-cache, no-store, must-revalidate'
    response['Pragma'] = 'no-cache'
    response['Expires'] = '0'
    return response

def login_view(request):
    """Handle user login"""
    if request.method == 'POST':
        username = request.POST.get('username')
        password = request.POST.get('password')

        user = authenticate(request, username=username, password=password)
        if user is not None:
            login(request, user)
            # Clear any existing messages (like logout success message)
            storage = messages.get_messages(request)
            storage.used = True
            messages.success(request, f'Welcome back, {user.first_name}!')
            return redirect('dashboard')
        else:
            messages.error(request, 'Invalid username or password.')

    return render(request, 'accounts/login.html')

def logout_view(request):
    """Handle user logout"""
    logout(request)
    messages.success(request, 'You have been logged out successfully.')
    response = redirect('landing')
    # Add cache control headers to prevent caching
    response['Cache-Control'] = 'no-cache, no-store, must-revalidate'
    response['Pragma'] = 'no-cache'
    response['Expires'] = '0'
    return response

@login_required
def dashboard(request):
    """Redirect to appropriate dashboard based on user type"""
    user = request.user

    if user.user_type == 'admin':
        return redirect('admin_dashboard')
    elif user.user_type == 'doctor':
        return redirect('doctor_dashboard')
    elif user.user_type == 'nurse':
        return redirect('nurse_dashboard')
    else:
        messages.error(request, 'Invalid user type.')
        return redirect('login')

@login_required
def admin_dashboard(request):
    """Admin dashboard with overview statistics"""
    if request.user.user_type != 'admin':
        messages.error(request, 'Access denied. Admin privileges required.')
        return redirect('dashboard')

    # Get statistics
    total_patients = Patient.objects.filter(is_active=True).count()
    total_doctors = Doctor.objects.count()
    total_nurses = Nurse.objects.count()
    total_staff = StaffMember.objects.filter(is_active=True).count()

    # Bed statistics
    total_beds = Bed.objects.filter(is_active=True).count()
    available_beds = Bed.objects.filter(status='available', is_active=True).count()
    occupied_beds = Bed.objects.filter(status='occupied', is_active=True).count()
    maintenance_beds = Bed.objects.filter(status='maintenance', is_active=True).count()
    emergency_beds = Bed.objects.filter(status='emergency', is_active=True).count()

    # Today's appointments
    today_appointments = Appointment.objects.filter(
        appointment_date=date.today()
    ).count()

    # Recent patient registrations with search functionality
    search_query = request.GET.get('search', '').strip()
    recent_patients = Patient.objects.filter(is_active=True)

    if search_query:
        # Filter patients based on search query
        recent_patients = recent_patients.filter(
            Q(patient_id__icontains=search_query) |
            Q(first_name__icontains=search_query) |
            Q(last_name__icontains=search_query) |
            Q(phone__icontains=search_query) |
            Q(email__icontains=search_query)
        )

    recent_patients = recent_patients.order_by('-created_at')[:10]

    context = {
        'total_patients': total_patients,
        'total_doctors': total_doctors,
        'total_nurses': total_nurses,
        'total_staff': total_staff,
        'total_beds': total_beds,
        'available_beds': available_beds,
        'occupied_beds': occupied_beds,
        'maintenance_beds': maintenance_beds,
        'emergency_beds': emergency_beds,
        'today_appointments': today_appointments,
        'recent_patients': recent_patients,
        'search_query': search_query,
    }

    return render(request, 'accounts/admin_dashboard.html', context)

@login_required
def doctors_list(request):
    """List all doctors"""
    if request.user.user_type != 'admin':
        messages.error(request, 'Access denied. Admin privileges required.')
        return redirect('dashboard')

    doctors = Doctor.objects.all()
    context = {
        'doctors': doctors,
    }
    return render(request, 'accounts/doctors_list.html', context)

@login_required
def doctor_create(request):
    """Create a new doctor"""
    if request.user.user_type != 'admin':
        messages.error(request, 'Access denied. Admin privileges required.')
        return redirect('dashboard')

    if request.method == 'POST':
        try:
            # Create user account
            username = request.POST.get('username')
            email = request.POST.get('email')
            first_name = request.POST.get('first_name')
            last_name = request.POST.get('last_name')
            password = request.POST.get('password')

            # Check if username already exists
            if User.objects.filter(username=username).exists():
                messages.error(request, 'Username already exists. Please choose a different username.')
                return render(request, 'accounts/doctor_create.html')

            # Check if email already exists
            if User.objects.filter(email=email).exists():
                messages.error(request, 'Email already exists. Please use a different email.')
                return render(request, 'accounts/doctor_create.html')

            # Create user
            user = User.objects.create_user(
                username=username,
                email=email,
                password=password,
                first_name=first_name,
                last_name=last_name,
                user_type='doctor'
            )

            # Get or create specialty
            from .models import MedicalSpecialty
            specialization_name = request.POST.get('specialization')
            specialty, created = MedicalSpecialty.objects.get_or_create(
                name=specialization_name,
                defaults={'description': f'{specialization_name} medical specialty'}
            )

            # Create doctor profile
            doctor = Doctor.objects.create(
                user=user,
                specialty=specialty,
                specialization=specialization_name,
                license_number=request.POST.get('license_number'),
                department=request.POST.get('department'),
                consultation_fee=request.POST.get('consultation_fee') or 0,
            )

            messages.success(request, f'Doctor {user.get_full_name()} has been created successfully.')
            return redirect('doctors_list')

        except Exception as e:
            messages.error(request, f'Error creating doctor: {str(e)}')

    from .models import MedicalSpecialty
    
    context = {
        'specializations': MedicalSpecialty.objects.filter(is_active=True).order_by('name'),
        'departments': [
            'General Medicine', 'Emergency', 'Surgery', 'Pediatrics',
            'Cardiology', 'Orthopedics', 'Gynecology', 'Neurology'
        ]
    }

    return render(request, 'accounts/doctor_create.html', context)

@login_required
def doctor_detail(request, doctor_id):
    """View doctor details"""
    doctor = get_object_or_404(Doctor, user_id=doctor_id)
    context = {
        'doctor': doctor,
    }
    return render(request, 'accounts/doctor_detail.html', context)

@login_required
def doctor_edit(request, doctor_id):
    """Edit doctor details"""
    if request.user.user_type != 'admin':
        messages.error(request, 'Access denied. Admin privileges required.')
        return redirect('dashboard')

    doctor = get_object_or_404(Doctor, user_id=doctor_id)

    if request.method == 'POST':
        try:
            # Update user information
            user = doctor.user
            user.first_name = request.POST.get('first_name')
            user.last_name = request.POST.get('last_name')
            user.email = request.POST.get('email')
            user.phone = request.POST.get('phone')
            user.save()

            # Update doctor information
            specialization_name = request.POST.get('specialization')
            doctor.specialization = specialization_name
            
            # Update or create specialty relationship
            from .models import MedicalSpecialty
            specialty, created = MedicalSpecialty.objects.get_or_create(
                name=specialization_name,
                defaults={'description': f'{specialization_name} medical specialty'}
            )
            doctor.specialty = specialty
            
            doctor.license_number = request.POST.get('license_number')
            doctor.department = request.POST.get('department')
            doctor.experience_years = request.POST.get('experience_years') or 0
            doctor.consultation_fee = request.POST.get('consultation_fee') or 0
            doctor.qualifications = request.POST.get('qualifications')
            doctor.save()

            messages.success(request, f'Dr. {user.get_full_name()} has been updated successfully.')
            return redirect('doctor_detail', doctor_id=doctor.user_id)

        except Exception as e:
            messages.error(request, f'Error updating doctor: {str(e)}')

    context = {
        'doctor': doctor,
        'specializations': [
            'General Medicine', 'Cardiology', 'Neurology', 'Orthopedics',
            'Pediatrics', 'Gynecology', 'Dermatology', 'Psychiatry',
            'Emergency Medicine', 'Surgery', 'Anesthesiology', 'Radiology'
        ],
        'departments': [
            'General Medicine', 'Emergency', 'Surgery', 'Pediatrics',
            'Cardiology', 'Orthopedics', 'Gynecology', 'Neurology'
        ]
    }

    return render(request, 'accounts/doctor_edit.html', context)

@login_required
def doctor_dashboard(request):
    """Doctor dashboard showing today's appointments and patient information"""
    if request.user.user_type != 'doctor':
        messages.error(request, 'Access denied. Doctor privileges required.')
        return redirect('dashboard')

    try:
        doctor = Doctor.objects.get(user=request.user)
    except Doctor.DoesNotExist:
        messages.error(request, 'Doctor profile not found.')
        return redirect('dashboard')

    # Today's appointments
    today_appointments = Appointment.objects.filter(
        doctor=doctor,
        appointment_date=date.today()
    ).order_by('appointment_time')

    # Upcoming appointments (next 7 days)
    from datetime import timedelta
    upcoming_appointments = Appointment.objects.filter(
        doctor=doctor,
        appointment_date__gt=date.today(),
        appointment_date__lte=date.today() + timedelta(days=7)
    ).order_by('appointment_date', 'appointment_time')[:10]

    # Recent patient updates
    recent_updates = PatientUpdate.objects.filter(
        updated_by=request.user
    ).order_by('-created_at')[:5]

    # Hospital admissions under this doctor's care
    from patients.models import PatientAdmission
    admitted_patients = PatientAdmission.objects.filter(
        doctor=doctor,
        status='admitted'
    ).order_by('-admission_date')

    # Nurses in the same specialty
    specialty_nurses = doctor.get_specialty_nurses()
    available_nurses = doctor.get_available_nurses()
    nurses_on_leave = doctor.get_nurses_on_leave()
    
    # Nurses by shift
    morning_nurses = doctor.get_nurses_by_shift('morning')
    evening_nurses = doctor.get_nurses_by_shift('evening')
    night_nurses = doctor.get_nurses_by_shift('night')

    context = {
        'doctor': doctor,
        'today_appointments': today_appointments,
        'upcoming_appointments': upcoming_appointments,
        'recent_updates': recent_updates,
        'today_count': today_appointments.count(),
        'admitted_patients': admitted_patients,
        'admitted_count': admitted_patients.count(),
        # Nurses information
        'specialty_nurses': specialty_nurses,
        'available_nurses': available_nurses,
        'nurses_on_leave': nurses_on_leave,
        'morning_nurses': morning_nurses,
        'evening_nurses': evening_nurses,
        'night_nurses': night_nurses,
        'total_nurses': specialty_nurses.count(),
        'available_nurses_count': available_nurses.count(),
        'nurses_on_leave_count': nurses_on_leave.count(),
    }

    return render(request, 'accounts/doctor_dashboard.html', context)

@login_required
def nurse_dashboard(request):
    """Nurse dashboard with bed updates and patient care information"""
    if request.user.user_type != 'nurse':
        messages.error(request, 'Access denied. Nurse privileges required.')
        return redirect('dashboard')

    try:
        nurse = Nurse.objects.get(user=request.user)
    except Nurse.DoesNotExist:
        messages.error(request, 'Nurse profile not found.')
        return redirect('dashboard')

    # Bed statistics for nurse's department
    ward_beds = Bed.objects.filter(
        ward__name__icontains=nurse.department,
        is_active=True
    )

    available_beds = ward_beds.filter(status='available').count()
    occupied_beds = ward_beds.filter(status='occupied').count()
    maintenance_beds = ward_beds.filter(status='maintenance').count()

    # Recent patient updates by this nurse
    recent_updates = PatientUpdate.objects.filter(
        updated_by=request.user
    ).order_by('-created_at')[:10]

    # Patients in nurse's department
    from beds.models import BedAssignment
    current_patients = BedAssignment.objects.filter(
        bed__ward__name__icontains=nurse.department,
        is_active=True,
        discharge_date__isnull=True
    ).select_related('patient', 'bed')

    context = {
        'nurse': nurse,
        'available_beds': available_beds,
        'occupied_beds': occupied_beds,
        'maintenance_beds': maintenance_beds,
        'recent_updates': recent_updates,
        'current_patients': current_patients,
    }

    return render(request, 'accounts/nurse_dashboard.html', context)

@login_required
def nurse_create(request):
    """Create a new nurse with automatic specialty assignment"""
    if request.user.user_type != 'admin':
        messages.error(request, 'Access denied. Admin privileges required.')
        return redirect('dashboard')

    if request.method == 'POST':
        try:
            # Create user account
            username = request.POST.get('username')
            email = request.POST.get('email')
            first_name = request.POST.get('first_name')
            last_name = request.POST.get('last_name')
            password = request.POST.get('password')

            # Check if username already exists
            if User.objects.filter(username=username).exists():
                messages.error(request, 'Username already exists. Please choose a different username.')
                return render(request, 'accounts/nurse_create.html')

            # Check if email already exists
            if User.objects.filter(email=email).exists():
                messages.error(request, 'Email already exists. Please use a different email.')
                return render(request, 'accounts/nurse_create.html')

            # Create user
            user = User.objects.create_user(
                username=username,
                email=email,
                password=password,
                first_name=first_name,
                last_name=last_name,
                user_type='nurse'
            )

            # Get or create specialty based on qualification
            from .models import MedicalSpecialty
            specialty_name = request.POST.get('specialty')
            specialty, created = MedicalSpecialty.objects.get_or_create(
                name=specialty_name,
                defaults={'description': f'{specialty_name} nursing specialty'}
            )

            # Create nurse profile
            nurse = Nurse.objects.create(
                user=user,
                specialty=specialty,
                department=specialty_name,  # Auto-assign department based on specialty
                shift=request.POST.get('shift'),
                license_number=request.POST.get('license_number'),
            )

            messages.success(request, f'Nurse {user.get_full_name()} has been created successfully and assigned to {specialty_name} specialty.')
            return redirect('nurses_list')

        except Exception as e:
            messages.error(request, f'Error creating nurse: {str(e)}')

    from .models import MedicalSpecialty
    
    context = {
        'specializations': MedicalSpecialty.objects.filter(is_active=True).order_by('name'),
        'shifts': [
            ('morning', 'Morning (6 AM - 2 PM)'),
            ('evening', 'Evening (2 PM - 10 PM)'),
            ('night', 'Night (10 PM - 6 AM)'),
        ]
    }

    return render(request, 'accounts/nurse_create.html', context)

@login_required
def nurse_edit(request, nurse_id):
    """Edit nurse details"""
    if request.user.user_type != 'admin':
        messages.error(request, 'Access denied. Admin privileges required.')
        return redirect('dashboard')

    nurse = get_object_or_404(Nurse, user_id=nurse_id)

    if request.method == 'POST':
        try:
            # Update user information
            user = nurse.user
            user.first_name = request.POST.get('first_name')
            user.last_name = request.POST.get('last_name')
            user.email = request.POST.get('email')
            user.phone = request.POST.get('phone')
            user.save()

            # Update nurse information
            specialty_name = request.POST.get('specialty')
            
            # Update or create specialty relationship
            from .models import MedicalSpecialty
            specialty, created = MedicalSpecialty.objects.get_or_create(
                name=specialty_name,
                defaults={'description': f'{specialty_name} nursing specialty'}
            )
            nurse.specialty = specialty
            nurse.department = specialty_name  # Keep department in sync with specialty
            
            nurse.license_number = request.POST.get('license_number')
            nurse.shift = request.POST.get('shift')
            nurse.experience_years = request.POST.get('experience_years') or 0
            nurse.qualifications = request.POST.get('qualifications')
            nurse.save()

            messages.success(request, f'{user.get_full_name()} has been updated successfully.')
            return redirect('nurse_detail', nurse_id=nurse.user_id)

        except Exception as e:
            messages.error(request, f'Error updating nurse: {str(e)}')

    from .models import MedicalSpecialty
    
    context = {
        'nurse': nurse,
        'specializations': MedicalSpecialty.objects.filter(is_active=True).order_by('name'),
        'shifts': [
            ('morning', 'Morning (6 AM - 2 PM)'),
            ('evening', 'Evening (2 PM - 10 PM)'),
            ('night', 'Night (10 PM - 6 AM)'),
        ]
    }

    return render(request, 'accounts/nurse_edit.html', context)

@login_required
def nurse_detail(request, nurse_id):
    """View nurse details"""
    nurse = get_object_or_404(Nurse, user_id=nurse_id)
    context = {
        'nurse': nurse,
    }
    return render(request, 'accounts/nurse_detail.html', context)

@login_required
def nurses_list(request):
    """List all nurses"""
    if request.user.user_type != 'admin':
        messages.error(request, 'Access denied. Admin privileges required.')
        return redirect('dashboard')

    nurses = Nurse.objects.all().select_related('user', 'specialty')
    context = {
        'nurses': nurses,
    }
    return render(request, 'accounts/nurses_list.html', context)

@login_required
def nurse_leave_management(request, nurse_id=None):
    """Manage nurse leave status - Admin only"""
    if request.user.user_type != 'admin':
        messages.error(request, 'Access denied. Admin privileges required.')
        return redirect('dashboard')

    if nurse_id:
        nurse = get_object_or_404(Nurse, user_id=nurse_id)
        
        if request.method == 'POST':
            leave_status = request.POST.get('leave_status')
            leave_start_date = request.POST.get('leave_start_date')
            leave_end_date = request.POST.get('leave_end_date')
            leave_reason = request.POST.get('leave_reason')
            
            nurse.leave_status = leave_status
            if leave_status != 'available':
                nurse.leave_start_date = leave_start_date if leave_start_date else None
                nurse.leave_end_date = leave_end_date if leave_end_date else None
                nurse.leave_reason = leave_reason
            else:
                nurse.leave_start_date = None
                nurse.leave_end_date = None
                nurse.leave_reason = None
            
            nurse.save()
            messages.success(request, f'Leave status updated for {nurse.user.get_full_name()}')
            return redirect('nurses_list')
        
        context = {
            'nurse': nurse,
            'leave_choices': Nurse.LEAVE_STATUS_CHOICES,
        }
        return render(request, 'accounts/nurse_leave_form.html', context)
    
    # List all nurses with leave information
    nurses = Nurse.objects.all().select_related('user', 'specialty')
    context = {
        'nurses': nurses,
    }
    return render(request, 'accounts/nurse_leave_management.html', context)

@login_required
def specialty_team_view(request):
    """View for doctors to see their specialty team"""
    if request.user.user_type != 'doctor':
        messages.error(request, 'Access denied. Doctor privileges required.')
        return redirect('dashboard')

    try:
        doctor = Doctor.objects.get(user=request.user)
    except Doctor.DoesNotExist:
        messages.error(request, 'Doctor profile not found.')
        return redirect('dashboard')

    if not doctor.specialty:
        messages.warning(request, 'You are not assigned to any specialty.')
        return redirect('doctor_dashboard')

    # Get all team information
    specialty_nurses = doctor.get_specialty_nurses()
    available_nurses = doctor.get_available_nurses()
    nurses_on_leave = doctor.get_nurses_on_leave()
    
    # Get other doctors in the same specialty
    other_doctors = Doctor.objects.filter(
        specialty=doctor.specialty
    ).exclude(user=request.user).select_related('user')
    
    # Get patients in the specialty
    specialty_patients = doctor.specialty.patients.filter(is_active=True)

    context = {
        'doctor': doctor,
        'specialty': doctor.specialty,
        'specialty_nurses': specialty_nurses,
        'available_nurses': available_nurses,
        'nurses_on_leave': nurses_on_leave,
        'other_doctors': other_doctors,
        'specialty_patients': specialty_patients,
        'total_nurses': specialty_nurses.count(),
        'available_nurses_count': available_nurses.count(),
        'nurses_on_leave_count': nurses_on_leave.count(),
        'total_patients': specialty_patients.count(),
    }

    return render(request, 'accounts/specialty_team.html', context)
