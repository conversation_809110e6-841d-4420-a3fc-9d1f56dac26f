{% extends 'base.html' %}
{% load static %}

{% block title %}Healthcare Analytics Dashboard{% endblock %}

{% block extra_css %}
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<style>
    .kpi-card {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        border-radius: 12px;
        padding: 1.5rem;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    }
    .kpi-value {
        font-size: 2.5rem;
        font-weight: bold;
        margin-bottom: 0.5rem;
    }
    .kpi-label {
        font-size: 0.9rem;
        opacity: 0.9;
    }
    .chart-container {
        background: white;
        border-radius: 12px;
        padding: 1.5rem;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        margin-bottom: 2rem;
    }
    .alert-item {
        border-left: 4px solid #ef4444;
        background: #fef2f2;
        padding: 1rem;
        margin-bottom: 0.5rem;
        border-radius: 0 8px 8px 0;
    }
    .nav-analytics {
        background: #1f2937;
        padding: 1rem;
        border-radius: 8px;
        margin-bottom: 2rem;
    }
    .nav-analytics a {
        color: #d1d5db;
        text-decoration: none;
        padding: 0.5rem 1rem;
        border-radius: 6px;
        margin-right: 0.5rem;
        transition: all 0.3s;
    }
    .nav-analytics a:hover, .nav-analytics a.active {
        background: #374151;
        color: white;
    }
</style>
{% endblock %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <!-- Header -->
    <div class="mb-8">
        <h1 class="text-4xl font-bold text-gray-900 mb-2">🏥 Healthcare Analytics Dashboard</h1>
        <p class="text-gray-600">Comprehensive insights to optimize patient care and operational efficiency</p>
    </div>

    <!-- Analytics Navigation -->
    <div class="nav-analytics">
        <a href="{% url 'analytics:dashboard' %}" class="active">📊 Overview</a>
        <a href="{% url 'analytics:patient_flow' %}">🚶 Patient Flow</a>
        <a href="{% url 'analytics:financial' %}">💰 Financial</a>
        <a href="{% url 'analytics:bed_utilization' %}">🛏️ Bed Utilization</a>
        <a href="{% url 'analytics:staff_efficiency' %}">👥 Staff Efficiency</a>
        <a href="{% url 'analytics:quality' %}">⭐ Quality Metrics</a>
    </div>

    <!-- Date Range Filter -->
    <div class="bg-white p-4 rounded-lg shadow mb-6">
        <form method="get" class="flex items-center space-x-4">
            <div>
                <label class="block text-sm font-medium text-gray-700">Start Date</label>
                <input type="date" name="start_date" value="{{ start_date|date:'Y-m-d' }}" 
                       class="mt-1 block w-full rounded-md border-gray-300 shadow-sm">
            </div>
            <div>
                <label class="block text-sm font-medium text-gray-700">End Date</label>
                <input type="date" name="end_date" value="{{ end_date|date:'Y-m-d' }}" 
                       class="mt-1 block w-full rounded-md border-gray-300 shadow-sm">
            </div>
            <div class="pt-6">
                <button type="submit" class="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700">
                    Update
                </button>
            </div>
        </form>
    </div>

    <!-- Key Performance Indicators -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        <div class="kpi-card">
            <div class="kpi-value">{{ kpis.occupancy_rate|default:0|floatformat:1 }}%</div>
            <div class="kpi-label">Bed Occupancy Rate</div>
        </div>
        <div class="kpi-card" style="background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);">
            <div class="kpi-value">${{ kpis.total_revenue|default:0|floatformat:0 }}</div>
            <div class="kpi-label">Total Revenue</div>
        </div>
        <div class="kpi-card" style="background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);">
            <div class="kpi-value">{{ kpis.patient_satisfaction|default:0|floatformat:1 }}/5</div>
            <div class="kpi-label">Patient Satisfaction</div>
        </div>
        <div class="kpi-card" style="background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);">
            <div class="kpi-value">{{ kpis.total_admissions|default:0 }}</div>
            <div class="kpi-label">Total Admissions</div>
        </div>
    </div>

    <!-- Charts Row -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
        <!-- Patient Flow Chart -->
        <div class="chart-container">
            <h3 class="text-xl font-semibold mb-4">📈 Patient Flow Trends</h3>
            <canvas id="patientFlowChart" height="300"></canvas>
        </div>

        <!-- Financial Chart -->
        <div class="chart-container">
            <h3 class="text-xl font-semibold mb-4">💰 Financial Performance</h3>
            <canvas id="financialChart" height="300"></canvas>
        </div>
    </div>

    <!-- Alerts and Quick Actions -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <!-- Recent Alerts -->
        <div class="bg-white rounded-lg shadow p-6">
            <h3 class="text-xl font-semibold mb-4">🚨 Recent Alerts</h3>
            {% if recent_alerts %}
                {% for alert in recent_alerts %}
                <div class="alert-item">
                    <div class="font-semibold">{{ alert.rule.name }}</div>
                    <div class="text-sm text-gray-600">{{ alert.message }}</div>
                    <div class="text-xs text-gray-500 mt-1">{{ alert.triggered_at|date:"M d, Y H:i" }}</div>
                </div>
                {% endfor %}
            {% else %}
                <p class="text-gray-500">No recent alerts</p>
            {% endif %}
        </div>

        <!-- Quick Actions -->
        <div class="bg-white rounded-lg shadow p-6">
            <h3 class="text-xl font-semibold mb-4">⚡ Quick Actions</h3>
            <div class="space-y-3">
                <button onclick="collectAnalyticsData()" 
                        class="w-full bg-blue-600 text-white py-2 px-4 rounded-md hover:bg-blue-700 transition">
                    🔄 Refresh Analytics Data
                </button>
                <a href="{% url 'analytics:patient_flow' %}" 
                   class="block w-full bg-green-600 text-white py-2 px-4 rounded-md hover:bg-green-700 transition text-center">
                    📊 View Detailed Patient Flow
                </a>
                <a href="{% url 'analytics:financial' %}" 
                   class="block w-full bg-purple-600 text-white py-2 px-4 rounded-md hover:bg-purple-700 transition text-center">
                    💰 View Financial Analytics
                </a>
                <a href="{% url 'analytics:quality' %}" 
                   class="block w-full bg-yellow-600 text-white py-2 px-4 rounded-md hover:bg-yellow-700 transition text-center">
                    ⭐ View Quality Metrics
                </a>
            </div>
        </div>
    </div>
</div>

<script>
// Chart data from Django
const chartData = {{ chart_data|safe }};

// Patient Flow Chart
const patientFlowCtx = document.getElementById('patientFlowChart').getContext('2d');
new Chart(patientFlowCtx, {
    type: 'line',
    data: {
        labels: chartData.patient_flow.dates,
        datasets: [
            {
                label: 'Admissions',
                data: chartData.patient_flow.admissions,
                borderColor: '#3b82f6',
                backgroundColor: 'rgba(59, 130, 246, 0.1)',
                tension: 0.4
            },
            {
                label: 'Discharges',
                data: chartData.patient_flow.discharges,
                borderColor: '#10b981',
                backgroundColor: 'rgba(16, 185, 129, 0.1)',
                tension: 0.4
            },
            {
                label: 'Occupancy %',
                data: chartData.patient_flow.occupancy,
                borderColor: '#f59e0b',
                backgroundColor: 'rgba(245, 158, 11, 0.1)',
                tension: 0.4,
                yAxisID: 'y1'
            }
        ]
    },
    options: {
        responsive: true,
        maintainAspectRatio: false,
        scales: {
            y: {
                beginAtZero: true,
                title: {
                    display: true,
                    text: 'Count'
                }
            },
            y1: {
                type: 'linear',
                display: true,
                position: 'right',
                title: {
                    display: true,
                    text: 'Occupancy %'
                },
                grid: {
                    drawOnChartArea: false,
                },
            }
        },
        plugins: {
            legend: {
                display: true,
                position: 'top'
            }
        }
    }
});

// Financial Chart
const financialCtx = document.getElementById('financialChart').getContext('2d');
new Chart(financialCtx, {
    type: 'bar',
    data: {
        labels: chartData.financial.dates,
        datasets: [
            {
                label: 'Revenue',
                data: chartData.financial.revenue,
                backgroundColor: 'rgba(34, 197, 94, 0.8)',
                borderColor: '#22c55e',
                borderWidth: 1
            },
            {
                label: 'Costs',
                data: chartData.financial.costs,
                backgroundColor: 'rgba(239, 68, 68, 0.8)',
                borderColor: '#ef4444',
                borderWidth: 1
            },
            {
                label: 'Profit',
                data: chartData.financial.profit,
                backgroundColor: 'rgba(59, 130, 246, 0.8)',
                borderColor: '#3b82f6',
                borderWidth: 1
            }
        ]
    },
    options: {
        responsive: true,
        maintainAspectRatio: false,
        scales: {
            y: {
                beginAtZero: true,
                title: {
                    display: true,
                    text: 'Amount ($)'
                }
            }
        },
        plugins: {
            legend: {
                display: true,
                position: 'top'
            }
        }
    }
});

// Function to collect analytics data
function collectAnalyticsData() {
    fetch('{% url "analytics:collect_data" %}', {
        method: 'POST',
        headers: {
            'X-CSRFToken': '{{ csrf_token }}',
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            'date': new Date().toISOString().split('T')[0]
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            alert('✅ Analytics data refreshed successfully!');
            location.reload();
        } else {
            alert('❌ Error refreshing data: ' + data.message);
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('❌ Error refreshing data');
    });
}
</script>
{% endblock %}
