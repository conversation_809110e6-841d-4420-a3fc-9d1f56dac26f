#!/usr/bin/env python
"""
Test database synchronization issues between admin changes and doctor/nurse records
"""
import os
import sys
import django
from django.test import Client

# Setup Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'hospitrack.settings')
django.setup()

from accounts.models import User, Doctor, Nurse, MedicalSpecialty
from patients.models import Patient
from appointments.models import PatientOPDSchedule

def test_doctor_specialty_sync():
    """Test if doctor specialty updates are properly synchronized"""
    print("🔍 Testing Doctor Specialty Synchronization")
    print("=" * 60)
    
    client = Client()
    results = {'passed': 0, 'failed': 0, 'errors': []}
    
    # Login as admin
    client.login(username='admin', password='admin123')
    
    try:
        # Get a doctor to test with
        doctor = Doctor.objects.first()
        if not doctor:
            print("   ❌ No doctors found")
            results['failed'] += 1
            return results
        
        print(f"   📋 Testing doctor: Dr. {doctor.user.get_full_name()}")
        print(f"   📋 Current specialization: {doctor.specialization}")
        print(f"   📋 Current specialty FK: {doctor.specialty}")
        
        # Test updating doctor specialization
        new_specialization = "Updated Cardiology"
        edit_url = f'/doctors/{doctor.user.id}/edit/'
        
        form_data = {
            'first_name': doctor.user.first_name,
            'last_name': doctor.user.last_name,
            'email': doctor.user.email,
            'phone': doctor.user.phone or '**********',
            'specialization': new_specialization,
            'license_number': doctor.license_number,
            'department': doctor.department,
            'experience_years': doctor.experience_years or 5,
            'consultation_fee': doctor.consultation_fee or 500,
            'qualifications': doctor.qualifications or 'Test qualifications'
        }
        
        response = client.post(edit_url, form_data)
        
        if response.status_code == 302:
            print("   ✅ Form submission successful")
            results['passed'] += 1
            
            # Refresh doctor from database
            doctor.refresh_from_db()
            
            # Check if specialization field was updated
            if doctor.specialization == new_specialization:
                print(f"   ✅ Specialization field updated: {doctor.specialization}")
                results['passed'] += 1
            else:
                print(f"   ❌ Specialization field not updated: {doctor.specialization}")
                results['failed'] += 1
                results['errors'].append("Specialization field not updated")
            
            # Check if specialty FK was updated
            if doctor.specialty and doctor.specialty.name == new_specialization:
                print(f"   ✅ Specialty FK updated: {doctor.specialty.name}")
                results['passed'] += 1
            else:
                print(f"   ❌ Specialty FK not updated: {doctor.specialty}")
                results['failed'] += 1
                results['errors'].append("Specialty FK not synchronized")
                
        else:
            print(f"   ❌ Form submission failed: {response.status_code}")
            results['failed'] += 1
            results['errors'].append(f"Form submission failed: {response.status_code}")
            
    except Exception as e:
        print(f"   ❌ Exception: {str(e)}")
        results['failed'] += 1
        results['errors'].append(f"Exception: {str(e)}")
    
    return results

def test_nurse_specialty_sync():
    """Test if nurse specialty updates are properly synchronized"""
    print(f"\n🔍 Testing Nurse Specialty Synchronization")
    print("=" * 60)
    
    results = {'passed': 0, 'failed': 0, 'errors': []}
    
    try:
        # Get a nurse to test with
        nurse = Nurse.objects.first()
        if not nurse:
            print("   ❌ No nurses found")
            results['failed'] += 1
            return results
        
        print(f"   📋 Testing nurse: {nurse.user.get_full_name()}")
        print(f"   📋 Current department: {nurse.department}")
        print(f"   📋 Current specialty FK: {nurse.specialty}")
        
        # Check if nurse has proper specialty assignment
        if nurse.specialty:
            print(f"   ✅ Nurse has specialty: {nurse.specialty.name}")
            results['passed'] += 1
        else:
            print("   ❌ Nurse has no specialty assigned")
            results['failed'] += 1
            results['errors'].append("Nurse has no specialty assigned")
        
        # Check if department matches specialty
        if nurse.specialty and nurse.department == nurse.specialty.name:
            print(f"   ✅ Department matches specialty: {nurse.department}")
            results['passed'] += 1
        else:
            print(f"   ❌ Department doesn't match specialty: {nurse.department} vs {nurse.specialty}")
            results['failed'] += 1
            results['errors'].append("Department doesn't match specialty")
            
    except Exception as e:
        print(f"   ❌ Exception: {str(e)}")
        results['failed'] += 1
        results['errors'].append(f"Exception: {str(e)}")
    
    return results

def test_opd_scheduling():
    """Test OPD scheduling with specific doctors"""
    print(f"\n🔍 Testing OPD Scheduling")
    print("=" * 60)
    
    client = Client()
    results = {'passed': 0, 'failed': 0, 'errors': []}
    
    # Login as admin
    client.login(username='admin', password='admin123')
    
    try:
        # Get a doctor and patient for testing
        doctor = Doctor.objects.first()
        patient = Patient.objects.first()
        
        if not doctor:
            print("   ❌ No doctors found for OPD scheduling")
            results['failed'] += 1
            return results
            
        if not patient:
            print("   ❌ No patients found for OPD scheduling")
            results['failed'] += 1
            return results
        
        print(f"   📋 Testing OPD scheduling")
        print(f"   📋 Doctor: Dr. {doctor.user.get_full_name()} (ID: {doctor.user.id})")
        print(f"   📋 Patient: {patient.full_name} (ID: {patient.id})")
        
        # Test OPD schedule creation
        from datetime import date, timedelta
        tomorrow = date.today() + timedelta(days=1)
        
        form_data = {
            'patient': patient.id,
            'doctor': doctor.user.id,
            'schedule_date': tomorrow.strftime('%Y-%m-%d'),
            'time_slot': '09:00-10:00',
            'reason': 'Regular checkup',
            'special_instructions': 'Test OPD scheduling'
        }
        
        response = client.post('/appointments/opd/create/', form_data)
        
        if response.status_code == 302:
            print("   ✅ OPD schedule creation successful")
            results['passed'] += 1
            
            # Check if schedule was created in database
            schedule = PatientOPDSchedule.objects.filter(
                doctor=doctor,
                patient=patient,
                schedule_date=tomorrow
            ).first()
            
            if schedule:
                print(f"   ✅ OPD schedule found in database: {schedule.schedule_id}")
                results['passed'] += 1
                
                # Check if doctor relationship is correct
                if schedule.doctor.user.id == doctor.user.id:
                    print(f"   ✅ Doctor relationship correct: {schedule.doctor.user.get_full_name()}")
                    results['passed'] += 1
                else:
                    print(f"   ❌ Doctor relationship incorrect")
                    results['failed'] += 1
                    results['errors'].append("Doctor relationship incorrect in OPD schedule")
                    
            else:
                print("   ❌ OPD schedule not found in database")
                results['failed'] += 1
                results['errors'].append("OPD schedule not created in database")
                
        else:
            print(f"   ❌ OPD schedule creation failed: {response.status_code}")
            results['failed'] += 1
            results['errors'].append(f"OPD schedule creation failed: {response.status_code}")
            
    except Exception as e:
        print(f"   ❌ Exception: {str(e)}")
        results['failed'] += 1
        results['errors'].append(f"Exception: {str(e)}")
    
    return results

def test_admin_dashboard_data():
    """Test if admin dashboard shows updated data"""
    print(f"\n🔍 Testing Admin Dashboard Data")
    print("=" * 60)
    
    client = Client()
    results = {'passed': 0, 'failed': 0, 'errors': []}
    
    # Login as admin
    client.login(username='admin', password='admin123')
    
    try:
        # Test admin dashboard
        response = client.get('/admin-dashboard/')
        
        if response.status_code == 200:
            print("   ✅ Admin dashboard accessible")
            results['passed'] += 1
            
            # Check if dashboard contains doctor and nurse data
            content = response.content.decode()
            
            # Count doctors and nurses in database
            doctor_count = Doctor.objects.count()
            nurse_count = Nurse.objects.count()
            
            print(f"   📊 Database counts - Doctors: {doctor_count}, Nurses: {nurse_count}")
            
            # Check if counts are reflected in dashboard
            if str(doctor_count) in content:
                print(f"   ✅ Doctor count displayed correctly: {doctor_count}")
                results['passed'] += 1
            else:
                print(f"   ❌ Doctor count not displayed correctly")
                results['failed'] += 1
                results['errors'].append("Doctor count not displayed correctly")
            
            if str(nurse_count) in content:
                print(f"   ✅ Nurse count displayed correctly: {nurse_count}")
                results['passed'] += 1
            else:
                print(f"   ❌ Nurse count not displayed correctly")
                results['failed'] += 1
                results['errors'].append("Nurse count not displayed correctly")
                
        else:
            print(f"   ❌ Admin dashboard failed: {response.status_code}")
            results['failed'] += 1
            results['errors'].append(f"Admin dashboard failed: {response.status_code}")
            
    except Exception as e:
        print(f"   ❌ Exception: {str(e)}")
        results['failed'] += 1
        results['errors'].append(f"Exception: {str(e)}")
    
    return results

def test_specialty_relationships():
    """Test medical specialty relationships"""
    print(f"\n🔍 Testing Medical Specialty Relationships")
    print("=" * 60)
    
    results = {'passed': 0, 'failed': 0, 'errors': []}
    
    try:
        # Check specialty objects
        specialties = MedicalSpecialty.objects.all()
        print(f"   📊 Total specialties: {specialties.count()}")
        
        for specialty in specialties:
            doctor_count = specialty.doctors.count()
            nurse_count = specialty.nurses.count()
            
            print(f"   📋 {specialty.name}: {doctor_count} doctors, {nurse_count} nurses")
            
            if doctor_count > 0 or nurse_count > 0:
                print(f"   ✅ Specialty {specialty.name} has staff assigned")
                results['passed'] += 1
            else:
                print(f"   ⚠️  Specialty {specialty.name} has no staff assigned")
                results['errors'].append(f"Specialty {specialty.name} has no staff")
        
        # Check orphaned doctors/nurses
        doctors_without_specialty = Doctor.objects.filter(specialty__isnull=True).count()
        nurses_without_specialty = Nurse.objects.filter(specialty__isnull=True).count()
        
        if doctors_without_specialty == 0:
            print(f"   ✅ All doctors have specialty assigned")
            results['passed'] += 1
        else:
            print(f"   ❌ {doctors_without_specialty} doctors without specialty")
            results['failed'] += 1
            results['errors'].append(f"{doctors_without_specialty} doctors without specialty")
        
        if nurses_without_specialty == 0:
            print(f"   ✅ All nurses have specialty assigned")
            results['passed'] += 1
        else:
            print(f"   ❌ {nurses_without_specialty} nurses without specialty")
            results['failed'] += 1
            results['errors'].append(f"{nurses_without_specialty} nurses without specialty")
            
    except Exception as e:
        print(f"   ❌ Exception: {str(e)}")
        results['failed'] += 1
        results['errors'].append(f"Exception: {str(e)}")
    
    return results

def main():
    """Run all database synchronization tests"""
    print("🔍 Database Synchronization Issues Testing")
    print("=" * 80)
    
    all_results = {'passed': 0, 'failed': 0, 'errors': []}
    
    # Run test suites
    test_suites = [
        ("Doctor Specialty Sync", test_doctor_specialty_sync),
        ("Nurse Specialty Sync", test_nurse_specialty_sync),
        ("OPD Scheduling", test_opd_scheduling),
        ("Admin Dashboard Data", test_admin_dashboard_data),
        ("Specialty Relationships", test_specialty_relationships)
    ]
    
    for suite_name, test_function in test_suites:
        print(f"\n🧪 Running {suite_name} Tests...")
        results = test_function()
        all_results['passed'] += results['passed']
        all_results['failed'] += results['failed']
        all_results['errors'].extend(results['errors'])
        
        print(f"   📊 {suite_name} Results: {results['passed']} passed, {results['failed']} failed")
    
    # Final summary
    print(f"\n" + "=" * 80)
    print(f"🎯 DATABASE SYNC TEST RESULTS")
    print(f"=" * 80)
    print(f"✅ Total Passed: {all_results['passed']}")
    print(f"❌ Total Failed: {all_results['failed']}")
    
    if all_results['passed'] + all_results['failed'] > 0:
        success_rate = (all_results['passed']/(all_results['passed']+all_results['failed']))*100
        print(f"📊 Success Rate: {success_rate:.1f}%")
    
    if all_results['errors']:
        print(f"\n🔍 Issues Found:")
        for i, error in enumerate(all_results['errors'], 1):
            print(f"   {i}. {error}")
    
    # Database sync status
    if all_results['failed'] == 0:
        print(f"\n🎉 DATABASE SYNC STATUS: ✅ WORKING CORRECTLY")
        print(f"   All database synchronization is working properly.")
    elif all_results['failed'] <= 3:
        print(f"\n⚠️  DATABASE SYNC STATUS: 🟡 NEEDS ATTENTION")
        print(f"   Some database synchronization issues found.")
    else:
        print(f"\n❌ DATABASE SYNC STATUS: 🔴 CRITICAL ISSUES")
        print(f"   Major database synchronization problems detected.")
    
    return all_results['failed'] <= 3

if __name__ == '__main__':
    try:
        success = main()
        sys.exit(0 if success else 1)
    except Exception as e:
        print(f"❌ Database sync test execution failed: {str(e)}")
        sys.exit(1)