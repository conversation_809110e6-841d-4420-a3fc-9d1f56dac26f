{% extends 'base.html' %}

{% block title %}Edit {{ staff_member.user.get_full_name }} - Nurse{% endblock %}

{% block content %}
<div class="space-y-6">
    <!-- Header -->
    <div class="flex items-center justify-between">
        <div>
            <h1 class="text-3xl font-bold text-gray-900">Edit Nurse</h1>
            <p class="text-gray-600 mt-2">Update {{ staff_member.user.get_full_name }}'s profile</p>
        </div>
        <div class="flex space-x-3">
            <a href="{% url 'staff:detail' staff_member.id %}" class="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
                <i class="fas fa-arrow-left mr-2"></i>
                Back to Details
            </a>
        </div>
    </div>

    <!-- Edit Form -->
    <div class="bg-white shadow rounded-lg p-6">
        <form method="post" class="space-y-6">
            {% csrf_token %}
            
            <!-- Personal Information -->
            <div>
                <h2 class="text-lg font-medium text-gray-900 mb-4">Personal Information</h2>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">
                            First Name <span class="text-red-500">*</span>
                        </label>
                        <input type="text" name="first_name" value="{{ staff_member.user.first_name }}" required class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500">
                    </div>
                    
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">
                            Last Name <span class="text-red-500">*</span>
                        </label>
                        <input type="text" name="last_name" value="{{ staff_member.user.last_name }}" required class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500">
                    </div>
                    
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">
                            Email <span class="text-red-500">*</span>
                        </label>
                        <input type="email" name="email" value="{{ staff_member.user.email }}" required class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500">
                    </div>
                    
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">
                            Employee ID <span class="text-red-500">*</span>
                        </label>
                        <input type="text" name="employee_id" value="{{ staff_member.employee_id }}" required class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500">
                    </div>
                </div>
            </div>

            <!-- Employment Information -->
            <div>
                <h2 class="text-lg font-medium text-gray-900 mb-4">Employment Information</h2>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">
                            Department <span class="text-red-500">*</span>
                        </label>
                        <select name="department" required class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500 bg-white">
                            <option value="">Select Department</option>
                            {% for dept in departments %}
                            <option value="{{ dept.id }}" {% if dept.id == staff_member.department.id %}selected{% endif %}>{{ dept.name }}</option>
                            {% endfor %}
                        </select>
                    </div>
                    
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">
                            Designation
                        </label>
                        <input type="hidden" name="designation" value="nurse">
                        <input type="text" value="Nurse" disabled class="w-full border border-gray-300 rounded-md px-3 py-2 bg-gray-100 text-gray-700">
                    </div>
                    
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">
                            Employment Type <span class="text-red-500">*</span>
                        </label>
                        <select name="employment_type" required class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500 bg-white">
                            <option value="">Select Employment Type</option>
                            <option value="full_time" {% if staff_member.employment_type == 'full_time' %}selected{% endif %}>Full Time</option>
                            <option value="part_time" {% if staff_member.employment_type == 'part_time' %}selected{% endif %}>Part Time</option>
                            <option value="contract" {% if staff_member.employment_type == 'contract' %}selected{% endif %}>Contract</option>
                        </select>
                    </div>
                    
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">
                            Hire Date <span class="text-red-500">*</span>
                        </label>
                        <input type="date" name="hire_date" value="{{ staff_member.hire_date|date:'Y-m-d' }}" required class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500">
                    </div>
                    
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">
                            Salary
                        </label>
                        <input type="number" name="salary" value="{{ staff_member.salary }}" min="0" step="0.01" class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500">
                    </div>
                    
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">
                            Experience (Years)
                        </label>
                        <input type="number" name="experience_years" value="{{ staff_member.experience_years }}" min="0" class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500">
                    </div>
                </div>
            </div>

            <!-- Nursing Specific Information -->
            <div>
                <h2 class="text-lg font-medium text-gray-900 mb-4">Nursing Information</h2>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">
                            Shift <span class="text-red-500">*</span>
                        </label>
                        <select name="shift" required class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500 bg-white">
                            <option value="">Select Shift</option>
                            <option value="morning" {% if staff_member.user.nurse.shift == 'morning' %}selected{% endif %}>Morning (6 AM - 2 PM)</option>
                            <option value="evening" {% if staff_member.user.nurse.shift == 'evening' %}selected{% endif %}>Evening (2 PM - 10 PM)</option>
                            <option value="night" {% if staff_member.user.nurse.shift == 'night' %}selected{% endif %}>Night (10 PM - 6 AM)</option>
                        </select>
                    </div>
                    
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">
                            License Number <span class="text-red-500">*</span>
                        </label>
                        <input type="text" name="license_number" value="{{ staff_member.user.nurse.license_number }}" required class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500">
                    </div>
                </div>
            </div>

            <!-- Emergency Contact -->
            <div>
                <h2 class="text-lg font-medium text-gray-900 mb-4">Emergency Contact</h2>
                <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">
                            Contact Name
                        </label>
                        <input type="text" name="emergency_contact_name" value="{{ staff_member.emergency_contact_name }}" class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500">
                    </div>
                    
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">
                            Contact Phone
                        </label>
                        <input type="tel" name="emergency_contact_phone" value="{{ staff_member.emergency_contact_phone }}" class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500">
                    </div>
                    
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">
                            Relationship
                        </label>
                        <input type="text" name="emergency_contact_relation" value="{{ staff_member.emergency_contact_relation }}" class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500">
                    </div>
                </div>
            </div>

            <!-- Qualifications -->
            <div>
                <h2 class="text-lg font-medium text-gray-900 mb-4">Qualifications</h2>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">
                        Education & Certifications
                    </label>
                    <textarea name="qualifications" rows="3" class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500 resize-vertical">{{ staff_member.qualifications }}</textarea>
                </div>
            </div>

            <!-- Account Status -->
            <div>
                <h2 class="text-lg font-medium text-gray-900 mb-4">Account Status</h2>
                <div class="flex items-center">
                    <input type="checkbox" name="is_active" id="is_active" {% if staff_member.is_active %}checked{% endif %} class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
                    <label for="is_active" class="ml-2 block text-sm text-gray-900">
                        Active Employee
                    </label>
                </div>
            </div>

            <!-- Form Actions -->
            <div class="flex justify-end space-x-3 pt-6 border-t border-gray-200">
                <a href="{% url 'staff:detail' staff_member.id %}" class="px-6 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
                    Cancel
                </a>
                <button type="submit" class="px-6 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700">
                    <i class="fas fa-save mr-2"></i>
                    Update Nurse
                </button>
            </div>
        </form>
    </div>
</div>
{% endblock %}
