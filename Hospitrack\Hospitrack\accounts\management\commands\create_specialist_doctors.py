from django.core.management.base import BaseCommand
from django.contrib.auth import get_user_model
from accounts.models import Doctor

User = get_user_model()

class Command(BaseCommand):
    help = 'Create 10 specialist doctors for the hospital'

    def handle(self, *args, **options):
        self.stdout.write('Creating specialist doctors...')
        
        doctors_data = [
            {
                'username': 'dr.cardiologist',
                'email': '<EMAIL>',
                'first_name': '<PERSON>',
                'last_name': '<PERSON>',
                'specialization': 'Cardiologist',
                'department': 'Cardiology',
                'license_number': 'CARD001',
                'experience_years': 12,
                'consultation_fee': 800.00,
                'qualifications': 'MBBS, MD Cardiology, Fellowship in Interventional Cardiology'
            },
            {
                'username': 'dr.nephrologist',
                'email': '<EMAIL>',
                'first_name': '<PERSON>',
                'last_name': '<PERSON>',
                'specialization': 'Nephrologist',
                'department': 'Nephrology',
                'license_number': 'NEPH001',
                'experience_years': 10,
                'consultation_fee': 750.00,
                'qualifications': 'MB<PERSON>, MD Internal Medicine, DM Nephrology'
            },
            {
                'username': 'dr.pulmonologist',
                'email': '<EMAIL>',
                'first_name': 'Emily',
                'last_name': 'Rodriguez',
                'specialization': 'Pulmonologist',
                'department': 'Pulmonology',
                'license_number': 'PULM001',
                'experience_years': 8,
                'consultation_fee': 700.00,
                'qualifications': 'MBBS, MD Pulmonary Medicine, Fellowship in Critical Care'
            },
            {
                'username': 'dr.hepatologist',
                'email': '<EMAIL>',
                'first_name': 'David',
                'last_name': 'Kumar',
                'specialization': 'Hepatologist',
                'department': 'Gastroenterology',
                'license_number': 'HEPT001',
                'experience_years': 15,
                'consultation_fee': 850.00,
                'qualifications': 'MBBS, MD Gastroenterology, DM Hepatology'
            },
            {
                'username': 'dr.orthopedist',
                'email': '<EMAIL>',
                'first_name': 'James',
                'last_name': 'Wilson',
                'specialization': 'Orthopedist',
                'department': 'Orthopedics',
                'license_number': 'ORTH001',
                'experience_years': 14,
                'consultation_fee': 900.00,
                'qualifications': 'MBBS, MS Orthopedics, Fellowship in Joint Replacement'
            },
            {
                'username': 'dr.pediatrician',
                'email': '<EMAIL>',
                'first_name': 'Lisa',
                'last_name': 'Thompson',
                'specialization': 'Pediatrician',
                'department': 'Pediatrics',
                'license_number': 'PEDI001',
                'experience_years': 9,
                'consultation_fee': 600.00,
                'qualifications': 'MBBS, MD Pediatrics, Fellowship in Pediatric Cardiology'
            },
            {
                'username': 'dr.ent',
                'email': '<EMAIL>',
                'first_name': 'Robert',
                'last_name': 'Anderson',
                'specialization': 'ENT Specialist',
                'department': 'ENT',
                'license_number': 'ENT001',
                'experience_years': 11,
                'consultation_fee': 650.00,
                'qualifications': 'MBBS, MS ENT, Fellowship in Head and Neck Surgery'
            },
            {
                'username': 'dr.neurosurgeon',
                'email': '<EMAIL>',
                'first_name': 'Maria',
                'last_name': 'Garcia',
                'specialization': 'Neurosurgeon',
                'department': 'Neurosurgery',
                'license_number': 'NEUR001',
                'experience_years': 16,
                'consultation_fee': 1200.00,
                'qualifications': 'MBBS, MS Neurosurgery, Fellowship in Spine Surgery'
            },
            {
                'username': 'dr.neurologist',
                'email': '<EMAIL>',
                'first_name': 'Thomas',
                'last_name': 'Brown',
                'specialization': 'Neurologist',
                'department': 'Neurology',
                'license_number': 'NEURO001',
                'experience_years': 13,
                'consultation_fee': 800.00,
                'qualifications': 'MBBS, MD Neurology, DM Neurology'
            },
            {
                'username': 'dr.psychiatrist',
                'email': '<EMAIL>',
                'first_name': 'Jennifer',
                'last_name': 'Davis',
                'specialization': 'Psychiatrist',
                'department': 'Psychiatry',
                'license_number': 'PSYC001',
                'experience_years': 7,
                'consultation_fee': 700.00,
                'qualifications': 'MBBS, MD Psychiatry, Fellowship in Child Psychiatry'
            }
        ]
        
        created_count = 0
        for doctor_data in doctors_data:
            try:
                # Check if user already exists
                if User.objects.filter(username=doctor_data['username']).exists():
                    self.stdout.write(f'- Doctor {doctor_data["username"]} already exists')
                    continue
                
                # Create user account
                user = User.objects.create_user(
                    username=doctor_data['username'],
                    email=doctor_data['email'],
                    password='doctor123',
                    first_name=doctor_data['first_name'],
                    last_name=doctor_data['last_name'],
                    user_type='doctor'
                )
                
                # Create doctor profile
                doctor = Doctor.objects.create(
                    user=user,
                    specialization=doctor_data['specialization'],
                    license_number=doctor_data['license_number'],
                    department=doctor_data['department'],
                    experience_years=doctor_data['experience_years'],
                    consultation_fee=doctor_data['consultation_fee'],
                    qualifications=doctor_data['qualifications'],
                    phone=f'98765{created_count:05d}'
                )
                
                created_count += 1
                self.stdout.write(f'✓ Created: Dr. {user.get_full_name()} - {doctor.specialization}')
                
            except Exception as e:
                self.stdout.write(f'✗ Error creating {doctor_data["username"]}: {str(e)}')
        
        self.stdout.write(f'\n✅ Created {created_count} specialist doctors.')
        self.stdout.write(f'📊 Total doctors: {Doctor.objects.count()}')
        self.stdout.write(f'🔑 All doctors password: doctor123')
