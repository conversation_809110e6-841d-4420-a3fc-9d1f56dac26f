from django.shortcuts import render, get_object_or_404
from django.contrib.auth.decorators import login_required
from django.http import JsonResponse
from django.db.models import Sum, Avg, Count, Q
from django.utils import timezone
from datetime import datetime, timedelta, date
import json

from .models import (
    AnalyticsMetric, PatientFlowMetrics, StaffEfficiencyMetrics,
    BedUtilizationMetrics, FinancialMetrics, QualityMetrics,
    PredictiveModel, Alert
)
from .services import AnalyticsDataCollector, AnalyticsCalculator
from accounts.models import MedicalSpecialty
from beds.models import Ward

@login_required
def analytics_dashboard(request):
    """Main analytics dashboard"""
    # Get date range from request or default to last 30 days
    end_date = date.today()
    start_date = end_date - timedelta(days=30)
    
    if request.GET.get('start_date'):
        start_date = datetime.strptime(request.GET.get('start_date'), '%Y-%m-%d').date()
    if request.GET.get('end_date'):
        end_date = datetime.strptime(request.GET.get('end_date'), '%Y-%m-%d').date()
    
    # Calculate KPIs
    calculator = AnalyticsCalculator()
    kpis = calculator.calculate_kpis(end_date)
    
    # Get recent alerts
    recent_alerts = Alert.objects.filter(
        is_resolved=False
    ).order_by('-triggered_at')[:10]
    
    # Get trend data for charts
    patient_flow_trend = PatientFlowMetrics.objects.filter(
        date__gte=start_date,
        date__lte=end_date,
        department=None
    ).order_by('date')
    
    financial_trend = FinancialMetrics.objects.filter(
        date__gte=start_date,
        date__lte=end_date,
        department=None
    ).order_by('date')
    
    # Prepare chart data
    chart_data = {
        'patient_flow': {
            'dates': [pf.date.strftime('%Y-%m-%d') for pf in patient_flow_trend],
            'admissions': [pf.total_admissions for pf in patient_flow_trend],
            'discharges': [pf.total_discharges for pf in patient_flow_trend],
            'occupancy': [float(pf.occupancy_rate) for pf in patient_flow_trend],
        },
        'financial': {
            'dates': [fm.date.strftime('%Y-%m-%d') for fm in financial_trend],
            'revenue': [float(fm.total_revenue) for fm in financial_trend],
            'costs': [float(fm.total_costs) for fm in financial_trend],
            'profit': [float(fm.net_profit) for fm in financial_trend],
        }
    }
    
    context = {
        'kpis': kpis,
        'recent_alerts': recent_alerts,
        'chart_data': json.dumps(chart_data),
        'start_date': start_date,
        'end_date': end_date,
        'specialties': MedicalSpecialty.objects.all(),
        'wards': Ward.objects.filter(is_active=True),
    }
    
    return render(request, 'analytics/dashboard.html', context)

@login_required
def patient_flow_analytics(request):
    """Patient flow analytics page"""
    # Get date range
    end_date = date.today()
    start_date = end_date - timedelta(days=30)
    
    if request.GET.get('start_date'):
        start_date = datetime.strptime(request.GET.get('start_date'), '%Y-%m-%d').date()
    if request.GET.get('end_date'):
        end_date = datetime.strptime(request.GET.get('end_date'), '%Y-%m-%d').date()
    
    # Get specialty filter
    specialty_id = request.GET.get('specialty')
    specialty = None
    if specialty_id:
        specialty = get_object_or_404(MedicalSpecialty, id=specialty_id)
    
    # Get patient flow data
    flow_data = PatientFlowMetrics.objects.filter(
        date__gte=start_date,
        date__lte=end_date
    )
    
    if specialty:
        flow_data = flow_data.filter(specialty=specialty)
    else:
        flow_data = flow_data.filter(department=None)  # Overall metrics
    
    flow_data = flow_data.order_by('date')
    
    # Calculate summary statistics
    total_admissions = sum(fd.total_admissions for fd in flow_data)
    total_discharges = sum(fd.total_discharges for fd in flow_data)
    avg_occupancy = flow_data.aggregate(avg=Avg('occupancy_rate'))['avg'] or 0
    avg_los = flow_data.aggregate(avg=Avg('average_length_of_stay'))['avg'] or 0
    
    # Prepare chart data
    chart_data = {
        'dates': [fd.date.strftime('%Y-%m-%d') for fd in flow_data],
        'admissions': [fd.total_admissions for fd in flow_data],
        'discharges': [fd.total_discharges for fd in flow_data],
        'emergency_admissions': [fd.emergency_admissions for fd in flow_data],
        'planned_admissions': [fd.planned_admissions for fd in flow_data],
        'occupancy_rate': [float(fd.occupancy_rate) for fd in flow_data],
    }
    
    context = {
        'flow_data': flow_data,
        'chart_data': json.dumps(chart_data),
        'total_admissions': total_admissions,
        'total_discharges': total_discharges,
        'avg_occupancy': round(float(avg_occupancy), 2),
        'avg_los': round(float(avg_los), 2),
        'start_date': start_date,
        'end_date': end_date,
        'specialty': specialty,
        'specialties': MedicalSpecialty.objects.all(),
    }
    
    return render(request, 'analytics/patient_flow.html', context)

@login_required
def financial_analytics(request):
    """Financial analytics page"""
    # Get date range
    end_date = date.today()
    start_date = end_date - timedelta(days=30)
    
    if request.GET.get('start_date'):
        start_date = datetime.strptime(request.GET.get('start_date'), '%Y-%m-%d').date()
    if request.GET.get('end_date'):
        end_date = datetime.strptime(request.GET.get('end_date'), '%Y-%m-%d').date()
    
    # Get financial data
    financial_data = FinancialMetrics.objects.filter(
        date__gte=start_date,
        date__lte=end_date,
        department=None  # Overall metrics
    ).order_by('date')
    
    # Calculate totals
    totals = financial_data.aggregate(
        total_revenue=Sum('total_revenue'),
        total_costs=Sum('total_costs'),
        total_profit=Sum('net_profit'),
        consultation_revenue=Sum('consultation_revenue'),
        bed_revenue=Sum('bed_revenue'),
        procedure_revenue=Sum('procedure_revenue'),
    )
    
    # Calculate average profit margin
    avg_profit_margin = financial_data.aggregate(avg=Avg('profit_margin'))['avg'] or 0
    
    # Prepare chart data
    chart_data = {
        'dates': [fd.date.strftime('%Y-%m-%d') for fd in financial_data],
        'revenue': [float(fd.total_revenue) for fd in financial_data],
        'costs': [float(fd.total_costs) for fd in financial_data],
        'profit': [float(fd.net_profit) for fd in financial_data],
        'consultation_revenue': [float(fd.consultation_revenue) for fd in financial_data],
        'bed_revenue': [float(fd.bed_revenue) for fd in financial_data],
        'procedure_revenue': [float(fd.procedure_revenue) for fd in financial_data],
    }
    
    # Revenue breakdown for pie chart
    revenue_breakdown = {
        'consultation': float(totals['consultation_revenue'] or 0),
        'bed': float(totals['bed_revenue'] or 0),
        'procedure': float(totals['procedure_revenue'] or 0),
    }
    
    context = {
        'financial_data': financial_data,
        'chart_data': json.dumps(chart_data),
        'revenue_breakdown': json.dumps(revenue_breakdown),
        'totals': totals,
        'avg_profit_margin': round(float(avg_profit_margin), 2),
        'start_date': start_date,
        'end_date': end_date,
    }
    
    return render(request, 'analytics/financial.html', context)

@login_required
def bed_utilization_analytics(request):
    """Bed utilization analytics page"""
    # Get date range
    end_date = date.today()
    start_date = end_date - timedelta(days=30)
    
    if request.GET.get('start_date'):
        start_date = datetime.strptime(request.GET.get('start_date'), '%Y-%m-%d').date()
    if request.GET.get('end_date'):
        end_date = datetime.strptime(request.GET.get('end_date'), '%Y-%m-%d').date()
    
    # Get ward filter
    ward_id = request.GET.get('ward')
    ward = None
    if ward_id:
        ward = get_object_or_404(Ward, id=ward_id)
    
    # Get bed utilization data
    if ward:
        bed_data = BedUtilizationMetrics.objects.filter(
            date__gte=start_date,
            date__lte=end_date,
            ward=ward
        ).order_by('date')
    else:
        # Aggregate data across all wards
        bed_data = BedUtilizationMetrics.objects.filter(
            date__gte=start_date,
            date__lte=end_date
        ).values('date').annotate(
            total_beds=Sum('total_beds'),
            occupied_beds=Sum('occupied_beds'),
            available_beds=Sum('available_beds'),
            total_revenue=Sum('total_revenue'),
        ).order_by('date')
    
    # Calculate summary statistics
    if ward:
        avg_utilization = bed_data.aggregate(avg=Avg('utilization_rate'))['avg'] or 0
        total_revenue = bed_data.aggregate(sum=Sum('total_revenue'))['sum'] or 0
    else:
        # Calculate utilization for aggregated data
        avg_utilization = 0
        total_revenue = 0
        for bd in bed_data:
            if bd['total_beds'] > 0:
                utilization = (bd['occupied_beds'] / bd['total_beds']) * 100
                avg_utilization += utilization
            total_revenue += bd['total_revenue'] or 0
        
        if bed_data:
            avg_utilization = avg_utilization / len(bed_data)
    
    # Prepare chart data
    if ward:
        chart_data = {
            'dates': [bd.date.strftime('%Y-%m-%d') for bd in bed_data],
            'utilization_rate': [float(bd.utilization_rate) for bd in bed_data],
            'occupied_beds': [bd.occupied_beds for bd in bed_data],
            'available_beds': [bd.available_beds for bd in bed_data],
            'revenue': [float(bd.total_revenue) for bd in bed_data],
        }
    else:
        chart_data = {
            'dates': [bd['date'].strftime('%Y-%m-%d') for bd in bed_data],
            'occupied_beds': [bd['occupied_beds'] for bd in bed_data],
            'available_beds': [bd['available_beds'] for bd in bed_data],
            'revenue': [float(bd['total_revenue'] or 0) for bd in bed_data],
        }
    
    context = {
        'bed_data': bed_data,
        'chart_data': json.dumps(chart_data),
        'avg_utilization': round(float(avg_utilization), 2),
        'total_revenue': total_revenue,
        'start_date': start_date,
        'end_date': end_date,
        'ward': ward,
        'wards': Ward.objects.filter(is_active=True),
    }
    
    return render(request, 'analytics/bed_utilization.html', context)

@login_required
def staff_efficiency_analytics(request):
    """Staff efficiency analytics page"""
    # Get date range
    end_date = date.today()
    start_date = end_date - timedelta(days=30)
    
    if request.GET.get('start_date'):
        start_date = datetime.strptime(request.GET.get('start_date'), '%Y-%m-%d').date()
    if request.GET.get('end_date'):
        end_date = datetime.strptime(request.GET.get('end_date'), '%Y-%m-%d').date()
    
    # Get staff efficiency data
    staff_data = StaffEfficiencyMetrics.objects.filter(
        date__gte=start_date,
        date__lte=end_date
    ).select_related('staff_member')
    
    # Aggregate by staff member
    staff_summary = staff_data.values(
        'staff_member__first_name',
        'staff_member__last_name',
        'staff_member__user_type'
    ).annotate(
        total_patients=Sum('patients_seen'),
        total_appointments=Sum('appointments_completed'),
        total_revenue=Sum('revenue_generated'),
        avg_consultation_time=Avg('average_consultation_time'),
        avg_satisfaction=Avg('patient_satisfaction_score'),
    ).order_by('-total_revenue')
    
    context = {
        'staff_data': staff_data,
        'staff_summary': staff_summary,
        'start_date': start_date,
        'end_date': end_date,
    }
    
    return render(request, 'analytics/staff_efficiency.html', context)

@login_required
def quality_analytics(request):
    """Quality metrics analytics page"""
    # Get date range
    end_date = date.today()
    start_date = end_date - timedelta(days=30)
    
    if request.GET.get('start_date'):
        start_date = datetime.strptime(request.GET.get('start_date'), '%Y-%m-%d').date()
    if request.GET.get('end_date'):
        end_date = datetime.strptime(request.GET.get('end_date'), '%Y-%m-%d').date()
    
    # Get quality data
    quality_data = QualityMetrics.objects.filter(
        date__gte=start_date,
        date__lte=end_date,
        department=None  # Overall metrics
    ).order_by('date')
    
    # Calculate averages
    averages = quality_data.aggregate(
        avg_satisfaction=Avg('average_satisfaction_score'),
        avg_readmission_rate=Avg('readmission_rate'),
        avg_wait_time=Avg('average_wait_time'),
        avg_punctuality=Avg('appointment_punctuality'),
        total_incidents=Sum('incident_count'),
    )
    
    # Prepare chart data
    chart_data = {
        'dates': [qd.date.strftime('%Y-%m-%d') for qd in quality_data],
        'satisfaction': [float(qd.average_satisfaction_score) for qd in quality_data],
        'readmission_rate': [float(qd.readmission_rate) for qd in quality_data],
        'wait_time': [qd.average_wait_time for qd in quality_data],
        'punctuality': [float(qd.appointment_punctuality) for qd in quality_data],
    }
    
    context = {
        'quality_data': quality_data,
        'chart_data': json.dumps(chart_data),
        'averages': averages,
        'start_date': start_date,
        'end_date': end_date,
    }
    
    return render(request, 'analytics/quality.html', context)

@login_required
def collect_analytics_data(request):
    """Manual trigger for analytics data collection"""
    if request.method == 'POST':
        target_date = request.POST.get('date')
        if target_date:
            target_date = datetime.strptime(target_date, '%Y-%m-%d').date()
        else:
            target_date = date.today()
        
        collector = AnalyticsDataCollector()
        collector.collect_daily_metrics(target_date)
        
        return JsonResponse({
            'success': True,
            'message': f'Analytics data collected for {target_date}'
        })
    
    return JsonResponse({'success': False, 'message': 'Invalid request method'})
