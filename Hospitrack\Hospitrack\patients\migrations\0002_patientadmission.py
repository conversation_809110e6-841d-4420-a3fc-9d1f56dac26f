# Generated by Django 5.2.4 on 2025-07-26 14:09

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('accounts', '0001_initial'),
        ('patients', '0001_initial'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='PatientAdmission',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('admission_id', models.CharField(max_length=20, unique=True)),
                ('admission_date', models.DateTimeField()),
                ('admission_type', models.CharField(choices=[('emergency', 'Emergency'), ('planned', 'Planned'), ('transfer', 'Transfer'), ('observation', 'Observation')], default='planned', max_length=15)),
                ('reason_for_admission', models.TextField()),
                ('diagnosis', models.TextField()),
                ('symptoms', models.TextField(blank=True, null=True)),
                ('medical_history', models.TextField(blank=True, null=True)),
                ('allergies', models.TextField(blank=True, null=True)),
                ('current_medications', models.TextField(blank=True, null=True)),
                ('status', models.CharField(choices=[('admitted', 'Admitted'), ('discharged', 'Discharged'), ('transferred', 'Transferred')], default='admitted', max_length=15)),
                ('expected_discharge_date', models.DateField(blank=True, null=True)),
                ('actual_discharge_date', models.DateTimeField(blank=True, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('admitted_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to=settings.AUTH_USER_MODEL)),
                ('doctor', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='patient_admissions', to='accounts.doctor')),
                ('patient', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='admissions', to='patients.patient')),
            ],
            options={
                'ordering': ['-admission_date'],
            },
        ),
    ]
