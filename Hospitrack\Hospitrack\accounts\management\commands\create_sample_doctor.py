from django.core.management.base import BaseCommand
from django.contrib.auth import get_user_model
from accounts.models import Doctor

User = get_user_model()

class Command(BaseCommand):
    help = 'Create a sample doctor account for testing'

    def handle(self, *args, **options):
        self.stdout.write('Creating sample doctor account...')
        
        try:
            # Check if doctor already exists
            if User.objects.filter(username='doctor').exists():
                self.stdout.write(self.style.WARNING('Doctor account already exists!'))
                doctor_user = User.objects.get(username='doctor')
                if hasattr(doctor_user, 'doctor'):
                    self.stdout.write(self.style.SUCCESS('Doctor profile already exists.'))
                else:
                    # Create doctor profile for existing user
                    doctor = Doctor.objects.create(
                        user=doctor_user,
                        specialization='General Medicine',
                        license_number='MD123456',
                        department='General Medicine',
                        phone='**********',
                        experience_years=5,
                        consultation_fee=500.00,
                        qualifications='MBBS from Medical College, MD in General Medicine'
                    )
                    self.stdout.write(self.style.SUCCESS('Doctor profile created for existing user.'))
                return
            
            # Create doctor user account
            doctor_user = User.objects.create_user(
                username='doctor',
                email='<EMAIL>',
                password='doctor123',
                first_name='John',
                last_name='Smith',
                user_type='doctor'
            )
            
            # Create doctor profile
            doctor = Doctor.objects.create(
                user=doctor_user,
                specialization='General Medicine',
                license_number='MD123456',
                department='General Medicine',
                phone='**********',
                experience_years=5,
                consultation_fee=500.00,
                qualifications='MBBS from Medical College, MD in General Medicine'
            )
            
            self.stdout.write(self.style.SUCCESS('Successfully created sample doctor account!'))
            self.stdout.write(f'Username: doctor')
            self.stdout.write(f'Password: doctor123')
            self.stdout.write(f'Name: Dr. {doctor_user.get_full_name()}')
            self.stdout.write(f'Specialization: {doctor.specialization}')
            
        except Exception as e:
            self.stdout.write(self.style.ERROR(f'Error creating doctor: {str(e)}'))
