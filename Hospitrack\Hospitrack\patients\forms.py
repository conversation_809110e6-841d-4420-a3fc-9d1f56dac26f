from django import forms
from .models import Patient, PatientUpdate, PatientDischarge
from accounts.models import MedicalSpecialty

class PatientForm(forms.ModelForm):
    class Meta:
        model = Patient
        fields = [
            'patient_id', 'first_name', 'last_name', 'date_of_birth', 'gender',
            'blood_group', 'phone', 'email', 'address', 'emergency_contact_name',
            'emergency_contact_phone', 'emergency_contact_relation', 'medical_history',
            'allergies', 'current_medications', 'insurance_number', 'primary_specialty'
        ]
        widgets = {
            'date_of_birth': forms.DateInput(attrs={
                'type': 'date',
                'class': 'w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500'
            }),
            'address': forms.Textarea(attrs={
                'rows': 3,
                'class': 'w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500 resize-vertical',
                'placeholder': 'Enter complete address with city, state, and postal code'
            }),
            'medical_history': forms.Textarea(attrs={
                'rows': 3,
                'class': 'w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500 resize-vertical',
                'placeholder': 'Previous medical conditions, surgeries, chronic illnesses...'
            }),
            'allergies': forms.Textarea(attrs={
                'rows': 2,
                'class': 'w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500 resize-vertical',
                'placeholder': 'Known allergies to medications, foods, or substances'
            }),
            'current_medications': forms.Textarea(attrs={
                'rows': 2,
                'class': 'w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500 resize-vertical',
                'placeholder': 'Current medications with dosage and frequency'
            }),
        }

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        # Apply Tailwind CSS classes to all form fields
        for field_name, field in self.fields.items():
            if isinstance(field.widget, forms.Textarea):
                field.widget.attrs.update({
                    'class': 'w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500 resize-vertical'
                })
            elif isinstance(field.widget, forms.Select):
                field.widget.attrs.update({
                    'class': 'w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500 bg-white'
                })
            else:
                field.widget.attrs.update({
                    'class': 'w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500'
                })

class PatientUpdateForm(forms.ModelForm):
    class Meta:
        model = PatientUpdate
        fields = ['update_text', 'vital_signs', 'medications_given', 'notes']
        widgets = {
            'update_text': forms.Textarea(attrs={
                'rows': 4,
                'class': 'w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500 resize-vertical',
                'placeholder': 'Describe the patient\'s current condition, treatment progress, or any observations...'
            }),
            'vital_signs': forms.Textarea(attrs={
                'rows': 3,
                'class': 'w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500 resize-vertical',
                'placeholder': 'BP: 120/80, Temp: 98.6°F, Pulse: 72, Resp: 16, O2 Sat: 98%'
            }),
            'medications_given': forms.Textarea(attrs={
                'rows': 3,
                'class': 'w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500 resize-vertical',
                'placeholder': 'List medications administered with dosage and time (e.g., Paracetamol 500mg at 2:00 PM)'
            }),
            'notes': forms.Textarea(attrs={
                'rows': 3,
                'class': 'w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500 resize-vertical',
                'placeholder': 'Additional observations, patient complaints, or care instructions...'
            }),
        }

class PatientDischargeForm(forms.ModelForm):
    class Meta:
        model = PatientDischarge
        fields = [
            'discharge_date', 'discharge_summary', 'final_diagnosis',
            'treatment_given', 'medications_prescribed', 'follow_up_instructions',
            'follow_up_date'
        ]
        widgets = {
            'discharge_date': forms.DateTimeInput(attrs={
                'type': 'datetime-local',
                'class': 'w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500'
            }),
            'follow_up_date': forms.DateInput(attrs={
                'type': 'date',
                'class': 'w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500'
            }),
            'discharge_summary': forms.Textarea(attrs={
                'rows': 4,
                'class': 'w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500 resize-vertical',
                'placeholder': 'Summary of patient\'s hospital stay, treatment received, and current condition...'
            }),
            'final_diagnosis': forms.Textarea(attrs={
                'rows': 3,
                'class': 'w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500 resize-vertical',
                'placeholder': 'Final diagnosis and medical conclusions...'
            }),
            'treatment_given': forms.Textarea(attrs={
                'rows': 3,
                'class': 'w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500 resize-vertical',
                'placeholder': 'Treatments, procedures, and interventions provided...'
            }),
            'medications_prescribed': forms.Textarea(attrs={
                'rows': 2,
                'class': 'w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500 resize-vertical',
                'placeholder': 'Medications prescribed for home use with dosage and instructions...'
            }),
            'follow_up_instructions': forms.Textarea(attrs={
                'rows': 3,
                'class': 'w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500 resize-vertical',
                'placeholder': 'Follow-up care instructions, lifestyle recommendations, warning signs...'
            }),
        }
